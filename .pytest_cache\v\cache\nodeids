["tests/test_dialog_integration.py::TestAreaEffectMaskDialog::test_area_dialog_initialization", "tests/test_dialog_integration.py::TestDialogDataFlow::test_ability_editor_to_range_dialog_flow", "tests/test_dialog_integration.py::TestDialogDataFlow::test_piece_editor_to_pattern_dialog_flow", "tests/test_dialog_integration.py::TestDialogValidation::test_dialog_cancel_handling", "tests/test_dialog_integration.py::TestDialogValidation::test_pattern_dialog_validation", "tests/test_dialog_integration.py::TestPatternEditorDialog::test_pattern_data_handling", "tests/test_dialog_integration.py::TestPatternEditorDialog::test_pattern_dialog_initialization", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_ability_selection", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_dialog_initialization", "tests/test_dialog_integration.py::TestPieceAbilityManagerDialog::test_dialog_integration_with_piece_editor", "tests/test_dialog_integration.py::TestRangeEditorDialog::test_range_dialog_initialization", "tests/test_dialog_integration.py::TestRangeEditorDialog::test_range_mask_handling", "tests/test_enhanced_cache_manager.py::TestCacheIntegratedDataManager::test_cache_manager_integration", "tests/test_enhanced_cache_manager.py::TestCacheIntegratedDataManager::test_error_logging", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_basic_cache_operations", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_cache_clear", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_cache_statistics", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_deleted_file_cleanup", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_file_invalidation", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_lru_eviction", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_manual_invalidation", "tests/test_enhanced_cache_manager.py::TestEnhancedCacheManager::test_memory_monitoring", "tests/test_enhanced_error_handling.py::test_basic_error_handling", "tests/test_enhanced_error_handling.py::test_enhanced_data_manager", "tests/test_enhanced_error_handling.py::test_error_recovery_suggestions", "tests/test_enhanced_error_handling.py::test_error_summary", "tests/test_enhanced_error_handling.py::test_file_operation_error_handling", "tests/test_enhanced_error_handling.py::test_filename_cleaning", "tests/test_enhanced_error_handling.py::test_safe_operations", "tests/test_error_message_improvements.py::TestContextualHelpProvider::test_get_help_content", "tests/test_error_message_improvements.py::TestContextualHelpProvider::test_get_related_help", "tests/test_error_message_improvements.py::TestContextualHelpProvider::test_help_content_structure", "tests/test_error_message_improvements.py::TestErrorMessageTranslator::test_file_not_found_translation", "tests/test_error_message_improvements.py::TestErrorMessageTranslator::test_generic_error_translation", "tests/test_error_message_improvements.py::TestErrorMessageTranslator::test_json_error_translation", "tests/test_error_message_improvements.py::TestErrorMessageTranslator::test_permission_error_translation", "tests/test_error_message_improvements.py::TestErrorMessageTranslator::test_validation_error_translation", "tests/test_error_message_improvements.py::TestErrorTemplates::test_all_templates_exist", "tests/test_error_message_improvements.py::TestErrorTemplates::test_template_formatting", "tests/test_error_message_improvements.py::TestErrorTemplates::test_template_with_missing_args", "tests/test_error_message_improvements.py::TestImprovedErrorHandler::test_error_context_storage", "tests/test_error_message_improvements.py::TestImprovedErrorHandler::test_quick_fix_handlers_registration", "tests/test_error_message_improvements.py::TestImprovedErrorHandler::test_reset_to_defaults", "tests/test_error_message_improvements.py::TestImprovedErrorHandler::test_retry_operation_with_load", "tests/test_error_message_improvements.py::TestImprovedErrorHandler::test_retry_operation_with_save", "tests/test_error_message_improvements.py::TestIntegrationScenarios::test_corrupted_file_scenario", "tests/test_error_message_improvements.py::TestIntegrationScenarios::test_file_permission_scenario", "tests/test_error_message_improvements.py::TestIntegrationScenarios::test_missing_file_scenario", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_directory_scanning_optimization", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_error_handling", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_file_compression", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_file_indexing", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_index_initialization", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_index_updates", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_performance_tracking", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_search_functionality", "tests/test_file_system_optimizer.py::TestFileSystemOptimizer::test_search_suggestions", "tests/test_file_system_optimizer.py::TestSearchPerformance::test_indexing_performance", "tests/test_file_system_optimizer.py::TestSearchPerformance::test_search_performance", "tests/test_lazy_loading_system.py::TestLazyIntegration::test_backward_compatibility", "tests/test_lazy_loading_system.py::TestLazyIntegration::test_file_list_managers", "tests/test_lazy_loading_system.py::TestLazyIntegration::test_lazy_ability_loading", "tests/test_lazy_loading_system.py::TestLazyIntegration::test_lazy_piece_loading", "tests/test_lazy_loading_system.py::TestLazyLoadingSystem::test_cache_integration", "tests/test_lazy_loading_system.py::TestLazyLoadingSystem::test_file_metadata_loading", "tests/test_lazy_loading_system.py::TestLazyLoadingSystem::test_lazy_data_loading", "tests/test_lazy_loading_system.py::TestLazyLoadingSystem::test_loading_status", "tests/test_lazy_loading_system.py::TestLazyLoadingSystem::test_preloading", "tests/test_main_app_integration.py::TestMainAppIntegration::test_cache_integration", "tests/test_main_app_integration.py::TestMainAppIntegration::test_data_manager_file_operations", "tests/test_main_app_integration.py::TestMainAppIntegration::test_error_handling", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_data_manager_initialization", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_loading_system_components", "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_patches_application", "tests/test_main_app_integration.py::TestMainAppIntegration::test_performance_benefits", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_creation_complete", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_creation_minimal", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_serialization", "tests/test_pydantic_models.py::TestAbilitySchema::test_ability_validation_errors", "tests/test_pydantic_models.py::TestEdgeCases::test_empty_strings_and_lists", "tests/test_pydantic_models.py::TestEdgeCases::test_large_numbers", "tests/test_pydantic_models.py::TestEdgeCases::test_unicode_and_special_characters", "tests/test_pydantic_models.py::TestModelIntegration::test_complex_movement_pattern", "tests/test_pydantic_models.py::TestModelIntegration::test_model_version_consistency", "tests/test_pydantic_models.py::TestModelIntegration::test_piece_with_abilities_references", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_creation_default", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_custom_pattern", "tests/test_pydantic_models.py::TestMovementSchema::test_movement_validation_errors", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_creation_complete", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_creation_minimal", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_serialization", "tests/test_pydantic_models.py::TestPieceSchema::test_piece_validation_errors", "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_direct_to_pydantic_compatibility", "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_pydantic_to_direct_compatibility", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_ability_save_load_cycle", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_file_corruption_handling", "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_piece_save_load_cycle", "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_concurrent_save_load", "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_large_data_save_load", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_ability_validation_workflow", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_piece_validation_workflow", "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_validation_error_handling", "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_ability_ui_workflow_simulation", "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_piece_ui_workflow_simulation", "tests/test_security_enhancements.py::TestCrashRecoveryManager::test_backup_creation", "tests/test_security_enhancements.py::TestCrashRecoveryManager::test_recovery_point_creation", "tests/test_security_enhancements.py::TestCrashRecoveryManager::test_recovery_restoration", "tests/test_security_enhancements.py::TestSecureDataManager::test_secure_save_and_load", "tests/test_security_enhancements.py::TestSecureDataManager::test_secure_save_with_dangerous_data", "tests/test_security_enhancements.py::TestSecureIntegration::test_secure_direct_data_manager", "tests/test_security_enhancements.py::TestSecureIntegration::test_secure_direct_data_manager_with_invalid_data", "tests/test_security_enhancements.py::TestSecurityValidator::test_file_size_validation", "tests/test_security_enhancements.py::TestSecurityValidator::test_filename_sanitization", "tests/test_security_enhancements.py::TestSecurityValidator::test_input_validation_security", "tests/test_security_enhancements.py::TestSecurityValidator::test_prevent_directory_traversal", "tests/test_security_enhancements.py::TestSecurityValidator::test_validate_safe_file_path", "tests/test_security_enhancements.py::TestValidationRules::test_coordinate_validation", "tests/test_security_enhancements.py::TestValidationRules::test_numeric_validation", "tests/test_security_enhancements.py::TestValidationRules::test_pattern_validation", "tests/test_security_enhancements.py::TestValidationRules::test_string_validation", "tests/test_ui_automation.py::TestAbilityEditor::test_ability_editor_initialization", "tests/test_ui_automation.py::TestAbilityEditor::test_ability_editor_tag_management", "tests/test_ui_automation.py::TestDialogIntegration::test_dialog_creation", "tests/test_ui_automation.py::TestDialogIntegration::test_dialog_execution", "tests/test_ui_automation.py::TestMainWindowIntegration::test_main_window_creation", "tests/test_ui_automation.py::TestMainWindowIntegration::test_main_window_editor_integration", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_data_collection", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_data_population", "tests/test_ui_automation.py::TestPieceEditor::test_piece_editor_initialization", "tests/test_ui_automation.py::TestUIDataIntegrity::test_ui_data_round_trip", "tests/test_ui_automation.py::TestUIDataIntegrity::test_ui_validation_feedback", "tests/test_ui_automation.py::TestUIPerformance::test_ui_responsiveness"]