"""
Workflow Optimization System for Adventure Chess Creator

This module provides comprehensive workflow improvements including:
- Undo/Redo functionality with QUndoStack
- Enhanced keyboard shortcuts
- Template system for common pieces/abilities
- Auto-save and workflow enhancements
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from PyQt6.QtCore import QTimer, pyqtSignal, QObject
from PyQt6.QtWidgets import (QMessageBox,
                            QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                            QListWidget, QLabel, QTextEdit, QComboBox, QSpinBox,
                            QCheckBox, QGroupBox, QGridLayout, QWidget)
from PyQt6.QtGui import Q<PERSON>eySequence, QAction, QUndoStack, QUndoCommand, QShortcut

logger = logging.getLogger(__name__)

# ========== UNDO/REDO SYSTEM ==========

class EditorCommand(QUndoCommand):
    """Base command for editor operations"""
    
    def __init__(self, editor, description: str):
        super().__init__(description)
        self.editor = editor
        self.old_data = None
        self.new_data = None
    
    def undo(self):
        """Undo the command"""
        if self.old_data is not None:
            self.editor.load_data_from_dict(self.old_data)
            logger.debug(f"Undid: {self.text()}")
    
    def redo(self):
        """Redo the command"""
        if self.new_data is not None:
            self.editor.load_data_from_dict(self.new_data)
            logger.debug(f"Redid: {self.text()}")

class FieldChangeCommand(EditorCommand):
    """Command for field value changes"""
    
    def __init__(self, editor, field_name: str, old_value: Any, new_value: Any):
        super().__init__(editor, f"Change {field_name}")
        self.field_name = field_name
        self.old_value = old_value
        self.new_value = new_value
    
    def undo(self):
        """Undo field change"""
        self.editor.set_field_value(self.field_name, self.old_value)
        logger.debug(f"Undid field change: {self.field_name} = {self.old_value}")
    
    def redo(self):
        """Redo field change"""
        self.editor.set_field_value(self.field_name, self.new_value)
        logger.debug(f"Redid field change: {self.field_name} = {self.new_value}")

class DataChangeCommand(EditorCommand):
    """Command for complete data changes"""
    
    def __init__(self, editor, description: str, old_data: Dict[str, Any], new_data: Dict[str, Any]):
        super().__init__(editor, description)
        self.old_data = old_data.copy() if old_data else {}
        self.new_data = new_data.copy() if new_data else {}

class UndoRedoManager:
    """Manages undo/redo operations for editors"""
    
    def __init__(self, editor):
        self.editor = editor
        self.undo_stack = QUndoStack()
        self.undo_stack.setUndoLimit(50)  # Limit to 50 operations
        
        # Connect to editor's undo/redo actions if they exist
        self.setup_actions()
    
    def setup_actions(self):
        """Setup undo/redo actions"""
        try:
            # Create undo/redo actions
            self.undo_action = self.undo_stack.createUndoAction(self.editor, "Undo")
            self.redo_action = self.undo_stack.createRedoAction(self.editor, "Redo")
            
            # Set shortcuts
            self.undo_action.setShortcut(QKeySequence.StandardKey.Undo)
            self.redo_action.setShortcut(QKeySequence.StandardKey.Redo)
            
            logger.info("Undo/Redo system initialized")
            
        except Exception as e:
            logger.error(f"Error setting up undo/redo actions: {e}")
    
    def push_command(self, command: QUndoCommand):
        """Push a command to the undo stack"""
        self.undo_stack.push(command)
    
    def push_field_change(self, field_name: str, old_value: Any, new_value: Any):
        """Push a field change command"""
        command = FieldChangeCommand(self.editor, field_name, old_value, new_value)
        self.push_command(command)
    
    def push_data_change(self, description: str, old_data: Dict[str, Any], new_data: Dict[str, Any]):
        """Push a data change command"""
        command = DataChangeCommand(self.editor, description, old_data, new_data)
        self.push_command(command)
    
    def clear(self):
        """Clear the undo stack"""
        self.undo_stack.clear()
    
    def can_undo(self) -> bool:
        """Check if undo is available"""
        return self.undo_stack.canUndo()
    
    def can_redo(self) -> bool:
        """Check if redo is available"""
        return self.undo_stack.canRedo()

# ========== KEYBOARD SHORTCUTS SYSTEM ==========

class KeyboardShortcutManager:
    """Manages enhanced keyboard shortcuts for editors"""
    
    def __init__(self, editor):
        self.editor = editor
        self.shortcuts = []
        self.setup_shortcuts()
    
    def setup_shortcuts(self):
        """Setup enhanced keyboard shortcuts"""
        shortcuts_config = [
            # File operations
            ("Ctrl+N", self.new_item, "New"),
            ("Ctrl+O", self.open_item, "Open"),
            ("Ctrl+S", self.save_item, "Save"),
            ("Ctrl+Shift+S", self.save_as_item, "Save As"),
            ("Ctrl+D", self.duplicate_item, "Duplicate"),
            ("Delete", self.delete_item, "Delete"),
            
            # Edit operations
            ("Ctrl+Z", self.undo, "Undo"),
            ("Ctrl+Y", self.redo, "Redo"),
            ("Ctrl+Shift+Z", self.redo, "Redo (Alt)"),
            
            # Navigation
            ("Ctrl+Tab", self.next_tab, "Next Tab"),
            ("Ctrl+Shift+Tab", self.prev_tab, "Previous Tab"),
            ("F5", self.refresh, "Refresh"),
            
            # Quick actions
            ("Ctrl+R", self.reset_form, "Reset Form"),
            ("Ctrl+T", self.show_templates, "Show Templates"),
            ("Ctrl+P", self.preview, "Preview"),
            ("Escape", self.cancel_operation, "Cancel"),
            
            # Field navigation
            ("Tab", self.next_field, "Next Field"),
            ("Shift+Tab", self.prev_field, "Previous Field"),
        ]
        
        for key_sequence, handler, description in shortcuts_config:
            try:
                shortcut = QShortcut(QKeySequence(key_sequence), self.editor)
                shortcut.activated.connect(handler)
                self.shortcuts.append((shortcut, description))
                logger.debug(f"Registered shortcut: {key_sequence} - {description}")
                
            except Exception as e:
                logger.error(f"Error registering shortcut {key_sequence}: {e}")
    
    # Shortcut handlers
    def new_item(self):
        """Create new item"""
        if hasattr(self.editor, 'new_data'):
            self.editor.new_data()
        elif hasattr(self.editor, 'reset_form'):
            self.editor.reset_form()
    
    def open_item(self):
        """Open item"""
        if hasattr(self.editor, 'open_data'):
            self.editor.open_data()
        elif hasattr(self.editor, 'load_data'):
            self.editor.load_data()
    
    def save_item(self):
        """Save item"""
        if hasattr(self.editor, 'save_data'):
            self.editor.save_data()
    
    def save_as_item(self):
        """Save as item"""
        if hasattr(self.editor, 'save_as_data'):
            self.editor.save_as_data()
    
    def duplicate_item(self):
        """Duplicate current item"""
        if hasattr(self.editor, 'duplicate_current'):
            self.editor.duplicate_current()
    
    def delete_item(self):
        """Delete current item"""
        if hasattr(self.editor, 'delete_current'):
            self.editor.delete_current()
    
    def undo(self):
        """Undo last action"""
        if hasattr(self.editor, 'undo_manager'):
            self.editor.undo_manager.undo_stack.undo()
    
    def redo(self):
        """Redo last action"""
        if hasattr(self.editor, 'undo_manager'):
            self.editor.undo_manager.undo_stack.redo()
    
    def next_tab(self):
        """Switch to next tab"""
        if hasattr(self.editor, 'tab_widget'):
            current = self.editor.tab_widget.currentIndex()
            count = self.editor.tab_widget.count()
            self.editor.tab_widget.setCurrentIndex((current + 1) % count)
    
    def prev_tab(self):
        """Switch to previous tab"""
        if hasattr(self.editor, 'tab_widget'):
            current = self.editor.tab_widget.currentIndex()
            count = self.editor.tab_widget.count()
            self.editor.tab_widget.setCurrentIndex((current - 1) % count)
    
    def refresh(self):
        """Refresh editor"""
        if hasattr(self.editor, 'refresh_data'):
            self.editor.refresh_data()
    
    def reset_form(self):
        """Reset form"""
        if hasattr(self.editor, 'reset_form'):
            self.editor.reset_form()
    
    def show_templates(self):
        """Show template dialog"""
        if hasattr(self.editor, 'template_manager'):
            self.editor.template_manager.show_template_dialog()
    
    def preview(self):
        """Show preview"""
        if hasattr(self.editor, 'show_preview'):
            self.editor.show_preview()
    
    def cancel_operation(self):
        """Cancel current operation"""
        # Close any open dialogs or reset current operation
        pass
    
    def next_field(self):
        """Navigate to next field"""
        # Let Qt handle default tab behavior
        pass
    
    def prev_field(self):
        """Navigate to previous field"""
        # Let Qt handle default shift+tab behavior
        pass

# ========== AUTO-SAVE SYSTEM ==========

class AutoSaveManager(QObject):
    """Manages automatic saving of editor data"""
    
    auto_saved = pyqtSignal(str)  # Emitted when auto-save occurs
    
    def __init__(self, editor, interval_seconds: int = 300):  # 5 minutes default
        super().__init__()
        self.editor = editor
        self.interval_seconds = interval_seconds
        self.timer = QTimer()
        self.timer.timeout.connect(self.auto_save)
        self.enabled = True
        self.last_data_hash = None
    
    def start(self):
        """Start auto-save timer"""
        if self.enabled:
            self.timer.start(self.interval_seconds * 1000)
            logger.info(f"Auto-save started with {self.interval_seconds}s interval")
    
    def stop(self):
        """Stop auto-save timer"""
        self.timer.stop()
        logger.info("Auto-save stopped")
    
    def auto_save(self):
        """Perform auto-save if data has changed"""
        try:
            if not hasattr(self.editor, 'collect_data'):
                return
            
            current_data = self.editor.collect_data()
            current_hash = hash(str(current_data))
            
            if current_hash != self.last_data_hash and self.editor.current_filename:
                # Data has changed and we have a filename
                backup_path = self._create_backup_path()
                
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(current_data, f, indent=2)
                
                self.last_data_hash = current_hash
                self.auto_saved.emit(str(backup_path))
                logger.info(f"Auto-saved to: {backup_path}")
                
        except Exception as e:
            logger.error(f"Auto-save failed: {e}")
    
    def _create_backup_path(self) -> Path:
        """Create backup file path"""
        if self.editor.current_filename:
            original_path = Path(self.editor.current_filename)
            backup_dir = original_path.parent / "backups"
            backup_dir.mkdir(exist_ok=True)
            
            backup_name = f"{original_path.stem}_autosave{original_path.suffix}"
            return backup_dir / backup_name
        else:
            # Create temp backup
            backup_dir = Path("data") / "temp_backups"
            backup_dir.mkdir(exist_ok=True)
            return backup_dir / f"autosave_{self.editor.data_type}.json"

# ========== TEMPLATE SYSTEM ==========

class TemplateManager:
    """Manages templates for common pieces and abilities"""

    def __init__(self, editor):
        self.editor = editor
        self.templates_dir = Path("data") / "templates"
        self.templates_dir.mkdir(exist_ok=True)
        self.piece_templates = {}
        self.ability_templates = {}
        self.load_templates()

    def load_templates(self):
        """Load all available templates"""
        try:
            # Load piece templates
            piece_templates_file = self.templates_dir / "piece_templates.json"
            if piece_templates_file.exists():
                with open(piece_templates_file, 'r', encoding='utf-8') as f:
                    self.piece_templates = json.load(f)
            else:
                self.piece_templates = self.create_default_piece_templates()
                self.save_piece_templates()

            # Load ability templates
            ability_templates_file = self.templates_dir / "ability_templates.json"
            if ability_templates_file.exists():
                with open(ability_templates_file, 'r', encoding='utf-8') as f:
                    self.ability_templates = json.load(f)
            else:
                self.ability_templates = self.create_default_ability_templates()
                self.save_ability_templates()

            logger.info(f"Loaded {len(self.piece_templates)} piece templates and {len(self.ability_templates)} ability templates")

        except Exception as e:
            logger.error(f"Error loading templates: {e}")

    def create_default_piece_templates(self) -> Dict[str, Dict[str, Any]]:
        """Create default piece templates"""
        return {
            "Basic Pawn": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic pawn piece",
                "role": "Soldier",
                "can_castle": False,
                "track_starting_position": True,
                "color_directional": True,
                "can_capture": True,
                "movement": {
                    "type": "Custom",
                    "pattern": self._create_pawn_pattern(),
                    "piece_position": [4, 4]
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Rook": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic rook piece",
                "role": "Commander",
                "can_castle": True,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Knight": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic knight piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "L-shape",
                    "distance": 1
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Bishop": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic bishop piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "diagonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic Queen": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic queen piece",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 8
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Basic King": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic king piece",
                "role": "King",
                "can_castle": True,
                "track_starting_position": True,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 1
                },
                "recharge": {"type": "None", "turns": 0},
                "abilities": [],
                "promotions": []
            },
            "Magical Archer": {
                "version": "1.0.0",
                "name": "",
                "description": "An archer with ranged magical abilities",
                "role": "Soldier",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 2
                },
                "recharge": {"type": "turnRecharge", "turns": 2},
                "abilities": ["Magic Bolt", "Long Range Shot"],
                "promotions": []
            },
            "Teleporting Mage": {
                "version": "1.0.0",
                "name": "",
                "description": "A mage with teleportation abilities",
                "role": "Commander",
                "can_castle": False,
                "track_starting_position": False,
                "color_directional": False,
                "can_capture": True,
                "movement": {
                    "type": "orthogonal_diagonal",
                    "distance": 1
                },
                "recharge": {"type": "turnRecharge", "turns": 3},
                "abilities": ["Teleport", "Magic Shield"],
                "promotions": []
            }
        }

    def create_default_ability_templates(self) -> Dict[str, Dict[str, Any]]:
        """Create default ability templates"""
        return {
            "Basic Attack": {
                "version": "1.0.0",
                "name": "",
                "description": "A basic attack ability",
                "cost": 1,
                "tags": ["capture"],
                "activationMode": "click",
                "captureTarget": "Enemy"
            },
            "Magic Bolt": {
                "version": "1.0.0",
                "name": "",
                "description": "A magical projectile attack",
                "cost": 2,
                "tags": ["capture", "range"],
                "activationMode": "click",
                "rangeMask": self._create_cross_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False,
                "captureTarget": "Enemy"
            },
            "Teleport": {
                "version": "1.0.0",
                "name": "",
                "description": "Teleport to any empty square",
                "cost": 3,
                "tags": ["move", "range"],
                "activationMode": "click",
                "rangeMask": self._create_full_board_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False
            },
            "Heal": {
                "version": "1.0.0",
                "name": "",
                "description": "Heal a friendly piece",
                "cost": 2,
                "tags": ["buffPiece", "range"],
                "activationMode": "click",
                "rangeMask": self._create_adjacent_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": True,
                "rangeEnemyOnly": False
            },
            "Summon Pawn": {
                "version": "1.0.0",
                "name": "",
                "description": "Summon a pawn piece",
                "cost": 3,
                "tags": ["summon", "range"],
                "activationMode": "click",
                "rangeMask": self._create_adjacent_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False,
                "summonPieces": ["Adventure Pawn"]
            },
            "Shield": {
                "version": "1.0.0",
                "name": "",
                "description": "Create a protective barrier",
                "cost": 2,
                "tags": ["addObstacle", "range"],
                "activationMode": "click",
                "rangeMask": self._create_adjacent_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False
            },
            "Area Blast": {
                "version": "1.0.0",
                "name": "",
                "description": "Area of effect damage",
                "cost": 4,
                "tags": ["capture", "range", "areaEffect"],
                "activationMode": "click",
                "rangeMask": self._create_cross_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False,
                "captureTarget": "Enemy",
                "areaEffectMask": self._create_adjacent_pattern(),
                "areaEffectPosition": [4, 4]
            }
        }

    def save_piece_templates(self):
        """Save piece templates to file"""
        try:
            templates_file = self.templates_dir / "piece_templates.json"
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.piece_templates, f, indent=2)
            logger.info("Piece templates saved")
        except Exception as e:
            logger.error(f"Error saving piece templates: {e}")

    def save_ability_templates(self):
        """Save ability templates to file"""
        try:
            templates_file = self.templates_dir / "ability_templates.json"
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.ability_templates, f, indent=2)
            logger.info("Ability templates saved")
        except Exception as e:
            logger.error(f"Error saving ability templates: {e}")

    def get_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get templates for current editor type"""
        if hasattr(self.editor, 'data_type'):
            if self.editor.data_type == 'piece':
                return self.piece_templates
            elif self.editor.data_type == 'ability':
                return self.ability_templates
        return {}

    def apply_template(self, template_name: str):
        """Apply a template to the current editor"""
        try:
            templates = self.get_templates()
            if template_name in templates:
                template_data = templates[template_name].copy()

                # Clear name to force user to set it
                template_data['name'] = ""

                # Apply template data to editor
                if hasattr(self.editor, 'load_data_from_dict'):
                    self.editor.load_data_from_dict(template_data)
                elif hasattr(self.editor, 'populate_data'):
                    self.editor.populate_data(template_data)

                logger.info(f"Applied template: {template_name}")
                return True
            else:
                logger.warning(f"Template not found: {template_name}")
                return False

        except Exception as e:
            logger.error(f"Error applying template {template_name}: {e}")
            return False

    def save_as_template(self, template_name: str, description: str = ""):
        """Save current editor data as a template"""
        try:
            if hasattr(self.editor, 'collect_data'):
                current_data = self.editor.collect_data()

                # Remove name and description to make it a template
                template_data = current_data.copy()
                template_data['name'] = ""
                if description:
                    template_data['description'] = description

                # Save to appropriate template collection
                if hasattr(self.editor, 'data_type'):
                    if self.editor.data_type == 'piece':
                        self.piece_templates[template_name] = template_data
                        self.save_piece_templates()
                    elif self.editor.data_type == 'ability':
                        self.ability_templates[template_name] = template_data
                        self.save_ability_templates()

                logger.info(f"Saved template: {template_name}")
                return True

        except Exception as e:
            logger.error(f"Error saving template {template_name}: {e}")
            return False

    def show_template_dialog(self):
        """Show template selection dialog"""
        dialog = TemplateDialog(self.editor, self)
        dialog.exec()

    # Pattern creation utilities
    def _create_pawn_pattern(self):
        """Create pawn movement pattern"""
        pattern = [[False for _ in range(9)] for _ in range(9)]
        # Forward movement
        pattern[3][4] = True  # One square forward
        pattern[2][4] = True  # Two squares forward (for starting position)
        # Diagonal capture
        pattern[3][3] = True  # Diagonal left
        pattern[3][5] = True  # Diagonal right
        return pattern

    def _create_cross_pattern(self):
        """Create cross/plus pattern"""
        pattern = [[False for _ in range(9)] for _ in range(9)]
        center = 4
        for i in range(9):
            pattern[center][i] = True  # Horizontal
            pattern[i][center] = True  # Vertical
        pattern[center][center] = False  # Don't include center
        return pattern

    def _create_adjacent_pattern(self):
        """Create adjacent squares pattern"""
        pattern = [[False for _ in range(9)] for _ in range(9)]
        center = 4
        for row in range(center-1, center+2):
            for col in range(center-1, center+2):
                if 0 <= row < 9 and 0 <= col < 9 and (row != center or col != center):
                    pattern[row][col] = True
        return pattern

    def _create_full_board_pattern(self):
        """Create full board pattern"""
        pattern = [[True for _ in range(9)] for _ in range(9)]
        pattern[4][4] = False  # Don't include center
        return pattern

class TemplateDialog(QDialog):
    """Dialog for selecting and managing templates"""

    def __init__(self, editor, template_manager):
        super().__init__(editor)
        self.editor = editor
        self.template_manager = template_manager
        self.setWindowTitle("Templates")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()

    def setup_ui(self):
        """Setup the template dialog UI"""
        layout = QVBoxLayout(self)

        # Title
        title = QLabel(f"Templates for {self.editor.data_type.title()}s")
        title.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # Template list
        self.template_list = QListWidget()
        self.populate_template_list()
        layout.addWidget(self.template_list)

        # Template preview
        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout(preview_group)
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(100)
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        layout.addWidget(preview_group)

        # Buttons
        button_layout = QHBoxLayout()

        self.apply_btn = QPushButton("Apply Template")
        self.apply_btn.clicked.connect(self.apply_template)

        self.save_btn = QPushButton("Save as Template")
        self.save_btn.clicked.connect(self.save_template)

        self.delete_btn = QPushButton("Delete Template")
        self.delete_btn.clicked.connect(self.delete_template)

        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.close)

        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        # Connect list selection
        self.template_list.currentItemChanged.connect(self.update_preview)

    def populate_template_list(self):
        """Populate the template list"""
        self.template_list.clear()
        templates = self.template_manager.get_templates()

        for template_name in sorted(templates.keys()):
            self.template_list.addItem(template_name)

    def update_preview(self):
        """Update template preview"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_name = current_item.text()
            templates = self.template_manager.get_templates()

            if template_name in templates:
                template_data = templates[template_name]
                preview_text = f"Name: {template_data.get('name', 'Template')}\n"
                preview_text += f"Description: {template_data.get('description', 'No description')}\n"

                if 'tags' in template_data:
                    preview_text += f"Tags: {', '.join(template_data['tags'])}\n"
                if 'cost' in template_data:
                    preview_text += f"Cost: {template_data['cost']}\n"
                if 'role' in template_data:
                    preview_text += f"Role: {template_data['role']}\n"
                if 'movement' in template_data:
                    movement = template_data['movement']
                    if isinstance(movement, dict):
                        preview_text += f"Movement: {movement.get('type', 'Unknown')}\n"

                self.preview_text.setPlainText(preview_text)

    def apply_template(self):
        """Apply selected template"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_name = current_item.text()
            if self.template_manager.apply_template(template_name):
                QMessageBox.information(self, "Success", f"Template '{template_name}' applied successfully!")
                self.close()
            else:
                QMessageBox.warning(self, "Error", f"Failed to apply template '{template_name}'")

    def save_template(self):
        """Save current data as template"""
        from PyQt6.QtWidgets import QInputDialog

        template_name, ok = QInputDialog.getText(
            self, "Save Template", "Enter template name:"
        )

        if ok and template_name.strip():
            description, ok2 = QInputDialog.getText(
                self, "Template Description", "Enter description (optional):"
            )

            if self.template_manager.save_as_template(template_name.strip(), description if ok2 else ""):
                QMessageBox.information(self, "Success", f"Template '{template_name}' saved successfully!")
                self.populate_template_list()
            else:
                QMessageBox.warning(self, "Error", f"Failed to save template '{template_name}'")

    def delete_template(self):
        """Delete selected template"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_name = current_item.text()

            reply = QMessageBox.question(
                self, "Delete Template",
                f"Are you sure you want to delete the template '{template_name}'?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                templates = self.template_manager.get_templates()
                if template_name in templates:
                    del templates[template_name]

                    # Save updated templates
                    if hasattr(self.editor, 'data_type'):
                        if self.editor.data_type == 'piece':
                            self.template_manager.save_piece_templates()
                        elif self.editor.data_type == 'ability':
                            self.template_manager.save_ability_templates()

                    self.populate_template_list()
                    self.preview_text.clear()
                    QMessageBox.information(self, "Success", f"Template '{template_name}' deleted successfully!")
