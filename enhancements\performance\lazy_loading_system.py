#!/usr/bin/env python3
"""
Comprehensive Lazy Loading System for Adventure Chess Creator

This consolidated module implements complete lazy loading functionality including:
- On-demand data loading with background processing
- Progress indicators and loading widgets
- Data integration with existing managers
- UI components with lazy loading capabilities
- File list management and caching

Consolidates functionality from:
- lazy_loading_system.py (core lazy loading)
- lazy_data_integration.py (data manager integration)
- lazy_ui_components.py (UI components)
"""

import os
import sys
import time
import logging
import threading
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import (
    QProgressBar, QLabel, QWidget, QVBoxLayout, QComboBox, QListWidget,
    QListWidgetItem, QHBoxLayout, QPushButton
)

logger = logging.getLogger(__name__)

@dataclass
class LazyLoadRequest:
    """Represents a lazy loading request"""
    key: str
    load_function: Callable[[], Any]
    priority: int = 0  # Higher priority loads first
    callback: Optional[Callable[[Any], None]] = None
    error_callback: Optional[Callable[[Exception], None]] = None
    created_at: datetime = field(default_factory=datetime.now)
    
class LoadingProgressWidget(QWidget):
    """Widget to show loading progress with customizable appearance"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the loading progress UI"""
        layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
                text-align: center;
            }
        """)
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        self.setLayout(layout)
        
    def update_progress(self, value: int, message: str = ""):
        """Update progress bar and status message"""
        self.progress_bar.setValue(value)
        if message:
            self.status_label.setText(message)
            
    def show_loading(self, message: str = "Loading..."):
        """Show indeterminate loading state"""
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.status_label.setText(message)
        self.setVisible(True)
        
    def hide_loading(self):
        """Hide loading indicator"""
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        self.setVisible(False)

class BackgroundLoader(QThread):
    """Background thread for loading data without blocking UI"""
    
    progress_updated = pyqtSignal(int, str)  # progress, message
    item_loaded = pyqtSignal(str, object)    # key, data
    loading_finished = pyqtSignal(dict)      # results summary
    error_occurred = pyqtSignal(str, str)    # key, error_message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.requests: List[LazyLoadRequest] = []
        self.is_running = False
        self.should_stop = False
        
    def add_request(self, request: LazyLoadRequest):
        """Add a loading request to the queue"""
        self.requests.append(request)
        # Sort by priority (higher first)
        self.requests.sort(key=lambda r: r.priority, reverse=True)
        
    def run(self):
        """Execute loading requests in background"""
        self.is_running = True
        total_requests = len(self.requests)
        completed = 0
        results = {"success": 0, "errors": 0, "total": total_requests}
        
        try:
            for request in self.requests:
                if self.should_stop:
                    break
                    
                try:
                    # Update progress
                    progress = int((completed / total_requests) * 100) if total_requests > 0 else 0
                    self.progress_updated.emit(progress, f"Loading {request.key}...")
                    
                    # Execute load function
                    data = request.load_function()
                    
                    # Emit success
                    self.item_loaded.emit(request.key, data)
                    
                    # Call success callback if provided
                    if request.callback:
                        request.callback(data)
                        
                    results["success"] += 1
                    
                except Exception as e:
                    logger.error(f"Error loading {request.key}: {e}")
                    self.error_occurred.emit(request.key, str(e))
                    
                    # Call error callback if provided
                    if request.error_callback:
                        request.error_callback(e)
                        
                    results["errors"] += 1
                
                completed += 1
                
            # Final progress update
            if not self.should_stop:
                self.progress_updated.emit(100, f"Completed: {results['success']} loaded, {results['errors']} errors")
                
        finally:
            self.loading_finished.emit(results)
            self.is_running = False
            
    def stop_loading(self):
        """Stop the loading process"""
        self.should_stop = True

class LazyDataManager:
    """
    Manages lazy loading of data with caching and background loading capabilities
    """
    
    def __init__(self, cache_manager=None, max_workers: int = 4):
        self.cache_manager = cache_manager
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Lazy loading state
        self.pending_requests: Dict[str, Future] = {}
        self.loading_callbacks: Dict[str, List[Callable]] = {}
        
        # File metadata cache for quick access
        self.file_metadata: Dict[str, Dict[str, Any]] = {}
        self.metadata_loaded = False
        
        # Background loader for UI integration
        self.background_loader: Optional[BackgroundLoader] = None
        
    def load_file_metadata_lazy(self, directory: str, extension: str = ".json") -> List[Dict[str, Any]]:
        """
        Load file metadata (name, size, modified time) without loading full content
        This provides quick file listing for UI components
        """
        try:
            if not os.path.exists(directory):
                return []
                
            metadata_list = []
            dir_path = Path(directory)
            
            for file_path in dir_path.glob(f"*{extension}"):
                try:
                    stat = file_path.stat()
                    metadata = {
                        "filename": file_path.stem,
                        "full_path": str(file_path),
                        "size_bytes": stat.st_size,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime),
                        "is_loaded": False,
                        "has_error": False
                    }
                    
                    # Try to get basic info from cache if available
                    if self.cache_manager:
                        cache_key = str(file_path)
                        if file_path.name.endswith('.json'):
                            if 'piece' in str(directory).lower():
                                cached_data = self.cache_manager.get_piece(cache_key)
                            else:
                                cached_data = self.cache_manager.get_ability(cache_key)
                            
                            if cached_data:
                                metadata["is_loaded"] = True
                                metadata["display_name"] = cached_data.get("name", file_path.stem)
                                metadata["description"] = cached_data.get("description", "")
                    
                    metadata_list.append(metadata)
                    
                except Exception as e:
                    logger.warning(f"Error reading metadata for {file_path}: {e}")
                    metadata_list.append({
                        "filename": file_path.stem,
                        "full_path": str(file_path),
                        "size_bytes": 0,
                        "modified_time": datetime.now(),
                        "is_loaded": False,
                        "has_error": True,
                        "error": str(e)
                    })
            
            # Sort by modification time (newest first)
            metadata_list.sort(key=lambda x: x["modified_time"], reverse=True)
            return metadata_list
            
        except Exception as e:
            logger.error(f"Error loading file metadata from {directory}: {e}")
            return []
    
    def load_data_lazy(self, key: str, load_function: Callable[[], Any], 
                      callback: Optional[Callable[[Any], None]] = None,
                      priority: int = 0) -> Optional[Any]:
        """
        Load data lazily - returns immediately if cached, otherwise loads in background
        """
        # Check cache first
        if self.cache_manager:
            if 'piece' in key.lower():
                cached_data = self.cache_manager.get_piece(key)
            else:
                cached_data = self.cache_manager.get_ability(key)
                
            if cached_data is not None:
                if callback:
                    callback(cached_data)
                return cached_data
        
        # Check if already loading
        if key in self.pending_requests:
            # Add callback to existing request
            if callback:
                if key not in self.loading_callbacks:
                    self.loading_callbacks[key] = []
                self.loading_callbacks[key].append(callback)
            return None
        
        # Start new loading request
        future = self.executor.submit(self._load_with_caching, key, load_function)
        self.pending_requests[key] = future
        
        if callback:
            self.loading_callbacks[key] = [callback]
        
        # Handle completion
        def on_complete(fut):
            try:
                data = fut.result()
                # Call all callbacks
                if key in self.loading_callbacks:
                    for cb in self.loading_callbacks[key]:
                        try:
                            cb(data)
                        except Exception as e:
                            logger.error(f"Error in callback for {key}: {e}")
                    del self.loading_callbacks[key]
            except Exception as e:
                logger.error(f"Error loading {key}: {e}")
                # Call error callbacks if any
                if key in self.loading_callbacks:
                    del self.loading_callbacks[key]
            finally:
                if key in self.pending_requests:
                    del self.pending_requests[key]
        
        future.add_done_callback(on_complete)
        return None
    
    def _load_with_caching(self, key: str, load_function: Callable[[], Any]) -> Any:
        """Load data and cache it"""
        try:
            data = load_function()
            
            # Cache the data
            if self.cache_manager and data is not None:
                if 'piece' in key.lower():
                    self.cache_manager.set_piece(key, data)
                else:
                    self.cache_manager.set_ability(key, data)
            
            return data
            
        except Exception as e:
            logger.error(f"Error in _load_with_caching for {key}: {e}")
            raise
    
    def preload_files(self, file_list: List[str], load_functions: Dict[str, Callable],
                     progress_callback: Optional[Callable[[int, str], None]] = None) -> None:
        """
        Preload multiple files in background with progress tracking
        """
        if not file_list:
            return
            
        def preload_worker():
            total = len(file_list)
            for i, filename in enumerate(file_list):
                try:
                    if filename in load_functions:
                        load_func = load_functions[filename]
                        self._load_with_caching(filename, load_func)
                        
                    if progress_callback:
                        progress = int((i + 1) / total * 100)
                        progress_callback(progress, f"Preloaded {filename}")
                        
                except Exception as e:
                    logger.error(f"Error preloading {filename}: {e}")
                    if progress_callback:
                        progress = int((i + 1) / total * 100)
                        progress_callback(progress, f"Error loading {filename}")
        
        # Run preloading in background
        self.executor.submit(preload_worker)
    
    def is_loading(self, key: str) -> bool:
        """Check if a specific key is currently being loaded"""
        return key in self.pending_requests
    
    def get_loading_status(self) -> Dict[str, Any]:
        """Get current loading status"""
        return {
            "pending_requests": len(self.pending_requests),
            "active_callbacks": len(self.loading_callbacks),
            "pending_keys": list(self.pending_requests.keys())
        }
    
    def shutdown(self):
        """Shutdown the lazy data manager"""
        try:
            # Cancel pending requests
            for future in self.pending_requests.values():
                future.cancel()

            # Shutdown executor (timeout parameter added in Python 3.9)
            try:
                self.executor.shutdown(wait=True, timeout=5.0)
            except TypeError:
                # Fallback for older Python versions
                self.executor.shutdown(wait=True)

            # Stop background loader if running
            if self.background_loader and self.background_loader.isRunning():
                self.background_loader.stop_loading()
                self.background_loader.wait(3000)  # Wait up to 3 seconds

        except Exception as e:
            logger.error(f"Error shutting down lazy data manager: {e}")

# Global lazy data manager instance
_lazy_manager: Optional[LazyDataManager] = None

def get_lazy_manager(cache_manager=None) -> LazyDataManager:
    """Get or create the global lazy data manager"""
    global _lazy_manager
    if _lazy_manager is None or _lazy_manager.executor._shutdown:
        if _lazy_manager:
            _lazy_manager.shutdown()
        _lazy_manager = LazyDataManager(cache_manager)
    return _lazy_manager

def reset_lazy_manager():
    """Reset the global lazy manager (for testing)"""
    global _lazy_manager
    if _lazy_manager:
        _lazy_manager.shutdown()
    _lazy_manager = None


# ========== LAZY DATA INTEGRATION ==========

class LazyFileListManager(QObject):
    """
    Manages lazy loading for file lists in UI components like dropdowns and list widgets
    """

    files_loaded = pyqtSignal(list)  # Emitted when file metadata is loaded
    file_data_loaded = pyqtSignal(str, dict)  # Emitted when individual file data is loaded
    loading_progress = pyqtSignal(int, str)  # Progress updates

    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension

        # Import cache manager here to avoid circular imports
        try:
            from .editor_integration import get_cache_manager
            self.lazy_manager = get_lazy_manager(get_cache_manager())
        except ImportError:
            self.lazy_manager = get_lazy_manager()

        # File metadata cache
        self.file_metadata: List[Dict[str, Any]] = []
        self.metadata_loaded = False

        # UI components that need updates
        self.registered_combos: List[QComboBox] = []
        self.registered_lists: List[QListWidget] = []

    def register_combo_box(self, combo: QComboBox):
        """Register a combo box for lazy updates"""
        self.registered_combos.append(combo)
        if self.metadata_loaded:
            self._update_combo_box(combo)

    def register_list_widget(self, list_widget: QListWidget):
        """Register a list widget for lazy updates"""
        self.registered_lists.append(list_widget)
        if self.metadata_loaded:
            self._update_list_widget(list_widget)

    def load_file_list_lazy(self):
        """Load file list metadata without full file content"""
        if self.metadata_loaded:
            return

        def load_metadata():
            return self.lazy_manager.load_file_metadata_lazy(self.directory, self.file_extension)

        def on_metadata_loaded(metadata):
            self.file_metadata = metadata
            self.metadata_loaded = True
            self.files_loaded.emit(metadata)
            self._update_all_ui_components()

        # Load metadata in background
        self.lazy_manager.load_data_lazy(
            f"metadata_{self.directory}",
            load_metadata,
            on_metadata_loaded,
            priority=10  # High priority for metadata
        )

    def load_file_data_lazy(self, filename: str, priority: int = 0):
        """Load full file data lazily"""
        file_path = Path(self.directory) / f"{filename}{self.file_extension}"
        cache_key = str(file_path)

        def load_file():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading file {file_path}: {e}")
                return None

        def on_file_loaded(data):
            if data:
                self.file_data_loaded.emit(filename, data)

        # Load file data in background
        self.lazy_manager.load_data_lazy(
            cache_key,
            load_file,
            on_file_loaded,
            priority=priority
        )

    def _update_combo_box(self, combo: QComboBox):
        """Update combo box with loaded metadata"""
        combo.clear()
        for metadata in self.file_metadata:
            display_name = metadata.get('display_name', metadata.get('filename', 'Unknown'))
            combo.addItem(display_name, metadata)

    def _update_list_widget(self, list_widget: QListWidget):
        """Update list widget with loaded metadata"""
        list_widget.clear()
        for metadata in self.file_metadata:
            display_name = metadata.get('display_name', metadata.get('filename', 'Unknown'))
            item = QListWidgetItem(display_name)
            item.setData(Qt.UserRole, metadata)
            list_widget.addItem(item)

    def _update_all_ui_components(self):
        """Update all registered UI components"""
        for combo in self.registered_combos:
            self._update_combo_box(combo)
        for list_widget in self.registered_lists:
            self._update_list_widget(list_widget)

    def get_file_metadata(self, filename: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific file"""
        for metadata in self.file_metadata:
            if metadata.get('filename') == filename:
                return metadata
        return None

    def preload_recent_files(self, max_files: int = 10):
        """Preload recently accessed files"""
        if not self.metadata_loaded:
            return

        # Sort by last modified time and preload recent files
        sorted_metadata = sorted(
            self.file_metadata,
            key=lambda x: x.get('modified_time', 0),
            reverse=True
        )

        for metadata in sorted_metadata[:max_files]:
            filename = metadata.get('filename')
            if filename:
                self.load_file_data_lazy(filename, priority=5)


class LazyIntegratedDataManager(QObject):
    """
    Integrated data manager that combines lazy loading with existing data managers
    """

    loading_progress = pyqtSignal(int, str)  # Progress updates

    def __init__(self, parent=None):
        super().__init__(parent)

        # Import config constants with fallbacks
        try:
            from config import PIECES_DIR, PIECE_EXTENSION, ABILITIES_DIR, ABILITY_EXTENSION
            self.pieces_dir = PIECES_DIR
            self.piece_extension = PIECE_EXTENSION
            self.abilities_dir = ABILITIES_DIR
            self.ability_extension = ABILITY_EXTENSION
        except ImportError:
            self.pieces_dir = 'data/pieces'
            self.piece_extension = '.json'
            self.abilities_dir = 'data/abilities'
            self.ability_extension = '.json'

        # Initialize lazy manager
        try:
            from .editor_integration import get_cache_manager
            self.lazy_manager = get_lazy_manager(get_cache_manager())
        except ImportError:
            self.lazy_manager = get_lazy_manager()

        # File list managers
        self.pieces_manager: Optional[LazyFileListManager] = None
        self.abilities_manager: Optional[LazyFileListManager] = None

        # Progress tracking
        self.loading_progress_callbacks: List[Callable[[int, str], None]] = []
        self._shutdown = False

    def add_progress_callback(self, callback: Callable[[int, str], None]):
        """Add a progress callback"""
        self.loading_progress_callbacks.append(callback)

    def get_pieces_manager(self) -> LazyFileListManager:
        """Get or create pieces file list manager"""
        if self.pieces_manager is None:
            self.pieces_manager = LazyFileListManager(self.pieces_dir, self.piece_extension)
        return self.pieces_manager

    def get_abilities_manager(self) -> LazyFileListManager:
        """Get or create abilities file list manager"""
        if self.abilities_manager is None:
            self.abilities_manager = LazyFileListManager(self.abilities_dir, self.ability_extension)
        return self.abilities_manager

    def load_piece_lazy(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                       priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load piece data lazily"""
        if self._shutdown:
            # Fallback to synchronous loading if shutdown
            data, error = self._load_piece_sync(filename)
            if callback and data:
                callback(data)
            return data

        # Normalize filename
        if not filename.endswith(self.piece_extension):
            filename += self.piece_extension

        file_path = Path(self.pieces_dir) / filename
        cache_key = str(file_path)

        def load_piece():
            if not file_path.exists():
                raise FileNotFoundError(f"Piece file not found: {filename}")

            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        return self.lazy_manager.load_data_lazy(cache_key, load_piece, callback, priority)

    def load_ability_lazy(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                         priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load ability data lazily"""
        if self._shutdown:
            # Fallback to synchronous loading if shutdown
            data, error = self._load_ability_sync(filename)
            if callback and data:
                callback(data)
            return data

        # Normalize filename
        if not filename.endswith(self.ability_extension):
            filename += self.ability_extension

        file_path = Path(self.abilities_dir) / filename
        cache_key = str(file_path)

        def load_ability():
            if not file_path.exists():
                raise FileNotFoundError(f"Ability file not found: {filename}")

            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        return self.lazy_manager.load_data_lazy(cache_key, load_ability, callback, priority)

    def _load_piece_sync(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Synchronous piece loading fallback"""
        try:
            if not filename.endswith(self.piece_extension):
                filename += self.piece_extension

            file_path = Path(self.pieces_dir) / filename
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f), None
        except Exception as e:
            return None, str(e)

    def _load_ability_sync(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Synchronous ability loading fallback"""
        try:
            if not filename.endswith(self.ability_extension):
                filename += self.ability_extension

            file_path = Path(self.abilities_dir) / filename
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f), None
        except Exception as e:
            return None, str(e)

    def preload_recent_files(self, max_files: int = 10):
        """Preload recently modified files in background"""
        try:
            # Get recent pieces
            pieces_metadata = self.lazy_manager.load_file_metadata_lazy(self.pieces_dir)
            recent_pieces = pieces_metadata[:max_files]

            # Get recent abilities
            abilities_metadata = self.lazy_manager.load_file_metadata_lazy(self.abilities_dir)
            recent_abilities = abilities_metadata[:max_files]

            # Create load functions
            load_functions = {}

            for metadata in recent_pieces:
                filename = metadata["filename"]
                load_functions[filename] = lambda f=filename: self._load_piece_sync(f)

            for metadata in recent_abilities:
                filename = metadata["filename"]
                load_functions[filename] = lambda f=filename: self._load_ability_sync(f)

            # Start preloading
            all_files = [m["filename"] for m in recent_pieces + recent_abilities]

            def progress_callback(progress, message):
                for callback in self.loading_progress_callbacks:
                    callback(progress, message)

            self.lazy_manager.preload_files(all_files, load_functions, progress_callback)

        except Exception as e:
            logger.error(f"Error preloading recent files: {e}")

    def shutdown(self):
        """Shutdown the integrated data manager"""
        self._shutdown = True
        if self.lazy_manager:
            self.lazy_manager.shutdown()


# Global lazy data manager instance
_lazy_data_manager: Optional[LazyIntegratedDataManager] = None

def get_lazy_data_manager() -> LazyIntegratedDataManager:
    """Get or create the global lazy data manager"""
    global _lazy_data_manager
    if _lazy_data_manager is None or _lazy_data_manager._shutdown:
        if _lazy_data_manager:
            _lazy_data_manager.shutdown()
        _lazy_data_manager = LazyIntegratedDataManager()
    return _lazy_data_manager


# ========== LAZY UI COMPONENTS ==========

class LazyComboBox(QComboBox):
    """
    Enhanced combo box with lazy loading support and progress indication
    """

    item_selected_lazy = pyqtSignal(str, dict)  # filename, data
    loading_started = pyqtSignal(str)  # filename
    loading_finished = pyqtSignal(str)  # filename

    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension

        # Lazy loading components
        self.file_manager = LazyFileListManager(directory, file_extension, self)
        self.file_manager.register_combo_box(self)
        self.file_manager.files_loaded.connect(self._on_files_loaded)
        self.file_manager.file_data_loaded.connect(self._on_file_data_loaded)

        # Loading state
        self.is_loading_files = False
        self.currently_loading_file: Optional[str] = None

        # Connect selection change
        self.currentTextChanged.connect(self._on_selection_changed)

        # Start loading file list
        self._load_file_list()

    def _load_file_list(self):
        """Start loading file list in background"""
        if not self.is_loading_files:
            self.is_loading_files = True
            self.addItem("🔄 Loading files...")
            self.file_manager.load_file_list_lazy()

    def _on_files_loaded(self, metadata_list: List[Dict[str, Any]]):
        """Handle file list loaded"""
        self.is_loading_files = False
        # UI will be updated by file_manager

    def _on_selection_changed(self, text: str):
        """Handle selection change with lazy loading"""
        if not text or text.startswith("🔄") or text.startswith("Select"):
            return

        # Get filename from item data
        current_index = self.currentIndex()
        if current_index <= 0:  # Skip "Select file..." item
            return

        filename = self.itemData(current_index)
        if not filename:
            return

        # Check if already loaded
        data_manager = get_lazy_data_manager()
        if not hasattr(data_manager, 'is_loading') or not data_manager.is_loading(filename):
            self.currently_loading_file = filename
            self.loading_started.emit(filename)

            # Load file data lazily
            def on_loaded(data):
                self.currently_loading_file = None
                self.loading_finished.emit(filename)
                self.item_selected_lazy.emit(filename, data)

            if 'piece' in self.directory.lower():
                data_manager.load_piece_lazy(filename, on_loaded, priority=5)
            else:
                data_manager.load_ability_lazy(filename, on_loaded, priority=5)

    def _on_file_data_loaded(self, filename: str, data: Dict[str, Any]):
        """Handle individual file data loaded"""
        if filename == self.currently_loading_file:
            self.currently_loading_file = None
            self.loading_finished.emit(filename)
            self.item_selected_lazy.emit(filename, data)

    def get_selected_filename(self) -> Optional[str]:
        """Get the currently selected filename"""
        current_index = self.currentIndex()
        if current_index > 0:
            return self.itemData(current_index)
        return None

    def set_selected_filename(self, filename: str):
        """Set selection by filename"""
        for i in range(self.count()):
            if self.itemData(i) == filename:
                self.setCurrentIndex(i)
                break

    def refresh_file_list(self):
        """Refresh the file list"""
        self.file_manager.metadata_loaded = False
        self.file_manager.file_metadata.clear()
        self._load_file_list()


class LazyListWidget(QListWidget):
    """
    Enhanced list widget with lazy loading support
    """

    item_selected_lazy = pyqtSignal(str, dict)  # filename, data
    loading_started = pyqtSignal(str)  # filename
    loading_finished = pyqtSignal(str)  # filename

    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension

        # Lazy loading components
        self.file_manager = LazyFileListManager(directory, file_extension, self)
        self.file_manager.register_list_widget(self)
        self.file_manager.files_loaded.connect(self._on_files_loaded)
        self.file_manager.file_data_loaded.connect(self._on_file_data_loaded)

        # Loading state
        self.is_loading_files = False
        self.currently_loading_file: Optional[str] = None

        # Connect selection change
        self.itemClicked.connect(self._on_item_clicked)

        # Start loading file list
        self._load_file_list()

    def _load_file_list(self):
        """Start loading file list in background"""
        if not self.is_loading_files:
            self.is_loading_files = True
            loading_item = QListWidgetItem("🔄 Loading files...")
            self.addItem(loading_item)
            self.file_manager.load_file_list_lazy()

    def _on_files_loaded(self, metadata_list: List[Dict[str, Any]]):
        """Handle file list loaded"""
        self.is_loading_files = False
        # UI will be updated by file_manager

    def _on_item_clicked(self, item: QListWidgetItem):
        """Handle item click with lazy loading"""
        if not item or item.text().startswith("🔄"):
            return

        filename = item.data(Qt.UserRole)
        if not filename:
            return

        # Check if already loaded
        data_manager = get_lazy_data_manager()
        if not hasattr(data_manager, 'is_loading') or not data_manager.is_loading(filename):
            self.currently_loading_file = filename
            self.loading_started.emit(filename)

            # Load file data lazily
            def on_loaded(data):
                self.currently_loading_file = None
                self.loading_finished.emit(filename)
                self.item_selected_lazy.emit(filename, data)

            if 'piece' in self.directory.lower():
                data_manager.load_piece_lazy(filename, on_loaded, priority=5)
            else:
                data_manager.load_ability_lazy(filename, on_loaded, priority=5)

    def _on_file_data_loaded(self, filename: str, data: Dict[str, Any]):
        """Handle individual file data loaded"""
        if filename == self.currently_loading_file:
            self.currently_loading_file = None
            self.loading_finished.emit(filename)
            self.item_selected_lazy.emit(filename, data)

    def get_selected_filename(self) -> Optional[str]:
        """Get the currently selected filename"""
        current_item = self.currentItem()
        if current_item:
            return current_item.data(Qt.UserRole)
        return None

    def refresh_file_list(self):
        """Refresh the file list"""
        self.file_manager.metadata_loaded = False
        self.file_manager.file_metadata.clear()
        self._load_file_list()


class LazyLoadingStatusWidget(QWidget):
    """
    Widget to display lazy loading status and controls
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = get_lazy_data_manager()
        self.setup_ui()

        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # Update every second

        # Add progress callback to data manager
        self.data_manager.add_progress_callback(self.on_progress_update)

    def setup_ui(self):
        """Setup the UI"""
        layout = QVBoxLayout()

        # Status frame
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_layout = QVBoxLayout()

        # Status labels
        self.pending_label = QLabel("Pending requests: 0")
        self.active_label = QLabel("Active callbacks: 0")
        self.cache_label = QLabel("Cache entries: 0")

        status_layout.addWidget(self.pending_label)
        status_layout.addWidget(self.active_label)
        status_layout.addWidget(self.cache_label)
        status_frame.setLayout(status_layout)
        layout.addWidget(status_frame)

        # Progress widget
        self.progress_widget = LoadingProgressWidget()
        self.progress_widget.hide()
        layout.addWidget(self.progress_widget)

        # Control buttons
        button_layout = QHBoxLayout()

        self.preload_btn = QPushButton("Preload Recent Files")
        self.preload_btn.clicked.connect(self.start_preloading)
        button_layout.addWidget(self.preload_btn)

        self.clear_cache_btn = QPushButton("Clear Cache")
        self.clear_cache_btn.clicked.connect(self.clear_cache)
        button_layout.addWidget(self.clear_cache_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def update_status(self):
        """Update status display"""
        try:
            # Get loading status (if available)
            if hasattr(self.data_manager, 'get_loading_status'):
                status = self.data_manager.get_loading_status()
                self.pending_label.setText(f"Pending requests: {status.get('pending_requests', 0)}")
                self.active_label.setText(f"Active callbacks: {status.get('active_callbacks', 0)}")

            # Get cache status (if available)
            if hasattr(self.data_manager, 'cache_manager') and hasattr(self.data_manager.cache_manager, 'get_cache_stats'):
                cache_stats = self.data_manager.cache_manager.get_cache_stats()
                total_entries = cache_stats.get('entries', {}).get('total', 0)
                self.cache_label.setText(f"Cache entries: {total_entries}")

        except Exception as e:
            logger.error(f"Error updating status: {e}")

    def on_progress_update(self, progress: int, message: str):
        """Handle progress updates"""
        if progress == 0:
            self.progress_widget.show_loading(message)
        elif progress == 100:
            self.progress_widget.hide_loading()
        else:
            self.progress_widget.update_progress(progress, message)

    def start_preloading(self):
        """Start preloading recent files"""
        try:
            self.preload_btn.setEnabled(False)
            self.progress_widget.show_loading("Starting preload...")

            # Start preloading
            self.data_manager.preload_recent_files(max_files=15)

            # Re-enable button after a delay
            QTimer.singleShot(2000, lambda: self.preload_btn.setEnabled(True))

        except Exception as e:
            logger.error(f"Error starting preload: {e}")
            self.preload_btn.setEnabled(True)
            self.progress_widget.hide_loading()

    def clear_cache(self):
        """Clear the cache"""
        try:
            if hasattr(self.data_manager, 'cache_manager'):
                self.data_manager.cache_manager.clear_all()
            self.update_status()

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")


class LazyFileSelector(QWidget):
    """
    Complete file selector widget with lazy loading and progress indication
    """

    file_selected = pyqtSignal(str, dict)  # filename, data

    def __init__(self, directory: str, title: str = "Select File", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.title = title
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI"""
        layout = QVBoxLayout()

        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        layout.addWidget(title_label)

        # Lazy combo box
        self.combo = LazyComboBox(self.directory)
        self.combo.item_selected_lazy.connect(self.file_selected)
        layout.addWidget(self.combo)

        # Loading indicator (initially hidden)
        self.loading_widget = LoadingProgressWidget()
        self.loading_widget.hide()
        layout.addWidget(self.loading_widget)

        # Connect loading signals
        self.combo.loading_started.connect(lambda f: self.loading_widget.show_loading(f"Loading {f}..."))
        self.combo.loading_finished.connect(lambda f: self.loading_widget.hide_loading())

        self.setLayout(layout)

    def refresh(self):
        """Refresh the file list"""
        self.combo.refresh_file_list()

    def get_selected_filename(self) -> Optional[str]:
        """Get currently selected filename"""
        return self.combo.get_selected_filename()
