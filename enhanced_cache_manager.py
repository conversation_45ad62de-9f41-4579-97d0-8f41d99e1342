#!/usr/bin/env python3
"""
Enhanced Cache Management System for Adventure Chess Creator
Implements cache size limits, automatic cleanup, memory monitoring, and file invalidation
"""

import os
import sys
import time
import psutil
import logging
import threading
from typing import Dict, Any, Optional, List, Tuple, Set, Callable
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import OrderedDict
from concurrent.futures import ThreadPoolExecutor, Future

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Represents a single cache entry with metadata"""
    data: Any
    access_time: datetime
    creation_time: datetime
    file_path: Optional[str] = None
    file_mtime: Optional[float] = None
    access_count: int = 0
    size_bytes: int = 0

class EnhancedCacheManager:
    """
    Enhanced cache management system with size limits, automatic cleanup, and monitoring
    """
    
    def __init__(self, 
                 max_cache_size_mb: int = 100,
                 max_entries: int = 1000,
                 cleanup_interval_seconds: int = 300,  # 5 minutes
                 memory_warning_threshold: float = 0.8,  # 80% of system memory
                 enable_file_watching: bool = True):
        
        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        self.max_entries = max_entries
        self.cleanup_interval = cleanup_interval_seconds
        self.memory_warning_threshold = memory_warning_threshold
        self.enable_file_watching = enable_file_watching
        
        # Cache storage using OrderedDict for LRU behavior
        self.piece_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.ability_cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # Cache statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'invalidations': 0,
            'memory_warnings': 0,
            'cleanup_runs': 0
        }
        
        # File watching for cache invalidation
        self.watched_files: Set[str] = set()
        self.file_mtimes: Dict[str, float] = {}
        
        # Background cleanup thread
        self.cleanup_thread: Optional[threading.Thread] = None
        self.cleanup_stop_event = threading.Event()
        
        # Memory monitoring
        self.last_memory_warning = datetime.min
        
        # Start background cleanup
        self._start_cleanup_thread()
    
    def get_piece(self, cache_key: str) -> Optional[Any]:
        """Get piece from cache with LRU update"""
        return self._get_from_cache(self.piece_cache, cache_key)
    
    def set_piece(self, cache_key: str, data: Any, file_path: Optional[str] = None) -> None:
        """Set piece in cache with metadata"""
        self._set_in_cache(self.piece_cache, cache_key, data, file_path)
    
    def get_ability(self, cache_key: str) -> Optional[Any]:
        """Get ability from cache with LRU update"""
        return self._get_from_cache(self.ability_cache, cache_key)
    
    def set_ability(self, cache_key: str, data: Any, file_path: Optional[str] = None) -> None:
        """Set ability in cache with metadata"""
        self._set_in_cache(self.ability_cache, cache_key, data, file_path)
    
    def _get_from_cache(self, cache: OrderedDict, cache_key: str) -> Optional[Any]:
        """Internal method to get data from cache"""
        if cache_key not in cache:
            self.stats['misses'] += 1
            return None
        
        # Check if file has been modified (cache invalidation)
        entry = cache[cache_key]
        if self._is_file_modified(entry):
            logger.debug(f"Cache invalidated for modified file: {cache_key}")
            del cache[cache_key]
            self.stats['invalidations'] += 1
            self.stats['misses'] += 1
            return None
        
        # Update access metadata and move to end (LRU)
        entry.access_time = datetime.now()
        entry.access_count += 1
        cache.move_to_end(cache_key)
        
        self.stats['hits'] += 1
        return entry.data
    
    def _set_in_cache(self, cache: OrderedDict, cache_key: str, data: Any, file_path: Optional[str] = None) -> None:
        """Internal method to set data in cache"""
        now = datetime.now()
        
        # Calculate approximate size
        size_bytes = self._estimate_size(data)
        
        # Get file modification time if file path provided
        file_mtime = None
        if file_path and os.path.exists(file_path):
            file_mtime = os.path.getmtime(file_path)
            self.watched_files.add(file_path)
            self.file_mtimes[file_path] = file_mtime
        
        # Create cache entry
        entry = CacheEntry(
            data=data,
            access_time=now,
            creation_time=now,
            file_path=file_path,
            file_mtime=file_mtime,
            access_count=1,
            size_bytes=size_bytes
        )
        
        # Remove existing entry if present
        if cache_key in cache:
            del cache[cache_key]
        
        # Add new entry
        cache[cache_key] = entry
        
        # Enforce cache limits
        self._enforce_cache_limits()
        
        # Check memory usage
        self._check_memory_usage()
    
    def _is_file_modified(self, entry: CacheEntry) -> bool:
        """Check if the file associated with cache entry has been modified"""
        if not entry.file_path or not entry.file_mtime:
            return False
        
        if not os.path.exists(entry.file_path):
            return True  # File was deleted
        
        current_mtime = os.path.getmtime(entry.file_path)
        return current_mtime != entry.file_mtime
    
    def _estimate_size(self, data: Any) -> int:
        """Estimate the memory size of cached data"""
        try:
            # Simple estimation based on string representation
            return len(str(data).encode('utf-8'))
        except:
            return 1024  # Default estimate
    
    def _enforce_cache_limits(self) -> None:
        """Enforce cache size and entry limits"""
        # Combine both caches for size calculation
        all_entries = list(self.piece_cache.values()) + list(self.ability_cache.values())
        total_size = sum(entry.size_bytes for entry in all_entries)
        total_entries = len(self.piece_cache) + len(self.ability_cache)
        
        # Check if we need to evict entries
        evictions_needed = 0
        
        if total_size > self.max_cache_size_bytes:
            evictions_needed = max(evictions_needed, len(all_entries) // 4)  # Evict 25%
        
        if total_entries > self.max_entries:
            evictions_needed = max(evictions_needed, total_entries - self.max_entries)
        
        if evictions_needed > 0:
            self._evict_lru_entries(evictions_needed)
    
    def _evict_lru_entries(self, count: int) -> None:
        """Evict least recently used entries"""
        # Collect all entries with their cache and key
        all_entries = []
        for key, entry in self.piece_cache.items():
            all_entries.append((entry.access_time, 'piece', key, entry))
        for key, entry in self.ability_cache.items():
            all_entries.append((entry.access_time, 'ability', key, entry))
        
        # Sort by access time (oldest first)
        all_entries.sort(key=lambda x: x[0])
        
        # Evict oldest entries
        evicted = 0
        for access_time, cache_type, key, entry in all_entries:
            if evicted >= count:
                break
            
            if cache_type == 'piece' and key in self.piece_cache:
                del self.piece_cache[key]
                evicted += 1
            elif cache_type == 'ability' and key in self.ability_cache:
                del self.ability_cache[key]
                evicted += 1
        
        self.stats['evictions'] += evicted
        logger.debug(f"Evicted {evicted} cache entries to enforce limits")
    
    def _check_memory_usage(self) -> None:
        """Check system memory usage and warn if high"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent / 100.0
            
            if memory_percent > self.memory_warning_threshold:
                # Only warn once per hour to avoid spam
                now = datetime.now()
                if now - self.last_memory_warning > timedelta(hours=1):
                    logger.warning(f"High memory usage detected: {memory_percent:.1%} of system memory in use")
                    self.stats['memory_warnings'] += 1
                    self.last_memory_warning = now
                    
                    # Aggressive cleanup if memory is very high
                    if memory_percent > 0.9:  # 90%
                        self._evict_lru_entries(len(self.piece_cache) // 2 + len(self.ability_cache) // 2)
                        
        except Exception as e:
            logger.debug(f"Could not check memory usage: {e}")
    
    def _start_cleanup_thread(self) -> None:
        """Start background cleanup thread"""
        if self.cleanup_thread is None or not self.cleanup_thread.is_alive():
            self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
            self.cleanup_thread.start()
            logger.debug("Started cache cleanup thread")
    
    def _cleanup_worker(self) -> None:
        """Background worker for periodic cache cleanup"""
        while not self.cleanup_stop_event.wait(self.cleanup_interval):
            try:
                self._periodic_cleanup()
            except Exception as e:
                logger.error(f"Error in cache cleanup worker: {e}")
    
    def _periodic_cleanup(self) -> None:
        """Perform periodic cache cleanup"""
        logger.debug("Running periodic cache cleanup")
        
        # Remove entries for deleted files
        self._cleanup_deleted_files()
        
        # Remove old entries (older than 1 hour with no recent access)
        self._cleanup_old_entries()
        
        # Check memory usage
        self._check_memory_usage()
        
        self.stats['cleanup_runs'] += 1
    
    def _cleanup_deleted_files(self) -> None:
        """Remove cache entries for files that no longer exist"""
        to_remove = []
        
        for key, entry in self.piece_cache.items():
            if entry.file_path and not os.path.exists(entry.file_path):
                to_remove.append(('piece', key))
        
        for key, entry in self.ability_cache.items():
            if entry.file_path and not os.path.exists(entry.file_path):
                to_remove.append(('ability', key))
        
        for cache_type, key in to_remove:
            if cache_type == 'piece':
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats['invalidations'] += 1
        
        if to_remove:
            logger.debug(f"Removed {len(to_remove)} cache entries for deleted files")
    
    def _cleanup_old_entries(self) -> None:
        """Remove old, unused cache entries"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        to_remove = []
        
        for key, entry in self.piece_cache.items():
            if entry.access_time < cutoff_time and entry.access_count == 1:
                to_remove.append(('piece', key))
        
        for key, entry in self.ability_cache.items():
            if entry.access_time < cutoff_time and entry.access_count == 1:
                to_remove.append(('ability', key))
        
        for cache_type, key in to_remove:
            if cache_type == 'piece':
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats['evictions'] += 1
        
        if to_remove:
            logger.debug(f"Removed {len(to_remove)} old cache entries")
    
    def invalidate_file(self, file_path: str) -> None:
        """Manually invalidate cache entries for a specific file"""
        to_remove = []
        
        for key, entry in self.piece_cache.items():
            if entry.file_path == file_path:
                to_remove.append(('piece', key))
        
        for key, entry in self.ability_cache.items():
            if entry.file_path == file_path:
                to_remove.append(('ability', key))
        
        for cache_type, key in to_remove:
            if cache_type == 'piece':
                del self.piece_cache[key]
            else:
                del self.ability_cache[key]
            self.stats['invalidations'] += 1
        
        if to_remove:
            logger.debug(f"Invalidated {len(to_remove)} cache entries for file: {file_path}")
    
    def clear_all(self) -> None:
        """Clear all cache entries"""
        piece_count = len(self.piece_cache)
        ability_count = len(self.ability_cache)
        
        self.piece_cache.clear()
        self.ability_cache.clear()
        
        logger.info(f"Cleared all cache entries: {piece_count} pieces, {ability_count} abilities")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        piece_size = sum(entry.size_bytes for entry in self.piece_cache.values())
        ability_size = sum(entry.size_bytes for entry in self.ability_cache.values())
        total_size = piece_size + ability_size
        
        hit_rate = 0.0
        total_requests = self.stats['hits'] + self.stats['misses']
        if total_requests > 0:
            hit_rate = self.stats['hits'] / total_requests
        
        return {
            'entries': {
                'pieces': len(self.piece_cache),
                'abilities': len(self.ability_cache),
                'total': len(self.piece_cache) + len(self.ability_cache)
            },
            'size': {
                'pieces_bytes': piece_size,
                'abilities_bytes': ability_size,
                'total_bytes': total_size,
                'total_mb': total_size / (1024 * 1024),
                'max_mb': self.max_cache_size_bytes / (1024 * 1024)
            },
            'performance': {
                'hit_rate': hit_rate,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'total_requests': total_requests
            },
            'maintenance': {
                'evictions': self.stats['evictions'],
                'invalidations': self.stats['invalidations'],
                'cleanup_runs': self.stats['cleanup_runs'],
                'memory_warnings': self.stats['memory_warnings']
            },
            'limits': {
                'max_entries': self.max_entries,
                'max_size_mb': self.max_cache_size_bytes / (1024 * 1024),
                'cleanup_interval_seconds': self.cleanup_interval
            }
        }
    
    def shutdown(self) -> None:
        """Shutdown the cache manager and cleanup resources"""
        self.cleanup_stop_event.set()
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        self.clear_all()
        logger.info("Cache manager shutdown complete")

# Global cache manager instance
cache_manager = EnhancedCacheManager()

def get_cache_manager() -> EnhancedCacheManager:
    """Get the global cache manager instance"""
    return cache_manager

class CacheIntegratedDataManager:
    """
    Enhanced data manager that integrates with the cache management system
    Drop-in replacement for PydanticDataManager with enhanced caching
    """

    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.error_log: List[str] = []

    def load_piece(self, filename: str) -> Tuple[Optional[Any], Optional[str]]:
        """Load piece with enhanced caching"""
        try:
            from config import PIECES_DIR, PIECE_EXTENSION

            # Normalize filename
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION

            filepath = Path(PIECES_DIR) / filename
            cache_key = str(filepath)

            if not filepath.exists():
                return None, f"Piece file not found: {filename}"

            # Check enhanced cache first
            cached_data = self.cache_manager.get_piece(cache_key)
            if cached_data is not None:
                logger.debug(f"Loaded piece from enhanced cache: {filename}")
                return cached_data, None

            # Load from file
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Cache with file path for invalidation
            self.cache_manager.set_piece(cache_key, data, str(filepath))

            logger.info(f"Successfully loaded and cached piece: {filename}")
            return data, None

        except Exception as e:
            error_msg = f"Error loading piece {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    def save_piece(self, piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save piece and update cache"""
        try:
            from config import PIECES_DIR, PIECE_EXTENSION

            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(piece_data.get('name', 'unnamed'))

            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION

            filepath = Path(PIECES_DIR) / filename
            cache_key = str(filepath)

            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(piece_data, f, indent=2, ensure_ascii=False)

            # Update cache
            self.cache_manager.set_piece(cache_key, piece_data, str(filepath))

            logger.info(f"Successfully saved and cached piece: {filename}")
            return True, None

        except Exception as e:
            error_msg = f"Error saving piece: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg

    def load_ability(self, filename: str) -> Tuple[Optional[Any], Optional[str]]:
        """Load ability with enhanced caching"""
        try:
            from config import ABILITIES_DIR, ABILITY_EXTENSION

            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename
            cache_key = str(filepath)

            if not filepath.exists():
                return None, f"Ability file not found: {filename}"

            # Check enhanced cache first
            cached_data = self.cache_manager.get_ability(cache_key)
            if cached_data is not None:
                logger.debug(f"Loaded ability from enhanced cache: {filename}")
                return cached_data, None

            # Load from file
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Cache with file path for invalidation
            self.cache_manager.set_ability(cache_key, data, str(filepath))

            logger.info(f"Successfully loaded and cached ability: {filename}")
            return data, None

        except Exception as e:
            error_msg = f"Error loading ability {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg

    def save_ability(self, ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save ability and update cache"""
        try:
            from config import ABILITIES_DIR, ABILITY_EXTENSION

            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(ability_data.get('name', 'unnamed'))

            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename
            cache_key = str(filepath)

            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(ability_data, f, indent=2, ensure_ascii=False)

            # Update cache
            self.cache_manager.set_ability(cache_key, ability_data, str(filepath))

            logger.info(f"Successfully saved and cached ability: {filename}")
            return True, None

        except Exception as e:
            error_msg = f"Error saving ability: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg

    def _sanitize_filename(self, name: str) -> str:
        """Sanitize a name for use as a filename"""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')
        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed"
        return sanitized

    def clear_cache(self):
        """Clear all cached data"""
        self.cache_manager.clear_all()
        logger.info("Enhanced cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return self.cache_manager.get_cache_stats()

    def get_error_log(self) -> List[str]:
        """Get list of all errors that occurred"""
        return self.error_log.copy()

    def clear_error_log(self):
        """Clear the error log"""
        self.error_log.clear()
