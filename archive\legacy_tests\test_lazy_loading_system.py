#!/usr/bin/env python3
"""
Comprehensive test suite for Lazy Loading System
Tests lazy loading functionality, performance, and integration
"""

import os
import sys
import json
import time
import tempfile
import unittest
import threading
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enhancements.performance import (
    LazyDataManager, LazyLoadRequest, LoadingProgressWidget,
    LazyIntegratedDataManager, LazyFileListManager, get_cache_manager
)

class TestLazyLoadingSystem(unittest.TestCase):
    """Test the core lazy loading system"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = get_cache_manager()
        self.lazy_manager = LazyDataManager(self.cache_manager)
        
        # Create test files
        self.test_files = {}
        for i in range(5):
            filename = f"test_file_{i}.json"
            file_path = Path(self.temp_dir) / filename
            test_data = {
                "name": f"Test Item {i}",
                "description": f"Test description {i}",
                "value": i * 10
            }
            
            with open(file_path, 'w') as f:
                json.dump(test_data, f)
            
            self.test_files[filename] = test_data
    
    def tearDown(self):
        """Clean up test environment"""
        self.lazy_manager.shutdown()
        
        # Clean up temp files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_metadata_loading(self):
        """Test lazy file metadata loading"""
        metadata = self.lazy_manager.load_file_metadata_lazy(self.temp_dir)
        
        self.assertEqual(len(metadata), 5)
        
        for meta in metadata:
            self.assertIn("filename", meta)
            self.assertIn("full_path", meta)
            self.assertIn("size_bytes", meta)
            self.assertIn("modified_time", meta)
            self.assertIn("is_loaded", meta)
            self.assertIn("has_error", meta)
    
    def test_lazy_data_loading(self):
        """Test lazy data loading with callbacks"""
        loaded_data = {}
        loading_complete = threading.Event()
        
        def load_test_file():
            file_path = Path(self.temp_dir) / "test_file_0.json"
            with open(file_path, 'r') as f:
                return json.load(f)
        
        def on_loaded(data):
            loaded_data['result'] = data
            loading_complete.set()
        
        # Test lazy loading
        result = self.lazy_manager.load_data_lazy(
            "test_file_0",
            load_test_file,
            on_loaded
        )
        
        # Should return None initially (loading in background)
        self.assertIsNone(result)
        
        # Wait for loading to complete
        self.assertTrue(loading_complete.wait(timeout=5.0))
        
        # Check loaded data
        self.assertIn('result', loaded_data)
        self.assertEqual(loaded_data['result']['name'], "Test Item 0")
    
    def test_cache_integration(self):
        """Test integration with cache manager"""
        def load_test_file():
            file_path = Path(self.temp_dir) / "test_file_1.json"
            with open(file_path, 'r') as f:
                return json.load(f)
        
        # First load - should cache the data
        result1 = self.lazy_manager._load_with_caching("test_piece_1", load_test_file)
        
        # Second load - should return cached data
        cached_data = self.cache_manager.get_piece("test_piece_1")
        
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data['name'], "Test Item 1")
    
    def test_preloading(self):
        """Test preloading functionality"""
        progress_updates = []
        
        def progress_callback(progress, message):
            progress_updates.append((progress, message))
        
        # Create load functions
        load_functions = {}
        for filename in self.test_files.keys():
            def make_loader(fname):
                def loader():
                    file_path = Path(self.temp_dir) / fname
                    with open(file_path, 'r') as f:
                        return json.load(f)
                return loader
            
            load_functions[filename] = make_loader(filename)
        
        # Start preloading
        file_list = list(self.test_files.keys())
        self.lazy_manager.preload_files(file_list, load_functions, progress_callback)
        
        # Wait a bit for preloading
        time.sleep(2.0)
        
        # Check that progress updates were received
        self.assertGreater(len(progress_updates), 0)
    
    def test_loading_status(self):
        """Test loading status tracking"""
        def slow_load_function():
            time.sleep(0.5)
            return {"test": "data"}
        
        # Start a slow loading operation
        self.lazy_manager.load_data_lazy("slow_test", slow_load_function)
        
        # Check status immediately
        status = self.lazy_manager.get_loading_status()
        self.assertGreater(status['pending_requests'], 0)
        
        # Wait for completion
        time.sleep(1.0)
        
        # Check status after completion
        status = self.lazy_manager.get_loading_status()
        self.assertEqual(status['pending_requests'], 0)

class TestLazyIntegration(unittest.TestCase):
    """Test lazy loading integration with data managers"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_pieces_dir = tempfile.mkdtemp(prefix="pieces_")
        self.temp_abilities_dir = tempfile.mkdtemp(prefix="abilities_")
        
        # Mock config directories only (extensions are constants)
        self.config_patch = patch.multiple(
            'config',
            PIECES_DIR=self.temp_pieces_dir,
            ABILITIES_DIR=self.temp_abilities_dir
        )

        # Also patch the lazy_data_integration module constants
        self.lazy_patch = patch.multiple(
            'lazy_data_integration',
            PIECES_DIR=self.temp_pieces_dir,
            ABILITIES_DIR=self.temp_abilities_dir,
            PIECE_EXTENSION='.json',
            ABILITY_EXTENSION='.json'
        )
        self.config_patch.start()
        self.lazy_patch.start()

        # Create test data
        self.create_test_data()

        # Reset and create fresh data manager for each test
        from lazy_data_integration import reset_lazy_data_manager, get_lazy_data_manager
        reset_lazy_data_manager()
        self.data_manager = get_lazy_data_manager()
    
    def tearDown(self):
        """Clean up test environment"""
        try:
            self.data_manager.shutdown()
        except Exception as e:
            print(f"Warning: Error during data manager shutdown: {e}")

        # Reset global manager
        from lazy_data_integration import reset_lazy_data_manager
        reset_lazy_data_manager()

        self.config_patch.stop()
        self.lazy_patch.stop()

        # Clean up temp files
        import shutil
        shutil.rmtree(self.temp_pieces_dir, ignore_errors=True)
        shutil.rmtree(self.temp_abilities_dir, ignore_errors=True)
    
    def create_test_data(self):
        """Create test piece and ability files"""
        # Create test pieces
        for i in range(3):
            piece_data = {
                "name": f"Test Piece {i}",
                "description": f"Test piece description {i}",
                "movement": "standard",
                "abilities": []
            }
            
            file_path = Path(self.temp_pieces_dir) / f"test_piece_{i}.json"
            with open(file_path, 'w') as f:
                json.dump(piece_data, f)
        
        # Create test abilities
        for i in range(3):
            ability_data = {
                "name": f"Test Ability {i}",
                "description": f"Test ability description {i}",
                "tags": [f"tag_{i}"],
                "config": {}
            }
            
            file_path = Path(self.temp_abilities_dir) / f"test_ability_{i}.json"
            with open(file_path, 'w') as f:
                json.dump(ability_data, f)
    
    def test_lazy_piece_loading(self):
        """Test lazy piece loading"""
        loaded_data = {}
        loading_complete = threading.Event()
        
        def on_loaded(data):
            loaded_data['piece'] = data
            loading_complete.set()
        
        # Load piece lazily
        result = self.data_manager.load_piece_lazy("test_piece_0", on_loaded)
        
        # Should return None initially (loading in background)
        self.assertIsNone(result)
        
        # Wait for loading
        self.assertTrue(loading_complete.wait(timeout=5.0))
        
        # Check loaded data
        self.assertIn('piece', loaded_data)
        self.assertEqual(loaded_data['piece']['name'], "Test Piece 0")
    
    def test_lazy_ability_loading(self):
        """Test lazy ability loading"""
        loaded_data = {}
        loading_complete = threading.Event()
        
        def on_loaded(data):
            loaded_data['ability'] = data
            loading_complete.set()
        
        # Load ability lazily
        result = self.data_manager.load_ability_lazy("test_ability_0", on_loaded)
        
        # Should return None initially (loading in background)
        self.assertIsNone(result)
        
        # Wait for loading
        self.assertTrue(loading_complete.wait(timeout=5.0))
        
        # Check loaded data
        self.assertIn('ability', loaded_data)
        self.assertEqual(loaded_data['ability']['name'], "Test Ability 0")
    
    def test_file_list_managers(self):
        """Test file list managers"""
        # Test pieces manager
        pieces_manager = self.data_manager.get_pieces_manager()
        self.assertIsNotNone(pieces_manager)
        
        # Test abilities manager
        abilities_manager = self.data_manager.get_abilities_manager()
        self.assertIsNotNone(abilities_manager)
    
    def test_backward_compatibility(self):
        """Test backward compatibility with synchronous methods"""
        # Test synchronous piece loading
        piece_data, error = self.data_manager.load_piece("test_piece_1")
        self.assertIsNotNone(piece_data)
        self.assertIsNone(error)
        self.assertEqual(piece_data['name'], "Test Piece 1")
        
        # Test synchronous ability loading
        ability_data, error = self.data_manager.load_ability("test_ability_1")
        self.assertIsNotNone(ability_data)
        self.assertIsNone(error)
        self.assertEqual(ability_data['name'], "Test Ability 1")

def run_performance_test():
    """Run performance comparison between eager and lazy loading"""
    print("\n" + "="*60)
    print("LAZY LOADING PERFORMANCE TEST")
    print("="*60)
    
    # Create temporary test data
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create many test files
        num_files = 50
        print(f"Creating {num_files} test files...")
        
        for i in range(num_files):
            file_path = Path(temp_dir) / f"test_file_{i}.json"
            test_data = {
                "name": f"Test Item {i}",
                "description": f"A test item with index {i}" * 10,  # Make it larger
                "properties": {f"prop_{j}": j * i for j in range(20)},
                "large_array": list(range(100))
            }
            
            with open(file_path, 'w') as f:
                json.dump(test_data, f, indent=2)
        
        # Test eager loading
        print("\nTesting eager loading...")
        start_time = time.time()
        
        eager_data = []
        for i in range(num_files):
            file_path = Path(temp_dir) / f"test_file_{i}.json"
            with open(file_path, 'r') as f:
                eager_data.append(json.load(f))
        
        eager_time = time.time() - start_time
        print(f"Eager loading time: {eager_time:.3f} seconds")
        
        # Test lazy loading (metadata only)
        print("\nTesting lazy loading (metadata only)...")
        start_time = time.time()
        
        cache_manager = get_cache_manager()
        lazy_manager = LazyDataManager(cache_manager)
        
        metadata = lazy_manager.load_file_metadata_lazy(temp_dir)
        
        lazy_time = time.time() - start_time
        print(f"Lazy loading time: {lazy_time:.3f} seconds")
        print(f"Performance improvement: {(eager_time / lazy_time):.1f}x faster")
        
        # Test memory usage
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        print(f"Memory usage: {memory_info.rss / 1024 / 1024:.1f} MB")
        
        lazy_manager.shutdown()
        
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)

if __name__ == '__main__':
    # Run unit tests
    print("Running Lazy Loading System Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()
    
    print("\n" + "="*60)
    print("ALL TESTS COMPLETED")
    print("="*60)
    print("\nLazy Loading System is ready for integration!")
    print("Key benefits:")
    print("- Faster startup times")
    print("- Reduced memory usage")
    print("- Better UI responsiveness")
    print("- Background loading with progress indicators")
    print("- Seamless cache integration")
