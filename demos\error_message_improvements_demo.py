"""
Error Message Improvements Demo for Adventure Chess Creator

This demo showcases the enhanced error handling system with:
- User-friendly error messages instead of technical jargon
- Contextual help and suggested solutions
- Quick fix actions for common problems
- Visual error categorization
- Expandable technical details

Run this demo to see the improvements in action.
"""

import sys
import os
import json
import tempfile
from pathlib import Path
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt6.QtCore import Qt

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from user_friendly_error_system import (
    show_user_friendly_error, show_contextual_help,
    ErrorMessageTranslator, UserFriendlyErrorDialog
)
from error_message_improvements import (
    improved_error_handler, show_improved_error,
    get_error_template
)

class ErrorDemoWindow(QMainWindow):
    """Demo window showing error message improvements"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Adventure Chess Creator - Error Message Improvements Demo")
        self.setGeometry(100, 100, 800, 600)
        
        # Create temporary files for testing
        self.temp_dir = tempfile.mkdtemp()
        self.setup_test_files()
        
        self.setup_ui()
    
    def setup_test_files(self):
        """Setup test files for demonstrations"""
        # Create a corrupted JSON file
        self.corrupted_file = os.path.join(self.temp_dir, "corrupted.json")
        with open(self.corrupted_file, 'w') as f:
            f.write('{"invalid": json content missing bracket')
        
        # Create a read-only file
        self.readonly_file = os.path.join(self.temp_dir, "readonly.json")
        with open(self.readonly_file, 'w') as f:
            json.dump({"test": "data"}, f)
        os.chmod(self.readonly_file, 0o444)  # Read-only
        
        # Missing file path
        self.missing_file = os.path.join(self.temp_dir, "missing.json")
    
    def setup_ui(self):
        """Setup the demo UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Error Message Improvements Demo")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Description
        description = QLabel(
            "Click the buttons below to see how technical error messages are transformed "
            "into user-friendly dialogs with helpful suggestions and quick fixes."
        )
        description.setWordWrap(True)
        description.setStyleSheet("margin: 10px; color: #666;")
        layout.addWidget(description)
        
        # Demo buttons
        self.create_demo_button(layout, "File Permission Error", self.demo_permission_error,
                               "Simulate trying to write to a read-only file")
        
        self.create_demo_button(layout, "File Not Found Error", self.demo_file_not_found,
                               "Simulate trying to load a missing file")
        
        self.create_demo_button(layout, "JSON Format Error", self.demo_json_error,
                               "Simulate loading a corrupted JSON file")
        
        self.create_demo_button(layout, "Validation Error", self.demo_validation_error,
                               "Simulate data validation failure")
        
        self.create_demo_button(layout, "Network Error", self.demo_network_error,
                               "Simulate connection timeout")
        
        self.create_demo_button(layout, "Show Contextual Help", self.demo_contextual_help,
                               "Show contextual help for file permissions")
        
        # Comparison section
        comparison_label = QLabel("Comparison: Old vs New Error Messages")
        comparison_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-top: 20px;")
        layout.addWidget(comparison_label)
        
        self.comparison_text = QTextEdit()
        self.comparison_text.setMaximumHeight(150)
        self.comparison_text.setStyleSheet("font-family: 'Courier New'; font-size: 10px;")
        layout.addWidget(self.comparison_text)
        
        # Show initial comparison
        self.show_error_comparison()
    
    def create_demo_button(self, layout, title, handler, description):
        """Create a demo button with description"""
        button = QPushButton(title)
        button.clicked.connect(handler)
        button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(button)
        
        desc_label = QLabel(f"   {description}")
        desc_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 10px;")
        layout.addWidget(desc_label)
    
    def demo_permission_error(self):
        """Demo permission error handling"""
        try:
            # Try to write to read-only file
            with open(self.readonly_file, 'w') as f:
                json.dump({"new": "data"}, f)
        except PermissionError as e:
            show_improved_error(e, "saving piece data", self.readonly_file, self)
    
    def demo_file_not_found(self):
        """Demo file not found error handling"""
        try:
            # Try to load missing file
            with open(self.missing_file, 'r') as f:
                json.load(f)
        except FileNotFoundError as e:
            show_improved_error(e, "loading ability data", self.missing_file, self)
    
    def demo_json_error(self):
        """Demo JSON format error handling"""
        try:
            # Try to load corrupted file
            with open(self.corrupted_file, 'r') as f:
                json.load(f)
        except json.JSONDecodeError as e:
            show_improved_error(e, "parsing piece configuration", self.corrupted_file, self)
    
    def demo_validation_error(self):
        """Demo validation error handling"""
        error = ValueError("Validation failed: 'name' field is required and must be 1-50 characters")
        show_improved_error(error, "validating piece data", "", self)
    
    def demo_network_error(self):
        """Demo network error handling"""
        error = ConnectionError("Connection timeout: Unable to reach update server")
        show_improved_error(error, "checking for updates", "", self)
    
    def demo_contextual_help(self):
        """Demo contextual help system"""
        show_contextual_help("file_permissions", self)
    
    def show_error_comparison(self):
        """Show comparison between old and new error messages"""
        comparison_text = """
ERROR MESSAGE COMPARISON:

OLD (Technical):
❌ PermissionError: [Errno 13] Permission denied: '/path/to/file.json'
❌ FileNotFoundError: [Errno 2] No such file or directory: '/path/to/missing.json'
❌ JSONDecodeError: Expecting ',' delimiter: line 1 column 15 (char 14)

NEW (User-Friendly):
✅ File Access Problem: Cannot access the file. This usually happens when the file 
   is being used by another program or you don't have permission to modify it.
   
   Suggestions:
   • Close any other programs that might be using this file
   • Check if the file is read-only and change its properties
   • Try running Adventure Chess Creator as administrator
   
   Quick Fixes: [Try Different Location] [Check File Properties]

✅ File Not Found: The file cannot be found. It may have been moved, renamed, or deleted.
   
   Suggestions:
   • Check if the file was moved to a different folder
   • Look in the Recycle Bin if it was accidentally deleted
   • Try browsing for the file manually
   
   Quick Fixes: [Browse for File] [Create New File]

✅ File Format Problem: The file appears to be corrupted or in an invalid format.
   
   Suggestions:
   • Try opening the file in a text editor to check for obvious problems
   • Restore from a backup copy if you have one
   • Create a new file and re-enter the data
   
   Quick Fixes: [Open in Text Editor] [Restore from Backup] [Create New]
        """
        self.comparison_text.setPlainText(comparison_text.strip())
    
    def closeEvent(self, event):
        """Cleanup when closing"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        event.accept()

def run_demo():
    """Run the error message improvements demo"""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show demo window
    demo_window = ErrorDemoWindow()
    demo_window.show()
    
    print("🚀 Error Message Improvements Demo")
    print("=" * 50)
    print("This demo shows how technical error messages are transformed")
    print("into user-friendly dialogs with helpful suggestions.")
    print()
    print("Features demonstrated:")
    print("• User-friendly error titles and messages")
    print("• Contextual suggestions for resolving issues")
    print("• Quick fix buttons for common actions")
    print("• Expandable technical details for developers")
    print("• Visual categorization with appropriate icons")
    print("• Contextual help system")
    print()
    print("Click the buttons in the demo window to see each error type!")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    run_demo()
