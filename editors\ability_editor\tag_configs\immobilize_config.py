"""
Immobilize tag configuration for ability editor.
Handles immobilize ability configurations with duration and target selection.
"""

from PyQt6.QtWidgets import (QFormLayout, QCheckBox, QSpinBox, QHBoxLayout,
                            QWidget, QLabel, QVBoxLayout)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class ImmobilizeConfig(BaseTagConfig):
    """Configuration for immobilize tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "immobilize")
        # Initialize immobilize targets list
        self.immobilize_targets = []
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for immobilize configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting immobilize UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Prevent a piece from moving or acting for a set number of turns.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Duration with checkbox
            duration_layout = QHBoxLayout()
            immobilize_duration_check = QCheckBox("Duration:")
            immobilize_duration_check.setChecked(True)
            self.store_widget("immobilize_duration_check", immobilize_duration_check)
            duration_layout.addWidget(immobilize_duration_check)
            
            immobilize_duration_spin = QSpinBox()
            immobilize_duration_spin.setRange(1, 20)
            immobilize_duration_spin.setValue(2)
            immobilize_duration_spin.setSuffix(" turns")
            immobilize_duration_spin.setToolTip("Duration of immobilization")
            self.store_widget("immobilize_duration_spin", immobilize_duration_spin)
            duration_layout.addWidget(immobilize_duration_spin)
            duration_layout.addStretch()
            form_layout.addRow("", duration_layout)

            # Connect duration checkbox
            immobilize_duration_check.stateChanged.connect(self._on_immobilize_duration_changed)

            # Enhanced inline piece selector for immobilize targets
            immobilize_target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("immobilize_target_selector", immobilize_target_selector)
            form_layout.addRow("", immobilize_target_selector)

            layout.addLayout(form_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Immobilize UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def _on_immobilize_duration_changed(self):
        """Handle duration checkbox state change."""
        try:
            duration_check = self.get_widget_by_name("immobilize_duration_check")
            duration_spin = self.get_widget_by_name("immobilize_duration_spin")
            
            if duration_check and duration_spin:
                duration_spin.setEnabled(duration_check.isChecked())
                
        except Exception as e:
            self.log_error(f"Error handling immobilize duration change: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating immobilize data")

            # Populate duration
            duration_check = self.get_widget_by_name("immobilize_duration_check")
            duration_spin = self.get_widget_by_name("immobilize_duration_spin")
            if "immobilizeDuration" in data:
                if duration_check:
                    duration_check.setChecked(True)
                if duration_spin:
                    duration_spin.setValue(data["immobilizeDuration"])
                    duration_spin.setEnabled(True)

            # Populate target pieces
            target_selector = self.get_widget_by_name("immobilize_target_selector")
            if target_selector and "immobilizeTargets" in data:
                target_selector.set_pieces(data["immobilizeTargets"])

            self.log_debug("Immobilize data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting immobilize data")
            data = {}

            # Collect duration
            duration_check = self.get_widget_by_name("immobilize_duration_check")
            duration_spin = self.get_widget_by_name("immobilize_duration_spin")
            if duration_check and duration_check.isChecked() and duration_spin:
                data["immobilizeDuration"] = duration_spin.value()

            # Collect target pieces
            target_selector = self.get_widget_by_name("immobilize_target_selector")
            if target_selector:
                targets = target_selector.get_pieces()
                if targets:
                    data["immobilizeTargets"] = targets

            self.log_debug("Immobilize data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
