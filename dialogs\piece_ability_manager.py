"""
Piece Ability Manager for Adventure Chess
Simplified ability reference management for pieces - no ability editing, just references
"""
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, 
    QListWidgetItem, QGroupBox, QMessageBox, QTextEdit
)
from PyQt6.QtCore import Qt

import os
from config import ABILITIES_DIR
from utils.simple_bridge import simple_bridge


class AbilitySummaryWidget(QTextEdit):
    """Widget to display a read-only summary of an ability"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setMaximumHeight(120)
        self.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                font-size: 11px;
            }
        """)
    
    def show_ability_summary(self, ability_data):
        """Display a summary of the ability"""
        if not ability_data:
            self.setPlainText("No ability selected")
            return
        
        name = ability_data.get('name', 'Unnamed Ability')
        description = ability_data.get('description', 'No description')
        tags = ability_data.get('tags', [])
        cost = ability_data.get('cost', 0)
        
        summary = f"📋 {name}\n\n"
        summary += f"Description: {description}\n\n"
        summary += f"Tags: {', '.join(tags) if tags else 'None'}\n"
        summary += f"Cost: {cost} points"
        
        self.setPlainText(summary)


class PieceAbilityManagerDialog(QDialog):
    """
    Simplified ability manager for pieces - only manages references, no editing
    """
    
    def __init__(self, parent=None, piece_abilities=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Piece Abilities - Adventure Chess")
        self.setMinimumSize(800, 600)
        self.parent = parent
        
        # Current piece abilities (list of ability file references)
        self.piece_abilities = piece_abilities.copy() if piece_abilities else []
        
        self.init_ui()
        self.refresh_ability_lists()
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel(
            "Manage abilities for this piece. Select from existing abilities or create new ones in the Ability Editor."
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("font-weight: bold; padding: 10px; background: #e3f2fd; border-radius: 4px;")
        layout.addWidget(instructions)
        
        # Main content in horizontal layout
        main_layout = QHBoxLayout()
        
        # Left side: Available abilities
        left_group = QGroupBox("Available Abilities")
        left_layout = QVBoxLayout()
        
        # Refresh button
        refresh_btn = QPushButton("🔄 Refresh List")
        refresh_btn.clicked.connect(self.refresh_ability_lists)
        left_layout.addWidget(refresh_btn)
        
        # Available abilities list
        self.available_list = QListWidget()
        self.available_list.setMinimumHeight(200)
        self.available_list.itemSelectionChanged.connect(self.on_available_selection_changed)
        left_layout.addWidget(self.available_list)
        
        # Create new ability button
        create_btn = QPushButton("➕ Create New Ability")
        create_btn.clicked.connect(self.create_new_ability)
        create_btn.setStyleSheet("QPushButton { background-color: #4caf50; color: white; font-weight: bold; }")
        left_layout.addWidget(create_btn)
        
        left_group.setLayout(left_layout)
        main_layout.addWidget(left_group)
        
        # Center: Action buttons
        center_layout = QVBoxLayout()
        center_layout.addStretch()
        
        self.add_btn = QPushButton("➡️ Add to Piece")
        self.add_btn.clicked.connect(self.add_ability_to_piece)
        self.add_btn.setEnabled(False)
        center_layout.addWidget(self.add_btn)
        
        self.remove_btn = QPushButton("⬅️ Remove from Piece")
        self.remove_btn.clicked.connect(self.remove_ability_from_piece)
        self.remove_btn.setEnabled(False)
        center_layout.addWidget(self.remove_btn)
        
        center_layout.addStretch()
        
        # Edit ability button
        self.edit_btn = QPushButton("✏️ Edit Selected Ability")
        self.edit_btn.clicked.connect(self.edit_selected_ability)
        self.edit_btn.setEnabled(False)
        center_layout.addWidget(self.edit_btn)
        
        center_layout.addStretch()
        main_layout.addLayout(center_layout)
        
        # Right side: Piece abilities
        right_group = QGroupBox("Piece Abilities")
        right_layout = QVBoxLayout()
        
        # Piece abilities list
        self.piece_list = QListWidget()
        self.piece_list.setMinimumHeight(200)
        self.piece_list.itemSelectionChanged.connect(self.on_piece_selection_changed)
        right_layout.addWidget(self.piece_list)
        
        right_group.setLayout(right_layout)
        main_layout.addWidget(right_group)
        
        layout.addLayout(main_layout)
        
        # Ability summary section
        summary_group = QGroupBox("Ability Summary")
        summary_layout = QVBoxLayout()
        
        self.ability_summary = AbilitySummaryWidget()
        summary_layout.addWidget(self.ability_summary)
        
        summary_group.setLayout(summary_layout)
        layout.addWidget(summary_group)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        ok_btn = QPushButton("Apply Changes")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        ok_btn.setStyleSheet("QPushButton { background-color: #2196f3; color: white; font-weight: bold; }")
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        
        # Error display
        self.error_label = QLabel()
        self.error_label.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(self.error_label)
        
        self.setLayout(layout)
    
    def refresh_ability_lists(self):
        """Refresh both available and piece ability lists"""
        self.error_label.setText("")

        # Refresh available abilities
        self.available_list.clear()
        available_abilities = simple_bridge.list_abilities()

        for ability_name in available_abilities:
            # Try to load ability to get its display name
            ability_data, error = simple_bridge.load_ability_for_ui(ability_name)
            if ability_data and not error:
                display_name = ability_data.get('name', ability_name)
                tags = ability_data.get('tags', [])
                tags_str = f" [{', '.join(tags[:3])}{'...' if len(tags) > 3 else ''}]" if tags else ""

                item_text = f"{display_name}{tags_str}"
                item = QListWidgetItem(item_text)
                item.setToolTip(f"File: {ability_name}\nTags: {', '.join(tags) if tags else 'None'}")
            else:
                item = QListWidgetItem(f"❌ {ability_name} (Error)")
                item.setToolTip(f"Error loading: {error}")

            item.setData(Qt.ItemDataRole.UserRole, ability_name)
            self.available_list.addItem(item)

        # Refresh piece abilities
        self.refresh_piece_abilities()
    
    def refresh_piece_abilities(self):
        """Refresh the piece abilities list"""
        self.piece_list.clear()

        for ability_ref in self.piece_abilities:
            # Only support simple string references (legacy formats removed)
            if isinstance(ability_ref, str):
                ability_data, error = simple_bridge.load_ability_for_ui(ability_ref)
                if ability_data and not error:
                    display_name = ability_data.get('name', ability_ref)
                    tags = ability_data.get('tags', [])
                    tags_str = f" [{', '.join(tags[:2])}{'...' if len(tags) > 2 else ''}]" if tags else ""

                    item_text = f"{display_name}{tags_str}"
                    item = QListWidgetItem(item_text)
                    item.setToolTip(f"File: {ability_ref}\nTags: {', '.join(tags) if tags else 'None'}")
                else:
                    item = QListWidgetItem(f"❌ {ability_ref} (Error)")
                    item.setToolTip(f"Error loading: {error}")

                item.setData(Qt.ItemDataRole.UserRole, ability_ref)
                self.piece_list.addItem(item)
    
    def on_available_selection_changed(self):
        """Handle selection change in available abilities list"""
        current_item = self.available_list.currentItem()
        self.add_btn.setEnabled(current_item is not None)
        self.edit_btn.setEnabled(current_item is not None)

        if current_item:
            ability_name = current_item.data(Qt.ItemDataRole.UserRole)
            if ability_name:
                ability_data, _ = simple_bridge.load_ability_for_ui(ability_name)
                self.ability_summary.show_ability_summary(ability_data)
        else:
            self.ability_summary.show_ability_summary(None)

    def on_piece_selection_changed(self):
        """Handle selection change in piece abilities list"""
        current_item = self.piece_list.currentItem()
        self.remove_btn.setEnabled(current_item is not None)

        if current_item:
            ability_name = current_item.data(Qt.ItemDataRole.UserRole)
            if ability_name and isinstance(ability_name, str):
                ability_data, _ = simple_bridge.load_ability_for_ui(ability_name)
                self.ability_summary.show_ability_summary(ability_data)
        else:
            self.ability_summary.show_ability_summary(None)
    
    def add_ability_to_piece(self):
        """Add selected ability to piece"""
        current_item = self.available_list.currentItem()
        if not current_item:
            return
        
        ability_name = current_item.data(Qt.ItemDataRole.UserRole)
        
        # Check if already added
        if ability_name in self.piece_abilities:
            QMessageBox.information(self, "Already Added", "This ability is already added to the piece.")
            return
        
        # Add as simple string reference
        self.piece_abilities.append(ability_name)
        self.refresh_piece_abilities()
    
    def remove_ability_from_piece(self):
        """Remove selected ability from piece"""
        current_row = self.piece_list.currentRow()
        if current_row < 0:
            return
        
        if current_row < len(self.piece_abilities):
            removed_ability = self.piece_abilities[current_row]
            del self.piece_abilities[current_row]
            self.refresh_piece_abilities()
            
            # Show confirmation
            if isinstance(removed_ability, str):
                QMessageBox.information(self, "Removed", f"Removed ability: {removed_ability}")
    
    def create_new_ability(self):
        """Open the Ability Editor to create a new ability"""
        self.open_ability_editor()
    
    def edit_selected_ability(self):
        """Open the Ability Editor to edit the selected ability"""
        current_item = self.available_list.currentItem()
        if not current_item:
            return
        
        ability_name = current_item.data(Qt.ItemDataRole.UserRole)
        self.open_ability_editor(ability_name)
    
    def open_ability_editor(self, ability_name=None):
        """Open the Ability Editor window"""
        try:
            from editors.ability_editor import AbilityEditorWindow

            # Create ability editor window
            self.ability_editor = AbilityEditorWindow()
            self.ability_editor.setWindowModality(Qt.WindowModality.ApplicationModal)

            # If editing existing ability, load it
            if ability_name:
                ability_data, error = simple_bridge.load_ability_for_ui(ability_name)
                if ability_data and not error:
                    self.ability_editor.set_ability_data(ability_data)
                    self.ability_editor.current_filename = os.path.join(ABILITIES_DIR, f"{ability_name}.json")
                else:
                    QMessageBox.critical(self, "Error", f"Failed to load ability: {error}")
                    return

            # Show the editor
            self.ability_editor.show()
            self.ability_editor.raise_()
            self.ability_editor.activateWindow()

            # Refresh our lists when the editor is closed (if it was accepted)
            # Note: This is a simple approach - in a more sophisticated system,
            # we might use signals to detect when abilities are saved

        except ImportError as e:
            QMessageBox.critical(self, "Error", f"Could not open Ability Editor: {e}")
    
    def get_piece_abilities(self):
        """Get the current piece abilities list"""
        return self.piece_abilities.copy()