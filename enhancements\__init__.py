"""
Enhancement Modules for Adventure Chess Creator

This package contains all enhancement and optimization modules organized by category:
- workflow: Workflow optimization and integration
- performance: Performance enhancements and lazy loading
- security: Security enhancements and validation
- ui: UI enhancements and visual feedback
"""

# Re-export commonly used enhancement modules for backward compatibility
from .workflow import integrate_workflow_optimization, add_workflow_menu
from .ui import (
    apply_visual_feedback_to_editor,
    apply_visual_feedback_to_main_window,
    get_visual_feedback_manager,
    show_user_friendly_error,
    EnhancedSearchWidget,
    ValidationRules
)

__all__ = [
    'integrate_workflow_optimization',
    'add_workflow_menu',
    'integrate_visual_feedback_into_piece_editor',
    'integrate_visual_feedback_into_ability_editor',
    'get_visual_feedback_manager'
]
