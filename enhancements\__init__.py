"""
Enhancement Modules for Adventure Chess Creator

This package contains all enhancement and optimization modules organized by category:
- workflow: Workflow optimization and integration
- performance: Performance enhancements and lazy loading
- security: Security enhancements and validation
- ui: UI enhancements and visual feedback
"""

# Re-export commonly used enhancement modules for backward compatibility
from .workflow.workflow_integration import integrate_workflow_optimization, add_workflow_menu
from .ui.visual_feedback_integration import (
    integrate_visual_feedback_into_piece_editor,
    integrate_visual_feedback_into_ability_editor,
    get_visual_feedback_manager
)

__all__ = [
    'integrate_workflow_optimization',
    'add_workflow_menu',
    'integrate_visual_feedback_into_piece_editor',
    'integrate_visual_feedback_into_ability_editor',
    'get_visual_feedback_manager'
]
