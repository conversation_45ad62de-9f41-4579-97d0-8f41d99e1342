"""
Base Tag Configuration for Adventure Chess Creator

This module provides the base class that all tag configurations should inherit from.
It defines the standard interface for creating UI widgets and handling data.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import QWidget, QFormLayout

logger = logging.getLogger(__name__)


class BaseTagConfig(ABC):
    """
    Base class for all tag configuration implementations.
    
    Each tag configuration should inherit from this class and implement
    the required methods for creating UI and handling data.
    """
    
    def __init__(self, editor_instance, tag_name: str):
        """
        Initialize the tag configuration.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
            tag_name: The name of the tag this config handles
        """
        self.editor = editor_instance
        self.tag_name = tag_name
        self.widgets = {}  # Store widget references for data population
    
    @abstractmethod
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for this tag configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        pass
    
    @abstractmethod
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        pass
    
    @abstractmethod
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the tag's data
        """
        pass
    
    def get_title(self) -> str:
        """
        Get the display title for this tag configuration.
        
        Returns:
            The title string with emoji and formatting
        """
        return f"🔧 {self.tag_name.title()} Configuration"
    
    def get_widget_by_name(self, widget_name: str) -> Optional[QWidget]:
        """
        Get a widget by its name.
        
        Args:
            widget_name: The name of the widget to retrieve
            
        Returns:
            The widget if found, None otherwise
        """
        return self.widgets.get(widget_name)
    
    def store_widget(self, widget_name: str, widget: QWidget) -> None:
        """
        Store a widget reference for later use.
        
        Args:
            widget_name: The name to store the widget under
            widget: The widget to store
        """
        self.widgets[widget_name] = widget
        
        # Also store on the editor for backward compatibility
        if hasattr(self.editor, 'setattr'):
            setattr(self.editor, widget_name, widget)
    
    def connect_change_signals(self, widget: QWidget = None) -> None:
        """
        Connect change signals from a widget to mark unsaved changes.

        Args:
            widget: The widget to connect signals for. If None, connects all stored widgets.
        """
        try:
            if widget is not None:
                # Connect signals for a specific widget
                self._connect_widget_signals(widget)
            else:
                # Connect signals for all stored widgets (legacy compatibility)
                for widget_name, stored_widget in self.widgets.items():
                    if stored_widget is not None:
                        self._connect_widget_signals(stored_widget)

        except Exception as e:
            logger.warning(f"Could not connect change signals: {e}")

    def _connect_widget_signals(self, widget: QWidget) -> None:
        """Helper method to connect signals for a specific widget."""
        try:
            if hasattr(self.editor, 'mark_unsaved_changes'):
                # Connect appropriate signals based on widget type
                widget_type = type(widget).__name__

                if widget_type in ['QLineEdit', 'QTextEdit']:
                    widget.textChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type in ['QSpinBox', 'QDoubleSpinBox']:
                    widget.valueChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type == 'QCheckBox':
                    widget.stateChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type == 'QComboBox':
                    widget.currentTextChanged.connect(self.editor.mark_unsaved_changes)
                # InlineSelectors have their own change handling

        except Exception as e:
            logger.warning(f"Could not connect change signals for {widget_type}: {e}")
    
    def create_form_layout(self) -> QFormLayout:
        """
        Create a standard form layout for this tag configuration.
        
        Returns:
            A QFormLayout ready for use
        """
        from ui.ui_utils import ResponsiveLayout
        return ResponsiveLayout.create_form_layout()
    
    def log_debug(self, message: str) -> None:
        """Log a debug message with tag context."""
        logger.debug(f"[{self.tag_name}] {message}")
    
    def log_error(self, message: str) -> None:
        """Log an error message with tag context."""
        logger.error(f"[{self.tag_name}] {message}")
