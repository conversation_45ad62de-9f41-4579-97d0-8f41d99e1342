"""
Pydantic-based data manager for Adventure Chess
Handles loading, saving, and validation using Pydantic models
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path

from .piece_schema import Piece
from .ability_schema import Ability
from config import PIECES_DIR, ABILITIES_DIR, PIECE_EXTENSION, ABILITY_EXTENSION

logger = logging.getLogger(__name__)


class PydanticDataManager:
    """
    Centralized data manager using Pydantic models for validation and consistency
    Replaces the old dictionary-based system with strongly-typed models
    """
    
    def __init__(self):
        self.piece_cache: Dict[str, Piece] = {}
        self.ability_cache: Dict[str, Ability] = {}
        self.error_log: List[str] = []
    
    # ========== PIECE OPERATIONS ==========
    
    def load_piece(self, filename: str) -> Tuple[Optional[Piece], Optional[str]]:
        """
        Load a piece from file using Pydantic validation
        Returns: (piece_model, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            if not filepath.exists():
                return None, f"Piece file not found: {filename}"
            
            # Check cache first
            cache_key = str(filepath)
            if cache_key in self.piece_cache:
                logger.debug(f"Loaded piece from cache: {filename}")
                return self.piece_cache[cache_key], None
            
            # Load and parse JSON
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Create Pydantic model from legacy data
            piece = Piece.from_legacy_dict(data)
            
            # Cache the piece
            self.piece_cache[cache_key] = piece
            
            logger.info(f"Successfully loaded piece: {piece.name}")
            return piece, None
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in piece file {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
            
        except Exception as e:
            error_msg = f"Error loading piece {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
    
    def save_piece(self, piece: Piece, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save a piece to file with Pydantic validation
        Returns: (success, error_message)
        """
        try:
            # Validate the piece model
            piece.model_dump()  # This will raise ValidationError if invalid
            
            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(piece.name)
            
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            # Convert to legacy format for file storage
            data = piece.to_legacy_dict()
            
            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Update cache
            cache_key = str(filepath)
            self.piece_cache[cache_key] = piece
            
            logger.info(f"Successfully saved piece: {piece.name} to {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error saving piece {piece.name}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg
    
    def load_piece_by_name(self, name: str) -> Tuple[Optional[Piece], Optional[str]]:
        """Load a piece by its name (searches all piece files)"""
        try:
            pieces_dir = Path(PIECES_DIR)
            if not pieces_dir.exists():
                return None, "Pieces directory not found"
            
            # Search for piece by name
            for filepath in pieces_dir.glob(f"*{PIECE_EXTENSION}"):
                piece, error = self.load_piece(filepath.name)
                if piece and piece.name == name:
                    return piece, None
            
            return None, f"Piece '{name}' not found"
            
        except Exception as e:
            error_msg = f"Error searching for piece '{name}': {e}"
            logger.error(error_msg)
            return None, error_msg
    
    def list_pieces(self) -> List[str]:
        """Get list of all available piece names"""
        pieces = []
        try:
            pieces_dir = Path(PIECES_DIR)
            if pieces_dir.exists():
                for filepath in pieces_dir.glob(f"*{PIECE_EXTENSION}"):
                    piece, error = self.load_piece(filepath.name)
                    if piece:
                        pieces.append(piece.name)
        except Exception as e:
            logger.error(f"Error listing pieces: {e}")
        
        return sorted(pieces)
    
    # ========== ABILITY OPERATIONS ==========
    
    def load_ability(self, filename: str) -> Tuple[Optional[Ability], Optional[str]]:
        """
        Load an ability from file using Pydantic validation
        Returns: (ability_model, error_message)
        """
        try:
            # Normalize filename
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION
            
            filepath = Path(ABILITIES_DIR) / filename
            
            if not filepath.exists():
                return None, f"Ability file not found: {filename}"
            
            # Check cache first
            cache_key = str(filepath)
            if cache_key in self.ability_cache:
                logger.debug(f"Loaded ability from cache: {filename}")
                return self.ability_cache[cache_key], None
            
            # Load and parse JSON
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Create Pydantic model from legacy data
            ability = Ability.from_legacy_dict(data)
            
            # Cache the ability
            self.ability_cache[cache_key] = ability
            
            logger.info(f"Successfully loaded ability: {ability.name}")
            return ability, None
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON in ability file {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
            
        except Exception as e:
            error_msg = f"Error loading ability {filename}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return None, error_msg
    
    def save_ability(self, ability: Ability, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save an ability to file with Pydantic validation
        Returns: (success, error_message)
        """
        try:
            # Validate the ability model
            ability.model_dump()  # This will raise ValidationError if invalid
            
            # Validate all tag data
            validation_errors = ability.validate_all_tags()
            if validation_errors:
                error_msg = f"Tag validation errors: {validation_errors}"
                logger.error(error_msg)
                return False, error_msg
            
            # Generate filename if not provided
            if not filename:
                filename = self._sanitize_filename(ability.name)
            
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION
            
            filepath = Path(ABILITIES_DIR) / filename
            
            # Convert to legacy format for file storage
            data = ability.to_legacy_dict()
            
            # Ensure directories exist
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Update cache
            cache_key = str(filepath)
            self.ability_cache[cache_key] = ability
            
            logger.info(f"Successfully saved ability: {ability.name} to {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error saving ability {ability.name}: {e}"
            logger.error(error_msg)
            self.error_log.append(error_msg)
            return False, error_msg
    
    def load_ability_by_name(self, name: str) -> Tuple[Optional[Ability], Optional[str]]:
        """Load an ability by its name (searches all ability files)"""
        try:
            abilities_dir = Path(ABILITIES_DIR)
            if not abilities_dir.exists():
                return None, "Abilities directory not found"
            
            # Search for ability by name
            for filepath in abilities_dir.glob(f"*{ABILITY_EXTENSION}"):
                ability, error = self.load_ability(filepath.name)
                if ability and ability.name == name:
                    return ability, None
            
            return None, f"Ability '{name}' not found"
            
        except Exception as e:
            error_msg = f"Error searching for ability '{name}': {e}"
            logger.error(error_msg)
            return None, error_msg
    
    def list_abilities(self) -> List[str]:
        """Get list of all available ability names"""
        abilities = []
        try:
            abilities_dir = Path(ABILITIES_DIR)
            if abilities_dir.exists():
                for filepath in abilities_dir.glob(f"*{ABILITY_EXTENSION}"):
                    ability, error = self.load_ability(filepath.name)
                    if ability:
                        abilities.append(ability.name)
        except Exception as e:
            logger.error(f"Error listing abilities: {e}")

        return sorted(abilities)
    
    # ========== UTILITY METHODS ==========
    
    def _sanitize_filename(self, name: str) -> str:
        """Sanitize a name for use as a filename"""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')
        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed"
        return sanitized
    
    def clear_cache(self):
        """Clear all cached data"""
        self.piece_cache.clear()
        self.ability_cache.clear()
        logger.info("Data cache cleared")
    
    def get_error_log(self) -> List[str]:
        """Get list of all errors that occurred"""
        return self.error_log.copy()
    
    def clear_error_log(self):
        """Clear the error log"""
        self.error_log.clear()


# Global instance for use throughout the application
pydantic_data_manager = PydanticDataManager()
