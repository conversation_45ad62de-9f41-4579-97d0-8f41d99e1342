"""
Workflow Enhancement Module

Consolidated workflow optimization and integration features:
- Core workflow features (undo/redo, shortcuts, templates, auto-save)
- Integration with existing editors
- Status bar and visual feedback
"""

from .workflow_enhancements import (
    UndoRedoManager, KeyboardShortcutManager,
    AutoSaveManager, TemplateManager,
    WorkflowIntegrator,
    integrate_workflow_optimization, add_workflow_menu
)

__all__ = [
    'UndoRedoManager',
    'KeyboardShortcutManager',
    'AutoSaveManager',
    'TemplateManager',
    'WorkflowIntegrator',
    'integrate_workflow_optimization',
    'add_workflow_menu'
]
