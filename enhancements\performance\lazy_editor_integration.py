#!/usr/bin/env python3
"""
Lazy Loading Integration Patches for Adventure Chess Creator Editors
Provides drop-in replacements and patches for existing editor components
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, List, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtWidgets import QComboBox, QListWidget, QWidget, QVBoxLayout, QHBoxLayout

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lazy_ui_components import LazyComboBox, LazyListWidget, LazyLoadingPanel, LazyFileSelector
from lazy_data_integration import get_lazy_data_manager

logger = logging.getLogger(__name__)

class LazyPieceEditorPatches:
    """
    Patches for piece editor to enable lazy loading
    """
    
    @staticmethod
    def patch_refresh_file_lists(editor_instance):
        """
        Patch the refresh_file_lists method to use lazy loading
        """
        original_method = getattr(editor_instance, 'refresh_file_lists', None)
        
        def lazy_refresh_file_lists():
            """Enhanced refresh with lazy loading"""
            try:
                # Get lazy data manager
                data_manager = get_lazy_data_manager()
                
                # Get pieces manager
                pieces_manager = data_manager.get_pieces_manager()
                
                # Check if editor has load_combo attribute
                if hasattr(editor_instance, 'load_combo'):
                    # Replace with lazy combo if not already done
                    if not isinstance(editor_instance.load_combo, LazyComboBox):
                        from config import PIECES_DIR
                        
                        # Store current selection
                        current_text = editor_instance.load_combo.currentText()
                        
                        # Replace with lazy combo
                        parent = editor_instance.load_combo.parent()
                        layout = parent.layout()
                        
                        # Remove old combo
                        layout.removeWidget(editor_instance.load_combo)
                        editor_instance.load_combo.deleteLater()
                        
                        # Create new lazy combo
                        editor_instance.load_combo = LazyComboBox(PIECES_DIR)
                        layout.addWidget(editor_instance.load_combo)
                        
                        # Connect signals
                        editor_instance.load_combo.item_selected_lazy.connect(
                            lambda filename, data: editor_instance.on_piece_loaded_lazy(filename, data)
                        )
                        
                        # Restore selection if possible
                        if current_text:
                            index = editor_instance.load_combo.findText(current_text)
                            if index >= 0:
                                editor_instance.load_combo.setCurrentIndex(index)
                
                # Start background preloading
                data_manager.preload_recent_files(max_files=10)
                
                logger.info("Piece editor file lists refreshed with lazy loading")
                
            except Exception as e:
                logger.error(f"Error in lazy refresh_file_lists: {e}")
                # Fallback to original method if available
                if original_method:
                    original_method()
        
        # Replace the method
        editor_instance.refresh_file_lists = lazy_refresh_file_lists
        
        # Add lazy loading callback method
        def on_piece_loaded_lazy(filename: str, data: Dict[str, Any]):
            """Handle piece loaded via lazy loading"""
            try:
                # Call existing load logic if available
                if hasattr(editor_instance, 'load_piece_data'):
                    editor_instance.load_piece_data(data)
                elif hasattr(editor_instance, 'populate_ui_from_data'):
                    editor_instance.populate_ui_from_data(data)
                
                logger.info(f"Piece loaded lazily: {filename}")
                
            except Exception as e:
                logger.error(f"Error handling lazy loaded piece {filename}: {e}")
        
        editor_instance.on_piece_loaded_lazy = on_piece_loaded_lazy
    
    @staticmethod
    def add_lazy_loading_panel(editor_instance):
        """Add lazy loading status panel to editor"""
        try:
            # Find main layout
            main_widget = editor_instance
            if hasattr(editor_instance, 'centralWidget'):
                main_widget = editor_instance.centralWidget()
            
            layout = main_widget.layout()
            if layout is None:
                return
            
            # Create lazy loading panel
            lazy_panel = LazyLoadingPanel()
            lazy_panel.setMaximumHeight(150)
            
            # Add to layout (usually at the bottom)
            layout.addWidget(lazy_panel)
            
            # Store reference
            editor_instance.lazy_loading_panel = lazy_panel
            
            logger.info("Added lazy loading panel to piece editor")
            
        except Exception as e:
            logger.error(f"Error adding lazy loading panel: {e}")

class LazyAbilityEditorPatches:
    """
    Patches for ability editor to enable lazy loading
    """
    
    @staticmethod
    def patch_refresh_file_lists(editor_instance):
        """
        Patch the refresh_file_lists method to use lazy loading
        """
        original_method = getattr(editor_instance, 'refresh_file_lists', None)
        
        def lazy_refresh_file_lists():
            """Enhanced refresh with lazy loading"""
            try:
                # Get lazy data manager
                data_manager = get_lazy_data_manager()
                
                # Get abilities manager
                abilities_manager = data_manager.get_abilities_manager()
                
                # Check if editor has load_combo attribute
                if hasattr(editor_instance, 'load_combo'):
                    # Replace with lazy combo if not already done
                    if not isinstance(editor_instance.load_combo, LazyComboBox):
                        from config import ABILITIES_DIR
                        
                        # Store current selection
                        current_text = editor_instance.load_combo.currentText()
                        
                        # Replace with lazy combo
                        parent = editor_instance.load_combo.parent()
                        layout = parent.layout()
                        
                        # Remove old combo
                        layout.removeWidget(editor_instance.load_combo)
                        editor_instance.load_combo.deleteLater()
                        
                        # Create new lazy combo
                        editor_instance.load_combo = LazyComboBox(ABILITIES_DIR)
                        layout.addWidget(editor_instance.load_combo)
                        
                        # Connect signals
                        editor_instance.load_combo.item_selected_lazy.connect(
                            lambda filename, data: editor_instance.on_ability_loaded_lazy(filename, data)
                        )
                        
                        # Restore selection if possible
                        if current_text:
                            index = editor_instance.load_combo.findText(current_text)
                            if index >= 0:
                                editor_instance.load_combo.setCurrentIndex(index)
                
                # Start background preloading
                data_manager.preload_recent_files(max_files=10)
                
                logger.info("Ability editor file lists refreshed with lazy loading")
                
            except Exception as e:
                logger.error(f"Error in lazy refresh_file_lists: {e}")
                # Fallback to original method if available
                if original_method:
                    original_method()
        
        # Replace the method
        editor_instance.refresh_file_lists = lazy_refresh_file_lists
        
        # Add lazy loading callback method
        def on_ability_loaded_lazy(filename: str, data: Dict[str, Any]):
            """Handle ability loaded via lazy loading"""
            try:
                # Call existing load logic if available
                if hasattr(editor_instance, 'load_ability_data'):
                    editor_instance.load_ability_data(data)
                elif hasattr(editor_instance, 'populate_ui_from_data'):
                    editor_instance.populate_ui_from_data(data)
                
                logger.info(f"Ability loaded lazily: {filename}")
                
            except Exception as e:
                logger.error(f"Error handling lazy loaded ability {filename}: {e}")
        
        editor_instance.on_ability_loaded_lazy = on_ability_loaded_lazy

class LazyDialogPatches:
    """
    Patches for dialogs to enable lazy loading
    """
    
    @staticmethod
    def patch_piece_ability_manager(dialog_instance):
        """
        Patch piece ability manager dialog for lazy loading
        """
        try:
            # Replace available_list with lazy list widget
            if hasattr(dialog_instance, 'available_list'):
                from config import ABILITIES_DIR
                
                # Store current parent and layout
                parent = dialog_instance.available_list.parent()
                layout = parent.layout()
                
                # Remove old list
                layout.removeWidget(dialog_instance.available_list)
                dialog_instance.available_list.deleteLater()
                
                # Create new lazy list
                dialog_instance.available_list = LazyListWidget(ABILITIES_DIR)
                layout.addWidget(dialog_instance.available_list)
                
                # Connect signals
                dialog_instance.available_list.item_selected_lazy.connect(
                    lambda filename, data: dialog_instance.on_ability_selected_lazy(filename, data)
                )
                
                logger.info("Patched piece ability manager with lazy loading")
        
        except Exception as e:
            logger.error(f"Error patching piece ability manager: {e}")
        
        # Add lazy loading callback method
        def on_ability_selected_lazy(filename: str, data: Dict[str, Any]):
            """Handle ability selected via lazy loading"""
            try:
                # Store the loaded data for quick access
                if not hasattr(dialog_instance, 'loaded_abilities'):
                    dialog_instance.loaded_abilities = {}
                
                dialog_instance.loaded_abilities[filename] = data
                
                logger.info(f"Ability selected lazily: {filename}")
                
            except Exception as e:
                logger.error(f"Error handling lazy selected ability {filename}: {e}")
        
        dialog_instance.on_ability_selected_lazy = on_ability_selected_lazy

class LazyMainApplicationPatches:
    """
    Patches for main application to enable lazy loading
    """
    
    @staticmethod
    def patch_editor_initialization(main_app_instance):
        """
        Patch main application to use lazy editor initialization
        """
        try:
            # Store original editor references
            original_piece_editor = getattr(main_app_instance, 'editor', None)
            original_ability_editor = getattr(main_app_instance, 'ability_editor', None)
            
            # Lazy editor properties
            main_app_instance._piece_editor = None
            main_app_instance._ability_editor = None
            
            def get_piece_editor():
                """Lazy piece editor getter"""
                if main_app_instance._piece_editor is None:
                    # Import and create editor
                    from editors.piece_editor.piece_editor_main import PieceEditorWindow
                    main_app_instance._piece_editor = PieceEditorWindow()
                    
                    # Apply lazy loading patches
                    LazyPieceEditorPatches.patch_refresh_file_lists(main_app_instance._piece_editor)
                    LazyPieceEditorPatches.add_lazy_loading_panel(main_app_instance._piece_editor)
                    
                    logger.info("Piece editor created lazily")
                
                return main_app_instance._piece_editor
            
            def get_ability_editor():
                """Lazy ability editor getter"""
                if main_app_instance._ability_editor is None:
                    # Import and create editor
                    from editors.ability_editor.ability_editor_main import AbilityEditorWindow
                    main_app_instance._ability_editor = AbilityEditorWindow()
                    
                    # Apply lazy loading patches
                    LazyAbilityEditorPatches.patch_refresh_file_lists(main_app_instance._ability_editor)
                    
                    logger.info("Ability editor created lazily")
                
                return main_app_instance._ability_editor
            
            # Replace editor properties with lazy getters
            main_app_instance.editor = property(lambda self: get_piece_editor())
            main_app_instance.ability_editor = property(lambda self: get_ability_editor())
            
            logger.info("Main application patched for lazy editor initialization")
            
        except Exception as e:
            logger.error(f"Error patching main application: {e}")

def apply_all_lazy_patches(main_app_instance):
    """
    Apply all lazy loading patches to the application
    """
    try:
        logger.info("Applying lazy loading patches...")
        
        # Patch main application
        LazyMainApplicationPatches.patch_editor_initialization(main_app_instance)
        
        # Start background preloading
        data_manager = get_lazy_data_manager()
        data_manager.preload_recent_files(max_files=15)
        
        logger.info("All lazy loading patches applied successfully")
        
    except Exception as e:
        logger.error(f"Error applying lazy loading patches: {e}")

def create_lazy_file_selector_widget(directory: str, title: str = "Select File") -> LazyFileSelector:
    """
    Create a lazy file selector widget for use in editors
    """
    return LazyFileSelector(directory, title)
