"""
File System Optimization Demo for Adventure Chess Creator

This demo showcases the file system optimization features including:
- File indexing and search capabilities
- Performance improvements
- Directory scanning optimization
- Search suggestions and autocomplete
- Integration with existing systems

Run this demo to see the optimization features in action.
"""

import sys
import os
import time
import json
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhancements.performance import get_file_system_optimizer, reset_optimizer, get_optimized_data_manager, PerformanceMonitor
from config import PIECES_DIR, ABILITIES_DIR

def print_header(title: str):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n📋 {title}")
    print("-" * 40)

def demo_file_indexing():
    """Demonstrate file indexing capabilities"""
    print_section("File Indexing Demo")
    
    optimizer = get_file_system_optimizer()
    
    print("🔄 Indexing files...")
    start_time = time.time()
    indexed_count = optimizer.update_index([PIECES_DIR, ABILITIES_DIR])
    index_time = time.time() - start_time
    
    print(f"✅ Indexed {indexed_count} files in {index_time:.3f} seconds")
    
    # Show index statistics
    stats = optimizer.get_index_statistics()
    print(f"📊 Index Statistics:")
    print(f"   Total Files: {stats.get('total_files', 0)}")
    print(f"   Total Size: {stats.get('total_size_bytes', 0) / 1024:.1f} KB")
    
    by_type = stats.get('by_type', {})
    for file_type, type_stats in by_type.items():
        print(f"   {file_type.title()}s: {type_stats.get('count', 0)} files")

def demo_search_functionality():
    """Demonstrate search functionality"""
    print_section("Search Functionality Demo")
    
    optimizer = get_file_system_optimizer()
    
    # Test various search queries
    search_queries = [
        ("king", "Search for 'king'"),
        ("magic", "Search for 'magic'"),
        ("teleport", "Search for 'teleport'"),
        ("royal", "Search for 'royal'"),
        ("fire", "Search for 'fire'")
    ]
    
    for query, description in search_queries:
        print(f"\n🔍 {description}:")
        
        start_time = time.time()
        results = optimizer.search_files(query, max_results=5)
        search_time = (time.time() - start_time) * 1000
        
        if results:
            print(f"   Found {len(results)} results in {search_time:.2f}ms:")
            for i, result in enumerate(results[:3], 1):
                print(f"   {i}. {result.filename} ({result.file_type}) - Score: {result.relevance_score:.1f}")
                print(f"      Preview: {result.preview_text[:60]}...")
        else:
            print(f"   No results found in {search_time:.2f}ms")

def demo_search_suggestions():
    """Demonstrate search suggestions"""
    print_section("Search Suggestions Demo")
    
    optimizer = get_file_system_optimizer()
    
    # Test suggestion queries
    suggestion_queries = [
        ("kin", "Suggestions for 'kin'"),
        ("mag", "Suggestions for 'mag'"),
        ("tel", "Suggestions for 'tel'"),
        ("test", "Suggestions for 'test'")
    ]
    
    for partial, description in suggestion_queries:
        print(f"\n💡 {description}:")
        
        suggestions = optimizer.get_file_suggestions(partial, limit=5)
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion}")
        else:
            print("   No suggestions found")

def demo_file_type_filtering():
    """Demonstrate file type filtering"""
    print_section("File Type Filtering Demo")
    
    optimizer = get_file_system_optimizer()
    
    # Search pieces only
    print("🎯 Searching pieces only:")
    piece_results = optimizer.search_files("", file_type="piece", max_results=5)
    for result in piece_results:
        print(f"   📄 {result.filename} - {result.preview_text[:50]}...")
    
    # Search abilities only
    print("\n🎯 Searching abilities only:")
    ability_results = optimizer.search_files("", file_type="ability", max_results=5)
    for result in ability_results:
        print(f"   ⚡ {result.filename} - {result.preview_text[:50]}...")

def demo_directory_optimization():
    """Demonstrate directory scanning optimization"""
    print_section("Directory Scanning Optimization Demo")
    
    optimizer = get_file_system_optimizer()
    
    # First scan (uncached)
    print("🔄 First directory scan (uncached):")
    result1 = optimizer.optimize_directory_scanning(PIECES_DIR)
    print(f"   Files found: {result1['file_count']}")
    print(f"   Scan time: {result1['scan_time_ms']:.2f}ms")
    print(f"   Cache hit: {result1['cache_hit']}")
    
    # Second scan (cached)
    print("\n🔄 Second directory scan (cached):")
    result2 = optimizer.optimize_directory_scanning(PIECES_DIR)
    print(f"   Files found: {result2['file_count']}")
    print(f"   Scan time: {result2['scan_time_ms']:.2f}ms")
    print(f"   Cache hit: {result2['cache_hit']}")
    
    # Show performance improvement
    if result1['scan_time_ms'] > 0:
        improvement = ((result1['scan_time_ms'] - result2['scan_time_ms']) / result1['scan_time_ms']) * 100
        print(f"   Performance improvement: {improvement:.1f}%")

def demo_performance_monitoring():
    """Demonstrate performance monitoring"""
    print_section("Performance Monitoring Demo")
    
    monitor = PerformanceMonitor()
    
    # Generate performance report
    print("📊 Generating performance report...")
    report = monitor.generate_performance_report()
    print(report)

def demo_integration_features():
    """Demonstrate integration with existing systems"""
    print_section("Integration Features Demo")
    
    data_manager = get_optimized_data_manager()
    
    # Test piece search
    print("🔍 Searching pieces through integrated manager:")
    piece_results = data_manager.search_pieces("king", max_results=3)
    for result in piece_results:
        print(f"   📄 {result.filename} - Score: {result.relevance_score:.1f}")
    
    # Test ability search
    print("\n🔍 Searching abilities through integrated manager:")
    ability_results = data_manager.search_abilities("magic", max_results=3)
    for result in ability_results:
        print(f"   ⚡ {result.filename} - Score: {result.relevance_score:.1f}")
    
    # Test suggestions
    print("\n💡 Getting piece suggestions:")
    piece_suggestions = data_manager.get_piece_suggestions("kin", limit=3)
    for suggestion in piece_suggestions:
        print(f"   📄 {suggestion}")

def demo_compression_features():
    """Demonstrate file compression features"""
    print_section("File Compression Demo")
    
    optimizer = get_file_system_optimizer()
    
    print("🗜️ Testing file compression...")
    result = optimizer.compress_large_files(threshold_kb=1)
    
    print(f"   Files compressed: {result.get('compressed_files', 0)}")
    print(f"   Space saved: {result.get('space_saved_bytes', 0)} bytes")
    print(f"   Threshold: {result.get('threshold_kb', 0)} KB")
    
    if result.get('compressed_files', 0) == 0:
        print("   ℹ️ No files met compression criteria (files too small or compression disabled)")

def run_comprehensive_demo():
    """Run the comprehensive file system optimization demo"""
    print_header("Adventure Chess Creator - File System Optimization Demo")
    
    print("🎯 This demo showcases the file system optimization features")
    print("   including indexing, search, caching, and performance improvements.")
    
    try:
        # Reset optimizer to start fresh
        reset_optimizer()
        
        # Run all demo sections
        demo_file_indexing()
        demo_search_functionality()
        demo_search_suggestions()
        demo_file_type_filtering()
        demo_directory_optimization()
        demo_integration_features()
        demo_compression_features()
        demo_performance_monitoring()
        
        print_header("Demo Complete!")
        print("✅ All file system optimization features demonstrated successfully!")
        print("🚀 The system is now optimized for faster file operations and search.")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            reset_optimizer()
        except:
            pass

if __name__ == "__main__":
    run_comprehensive_demo()
