"""
User-Friendly Error System for Adventure Chess Creator

This module provides enhanced error dialogs with:
- User-friendly error messages instead of technical jargon
- Contextual help and suggested solutions
- Visual error categorization with icons
- Quick action buttons for common fixes
- Detailed technical information (expandable)
- Error reporting and feedback mechanisms

The system replaces technical error messages with clear, actionable guidance
that helps users understand and resolve issues quickly.
"""

import logging
import traceback
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QScrollArea, QWidget, QFrame, QMessageBox,
    QApplication, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QFont

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """User-friendly error severity levels"""
    INFO = "info"
    WARNING = "warning" 
    ERROR = "error"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """User-friendly error categories"""
    FILE_ACCESS = "file_access"
    DATA_FORMAT = "data_format"
    MISSING_DATA = "missing_data"
    PERMISSION = "permission"
    NETWORK = "network"
    VALIDATION = "validation"
    SYSTEM = "system"

@dataclass
class UserFriendlyError:
    """User-friendly error information"""
    title: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    suggestions: List[str]
    quick_fixes: List[Dict[str, Any]]  # {"text": "Fix Name", "action": callable}
    technical_details: Optional[str] = None
    help_url: Optional[str] = None
    can_continue: bool = True

class ErrorMessageTranslator:
    """Translates technical errors into user-friendly messages"""
    
    @staticmethod
    def translate_error(error: Exception, operation: str = "", file_path: str = "") -> UserFriendlyError:
        """Translate technical error into user-friendly format"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # File permission errors
        if "permission" in error_str or "access" in error_str:
            return UserFriendlyError(
                title="File Access Problem",
                message=f"Cannot access the file '{file_path}'. This usually happens when the file is being used by another program or you don't have permission to modify it.",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.PERMISSION,
                suggestions=[
                    "Close any other programs that might be using this file",
                    "Check if the file is read-only and change its properties",
                    "Try running Adventure Chess Creator as administrator",
                    "Save the file to a different location (like your Documents folder)"
                ],
                quick_fixes=[
                    {"text": "Try Different Location", "action": "save_as_dialog"},
                    {"text": "Check File Properties", "action": "open_file_properties"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}"
            )
        
        # File not found errors
        elif "not found" in error_str or "no such file" in error_str:
            return UserFriendlyError(
                title="File Not Found",
                message=f"The file '{file_path}' cannot be found. It may have been moved, renamed, or deleted.",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_ACCESS,
                suggestions=[
                    "Check if the file was moved to a different folder",
                    "Look in the Recycle Bin if it was accidentally deleted",
                    "Try browsing for the file manually",
                    "Create a new file with the same name if needed"
                ],
                quick_fixes=[
                    {"text": "Browse for File", "action": "browse_for_file"},
                    {"text": "Create New File", "action": "create_new_file"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}"
            )
        
        # JSON format errors
        elif "json" in error_str or "decode" in error_str or "expecting" in error_str:
            return UserFriendlyError(
                title="File Format Problem",
                message=f"The file '{file_path}' appears to be corrupted or in an invalid format. This can happen if the file was edited outside of Adventure Chess Creator.",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.DATA_FORMAT,
                suggestions=[
                    "Try opening the file in a text editor to check for obvious problems",
                    "Restore from a backup copy if you have one",
                    "Create a new file and re-enter the data",
                    "Contact support if this file was created by Adventure Chess Creator"
                ],
                quick_fixes=[
                    {"text": "Open in Text Editor", "action": "open_in_text_editor"},
                    {"text": "Restore from Backup", "action": "restore_backup"},
                    {"text": "Create New", "action": "create_new_file"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}"
            )
        
        # Validation errors
        elif "validation" in error_str or "invalid" in error_str:
            return UserFriendlyError(
                title="Data Validation Problem",
                message="Some of the information you entered doesn't meet the required format. Please check the highlighted fields and try again.",
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.VALIDATION,
                suggestions=[
                    "Check that all required fields are filled in",
                    "Make sure numbers are within the allowed ranges",
                    "Verify that text fields don't contain special characters",
                    "Review any highlighted fields for specific requirements"
                ],
                quick_fixes=[
                    {"text": "Reset to Defaults", "action": "reset_to_defaults"},
                    {"text": "Show Field Requirements", "action": "show_field_help"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}"
            )
        
        # Network/connection errors
        elif "network" in error_str or "connection" in error_str or "timeout" in error_str:
            return UserFriendlyError(
                title="Connection Problem",
                message="Cannot connect to the required service. This might be due to internet connectivity issues or server problems.",
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.NETWORK,
                suggestions=[
                    "Check your internet connection",
                    "Try again in a few minutes",
                    "Check if your firewall is blocking the application",
                    "Contact your network administrator if on a corporate network"
                ],
                quick_fixes=[
                    {"text": "Retry Connection", "action": "retry_operation"},
                    {"text": "Work Offline", "action": "enable_offline_mode"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}"
            )
        
        # Generic system errors
        else:
            return UserFriendlyError(
                title="Unexpected Error",
                message=f"An unexpected error occurred while {operation or 'performing the operation'}. The application should continue to work normally.",
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.SYSTEM,
                suggestions=[
                    "Try the operation again",
                    "Save your work and restart the application if problems persist",
                    "Check if you have enough disk space and memory",
                    "Contact support if this error keeps happening"
                ],
                quick_fixes=[
                    {"text": "Try Again", "action": "retry_operation"},
                    {"text": "Save Work", "action": "save_current_work"}
                ],
                technical_details=f"Technical error: {error_type}: {str(error)}",
                can_continue=True
            )

class UserFriendlyErrorDialog(QDialog):
    """Enhanced error dialog with user-friendly messages and quick fixes"""
    
    # Signals for quick fix actions
    quick_fix_requested = pyqtSignal(str)  # action name
    
    def __init__(self, error_info: UserFriendlyError, parent=None):
        super().__init__(parent)
        self.error_info = error_info
        self.setWindowTitle(f"Adventure Chess Creator - {error_info.title}")
        self.setMinimumSize(500, 400)
        self.setMaximumSize(800, 600)
        
        self.setup_ui()
        self.setup_styling()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        # Error icon based on severity
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Set icon based on severity
        severity_icons = {
            ErrorSeverity.INFO: "ℹ️",
            ErrorSeverity.WARNING: "⚠️", 
            ErrorSeverity.ERROR: "❌",
            ErrorSeverity.CRITICAL: "🚨"
        }
        icon_text = severity_icons.get(self.error_info.severity, "❓")
        icon_label.setText(icon_text)
        icon_label.setStyleSheet("font-size: 32px;")
        
        # Title and message
        text_layout = QVBoxLayout()
        
        title_label = QLabel(self.error_info.title)
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        
        message_label = QLabel(self.error_info.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("color: #333; line-height: 1.4;")
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(message_label)
        
        header_layout.addWidget(icon_label)
        header_layout.addLayout(text_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Suggestions section
        if self.error_info.suggestions:
            suggestions_group = QGroupBox("What you can try:")
            suggestions_layout = QVBoxLayout(suggestions_group)
            
            for suggestion in self.error_info.suggestions:
                suggestion_label = QLabel(f"• {suggestion}")
                suggestion_label.setWordWrap(True)
                suggestion_label.setStyleSheet("margin-left: 10px; color: #555;")
                suggestions_layout.addWidget(suggestion_label)
            
            layout.addWidget(suggestions_group)
        
        # Quick fix buttons
        if self.error_info.quick_fixes:
            quick_fix_group = QGroupBox("Quick fixes:")
            quick_fix_layout = QHBoxLayout(quick_fix_group)
            
            for fix in self.error_info.quick_fixes:
                fix_button = QPushButton(fix["text"])
                fix_button.clicked.connect(lambda checked, action=fix["action"]: self.quick_fix_requested.emit(action))
                fix_button.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #0056b3;
                    }
                """)
                quick_fix_layout.addWidget(fix_button)
            
            quick_fix_layout.addStretch()
            layout.addWidget(quick_fix_group)
        
        # Technical details (expandable)
        if self.error_info.technical_details:
            self.details_checkbox = QCheckBox("Show technical details")
            self.details_checkbox.stateChanged.connect(self.toggle_technical_details)
            layout.addWidget(self.details_checkbox)
            
            self.details_text = QTextEdit()
            self.details_text.setPlainText(self.error_info.technical_details)
            self.details_text.setMaximumHeight(150)
            self.details_text.setVisible(False)
            self.details_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            layout.addWidget(self.details_text)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        if self.error_info.can_continue:
            continue_button = QPushButton("Continue")
            continue_button.clicked.connect(self.accept)
            continue_button.setDefault(True)
            button_layout.addWidget(continue_button)
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.reject)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
    
    def setup_styling(self):
        """Setup dialog styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def toggle_technical_details(self, state):
        """Toggle technical details visibility"""
        self.details_text.setVisible(state == Qt.CheckState.Checked.value)
        
        # Adjust dialog size
        if state == Qt.CheckState.Checked.value:
            self.resize(self.width(), self.height() + 150)
        else:
            self.resize(self.width(), max(400, self.height() - 150))

# Global error dialog manager
class ErrorDialogManager:
    """Manages user-friendly error dialogs throughout the application"""
    
    def __init__(self):
        self.quick_fix_handlers: Dict[str, Callable] = {}
        self.register_default_handlers()
    
    def register_quick_fix_handler(self, action: str, handler: Callable):
        """Register a handler for a quick fix action"""
        self.quick_fix_handlers[action] = handler
    
    def register_default_handlers(self):
        """Register default quick fix handlers"""
        self.quick_fix_handlers.update({
            "save_as_dialog": self._show_save_as_dialog,
            "browse_for_file": self._show_browse_dialog,
            "create_new_file": self._create_new_file,
            "retry_operation": self._retry_last_operation,
            "reset_to_defaults": self._reset_to_defaults,
            "open_in_text_editor": self._open_in_text_editor
        })
    
    def show_error(self, error: Exception, operation: str = "", file_path: str = "", parent=None) -> bool:
        """Show user-friendly error dialog"""
        try:
            # Translate error to user-friendly format
            error_info = ErrorMessageTranslator.translate_error(error, operation, file_path)
            
            # Create and show dialog
            dialog = UserFriendlyErrorDialog(error_info, parent)
            dialog.quick_fix_requested.connect(self._handle_quick_fix)
            
            result = dialog.exec()
            return result == QDialog.DialogCode.Accepted
            
        except Exception as e:
            # Fallback to standard error dialog
            logger.error(f"Error showing user-friendly dialog: {e}")
            QMessageBox.critical(parent, "Error", f"An error occurred: {str(error)}")
            return False
    
    def _handle_quick_fix(self, action: str):
        """Handle quick fix action"""
        handler = self.quick_fix_handlers.get(action)
        if handler:
            try:
                handler()
            except Exception as e:
                logger.error(f"Error executing quick fix {action}: {e}")
        else:
            logger.warning(f"No handler registered for quick fix action: {action}")
    
    # Default quick fix handlers
    def _show_save_as_dialog(self):
        """Show save as dialog"""
        # This would be implemented to show a save as dialog
        pass
    
    def _show_browse_dialog(self):
        """Show file browse dialog"""
        # This would be implemented to show a file browser
        pass
    
    def _create_new_file(self):
        """Create new file"""
        # This would be implemented to create a new file
        pass
    
    def _retry_last_operation(self):
        """Retry the last operation"""
        # This would be implemented to retry the last operation
        pass
    
    def _reset_to_defaults(self):
        """Reset form to defaults"""
        # This would be implemented to reset current form
        pass
    
    def _open_in_text_editor(self):
        """Open file in text editor"""
        # This would be implemented to open file in system text editor
        pass

# Global instance
error_dialog_manager = ErrorDialogManager()

# Contextual Help System
class ContextualHelpProvider:
    """Provides contextual help for common issues and operations"""

    HELP_CONTENT = {
        "file_permissions": {
            "title": "File Permission Issues",
            "content": """
            <h3>Why do file permission errors happen?</h3>
            <p>File permission errors occur when Adventure Chess Creator cannot read or write to a file. Common causes:</p>
            <ul>
                <li><b>File is open in another program</b> - Close Excel, Notepad, or other programs using the file</li>
                <li><b>File is read-only</b> - Right-click the file, select Properties, and uncheck "Read-only"</li>
                <li><b>Insufficient permissions</b> - Try running as administrator or save to your Documents folder</li>
                <li><b>File is on a network drive</b> - Network issues can cause permission problems</li>
            </ul>

            <h3>How to fix permission issues:</h3>
            <ol>
                <li>Close any other programs that might be using the file</li>
                <li>Check if the file properties show it as "Read-only" and change this if needed</li>
                <li>Try saving to a different location like your Documents folder</li>
                <li>If on a work computer, contact your IT administrator</li>
            </ol>
            """,
            "related_topics": ["file_access", "saving_files"]
        },

        "json_format_errors": {
            "title": "File Format Problems",
            "content": """
            <h3>Understanding file format errors</h3>
            <p>Adventure Chess Creator saves data in JSON format. Format errors happen when:</p>
            <ul>
                <li><b>File was edited outside the program</b> - Always use Adventure Chess Creator to edit files</li>
                <li><b>File got corrupted</b> - This can happen due to power outages or disk errors</li>
                <li><b>File is incomplete</b> - The save operation may have been interrupted</li>
            </ul>

            <h3>How to recover from format errors:</h3>
            <ol>
                <li>Check if you have a backup copy of the file</li>
                <li>Try opening the file in Notepad to see if there are obvious problems</li>
                <li>Look for missing brackets { } or quotes " " at the end of the file</li>
                <li>If the file is severely corrupted, create a new one and re-enter the data</li>
            </ol>

            <h3>Preventing format errors:</h3>
            <ul>
                <li>Always use "Save" in Adventure Chess Creator instead of editing files directly</li>
                <li>Keep backup copies of important pieces and abilities</li>
                <li>Don't edit .json files in text editors unless you're experienced with JSON</li>
            </ul>
            """,
            "related_topics": ["file_corruption", "backups"]
        },

        "validation_errors": {
            "title": "Data Validation Issues",
            "content": """
            <h3>What are validation errors?</h3>
            <p>Validation errors occur when the data you enter doesn't meet the requirements. Common issues:</p>
            <ul>
                <li><b>Required fields are empty</b> - All pieces need a name and description</li>
                <li><b>Numbers are out of range</b> - Values like movement distance have minimum and maximum limits</li>
                <li><b>Invalid characters</b> - Some fields don't allow special characters</li>
                <li><b>Conflicting settings</b> - Some combinations of options aren't allowed</li>
            </ul>

            <h3>How to fix validation errors:</h3>
            <ol>
                <li>Look for fields highlighted in red - these need attention</li>
                <li>Check that all required fields (marked with *) are filled in</li>
                <li>Make sure numbers are within the allowed ranges</li>
                <li>Remove any unusual characters from text fields</li>
                <li>Review the error message for specific guidance</li>
            </ol>

            <h3>Field requirements:</h3>
            <ul>
                <li><b>Name:</b> Required, 1-50 characters, letters and numbers only</li>
                <li><b>Description:</b> Required, up to 200 characters</li>
                <li><b>Movement Distance:</b> 1-10 tiles</li>
                <li><b>Points:</b> 0-100</li>
            </ul>
            """,
            "related_topics": ["field_requirements", "data_entry"]
        }
    }

    @classmethod
    def get_help_content(cls, topic: str) -> Optional[Dict[str, Any]]:
        """Get help content for a specific topic"""
        return cls.HELP_CONTENT.get(topic)

    @classmethod
    def get_related_help(cls, error_category: ErrorCategory) -> Optional[str]:
        """Get help topic related to error category"""
        category_mapping = {
            ErrorCategory.PERMISSION: "file_permissions",
            ErrorCategory.DATA_FORMAT: "json_format_errors",
            ErrorCategory.VALIDATION: "validation_errors"
        }
        return category_mapping.get(error_category)

class ContextualHelpDialog(QDialog):
    """Dialog showing contextual help content"""

    def __init__(self, topic: str, parent=None):
        super().__init__(parent)
        self.topic = topic
        self.help_content = ContextualHelpProvider.get_help_content(topic)

        if not self.help_content:
            self.help_content = {
                "title": "Help Not Available",
                "content": f"<p>Help content for '{topic}' is not available yet.</p>",
                "related_topics": []
            }

        self.setWindowTitle(f"Help - {self.help_content['title']}")
        self.setMinimumSize(600, 500)
        self.setup_ui()

    def setup_ui(self):
        """Setup help dialog UI"""
        layout = QVBoxLayout(self)

        # Help content
        content_area = QTextEdit()
        content_area.setHtml(self.help_content["content"])
        content_area.setReadOnly(True)
        layout.addWidget(content_area)

        # Related topics
        if self.help_content.get("related_topics"):
            related_layout = QHBoxLayout()
            related_layout.addWidget(QLabel("Related topics:"))

            for topic in self.help_content["related_topics"]:
                topic_button = QPushButton(topic.replace("_", " ").title())
                topic_button.clicked.connect(lambda checked, t=topic: self.show_related_topic(t))
                related_layout.addWidget(topic_button)

            related_layout.addStretch()
            layout.addLayout(related_layout)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        layout.addLayout(close_layout)

    def show_related_topic(self, topic: str):
        """Show related help topic"""
        related_dialog = ContextualHelpDialog(topic, self)
        related_dialog.exec()

# Convenience function for easy usage
def show_user_friendly_error(error: Exception, operation: str = "", file_path: str = "", parent=None) -> bool:
    """Show a user-friendly error dialog"""
    return error_dialog_manager.show_error(error, operation, file_path, parent)

def show_contextual_help(topic: str, parent=None):
    """Show contextual help dialog"""
    dialog = ContextualHelpDialog(topic, parent)
    dialog.exec()
