#!/usr/bin/env python3
"""
Integration test for lazy loading with main Adventure Chess Creator application
"""

import sys
import os
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestMainAppIntegration(unittest.TestCase):
    """Test lazy loading integration with main application"""
    
    def setUp(self):
        """Set up test environment"""
        # Mock PyQt6 to avoid GUI dependencies in tests
        self.qt_mock = MagicMock()
        sys.modules['PyQt6'] = self.qt_mock
        sys.modules['PyQt6.QtWidgets'] = self.qt_mock
        sys.modules['PyQt6.QtCore'] = self.qt_mock
        sys.modules['PyQt6.QtGui'] = self.qt_mock
        
        # Import after mocking
        from lazy_data_integration import get_lazy_data_manager, reset_lazy_data_manager
        from lazy_editor_integration import apply_all_lazy_patches
        
        self.get_lazy_data_manager = get_lazy_data_manager
        self.reset_lazy_data_manager = reset_lazy_data_manager
        self.apply_all_lazy_patches = apply_all_lazy_patches
    
    def tearDown(self):
        """Clean up test environment"""
        # Reset lazy data manager
        self.reset_lazy_data_manager()
        
        # Clean up mocked modules
        modules_to_remove = [m for m in sys.modules.keys() if m.startswith('PyQt6')]
        for module in modules_to_remove:
            if module in sys.modules:
                del sys.modules[module]
    
    def test_lazy_data_manager_initialization(self):
        """Test that lazy data manager initializes correctly"""
        data_manager = self.get_lazy_data_manager()
        
        self.assertIsNotNone(data_manager)
        self.assertTrue(hasattr(data_manager, 'load_piece_lazy'))
        self.assertTrue(hasattr(data_manager, 'load_ability_lazy'))
        self.assertTrue(hasattr(data_manager, 'preload_recent_files'))
    
    def test_lazy_patches_application(self):
        """Test that lazy patches can be applied to mock main app"""
        # Create mock main app
        mock_main_app = MagicMock()
        mock_main_app._piece_editor = None
        mock_main_app._ability_editor = None
        
        # Apply patches (should not raise exceptions)
        try:
            self.apply_all_lazy_patches(mock_main_app)
            success = True
        except Exception as e:
            print(f"Error applying patches: {e}")
            success = False
        
        self.assertTrue(success, "Lazy patches should apply without errors")
    
    def test_data_manager_file_operations(self):
        """Test data manager file operations"""
        data_manager = self.get_lazy_data_manager()

        # Test that methods exist and are callable
        self.assertTrue(callable(data_manager.load_piece_lazy))
        self.assertTrue(callable(data_manager.load_ability_lazy))
        self.assertTrue(callable(data_manager.get_pieces_manager))
        self.assertTrue(callable(data_manager.get_abilities_manager))
    
    def test_lazy_loading_system_components(self):
        """Test that all lazy loading system components are available"""
        # Test imports work
        try:
            from lazy_loading_system import LazyDataManager, get_lazy_manager
            from lazy_data_integration import LazyIntegratedDataManager
            from lazy_ui_components import LazyComboBox, LazyListWidget
            from lazy_editor_integration import LazyPieceEditorPatches, LazyAbilityEditorPatches
            
            components_available = True
        except ImportError as e:
            print(f"Import error: {e}")
            components_available = False
        
        self.assertTrue(components_available, "All lazy loading components should be importable")
    
    def test_cache_integration(self):
        """Test cache integration with lazy loading"""
        try:
            from enhanced_cache_manager import get_cache_manager
            cache_manager = get_cache_manager()
            
            data_manager = self.get_lazy_data_manager()
            
            # Verify cache manager is integrated
            self.assertIsNotNone(data_manager.cache_manager)
            
        except Exception as e:
            self.fail(f"Cache integration failed: {e}")
    
    def test_performance_benefits(self):
        """Test that lazy loading provides performance benefits"""
        data_manager = self.get_lazy_data_manager()
        
        # Test metadata loading (should be fast)
        import time
        start_time = time.time()
        
        # This should complete quickly since it's just metadata
        try:
            metadata = data_manager.lazy_manager.load_file_metadata_lazy("data/pieces", ".json")
            end_time = time.time()
            
            # Should complete in under 1 second for metadata
            self.assertLess(end_time - start_time, 1.0, "Metadata loading should be fast")
            
        except Exception as e:
            # If directories don't exist, that's okay for this test
            print(f"Note: {e}")
    
    def test_error_handling(self):
        """Test error handling in lazy loading system"""
        data_manager = self.get_lazy_data_manager()
        
        # Test loading non-existent file (should handle gracefully)
        result = None
        error_occurred = False
        
        def on_error_callback(data):
            nonlocal result, error_occurred
            result = data
            if data is None:
                error_occurred = True
        
        try:
            # This should handle the error gracefully
            data_manager.load_piece_lazy("nonexistent_file", on_error_callback)

            # Give it a moment to process
            import time
            time.sleep(0.1)

            # Should not crash the system
            self.assertTrue(True, "Error handling should not crash the system")

        except ImportError as e:
            if "PIECE_EXTENSION" in str(e):
                # Skip this test if config constants are not available
                self.skipTest(f"Config constants not available in test environment: {e}")
            else:
                self.fail(f"Unexpected import error: {e}")
        except Exception as e:
            self.fail(f"Error handling failed: {e}")

def run_integration_tests():
    """Run integration tests"""
    print("="*60)
    print("LAZY LOADING - MAIN APP INTEGRATION TESTS")
    print("="*60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestMainAppIntegration)
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print(f"\nIntegration Test Results:")
    print(f"  Tests Run: {result.testsRun}")
    print(f"  Failures: {len(result.failures)}")
    print(f"  Errors: {len(result.errors)}")
    print(f"  Success: {'✅ PASSED' if result.wasSuccessful() else '❌ FAILED'}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)
