"""
UI Utilities for Adventure Chess
Responsive design helpers and common UI components
"""
from PyQt6.QtWidgets import (
    QScrollArea, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
    QSizePolicy, QFrame, Q<PERSON>plitter
)
from PyQt6.QtCore import Qt, QSize

from config import LAYOUT_MARGIN, LAYOUT_SPACING, WIDGET_SPACING

class ResponsiveScrollArea(QScrollArea):
    """Scroll area that adapts to content and window size with zoom support"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setFrameStyle(QFrame.Shape.NoFrame)

        # Zoom functionality
        self.zoom_factor = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 2.0

        # Create content widget with proper sizing
        self.content_widget = QWidget()
        self.content_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        self.content_layout.setSpacing(LAYOUT_SPACING)

        self.setWidget(self.content_widget)

        # Enable smooth scrolling and ensure proper content bounds
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Ensure content widget can expand properly
        self.content_widget.setMinimumSize(400, 600)  # Minimum content size
    
    def add_widget(self, widget):
        """Add a widget to the scroll area content"""
        self.content_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """Add a layout to the scroll area content"""
        self.content_layout.addLayout(layout)
    
    def add_stretch(self):
        """Add stretch to the scroll area content"""
        self.content_layout.addStretch()
    
    def clear_content(self):
        """Clear all content from the scroll area"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                self.clear_layout(child.layout())
    
    def clear_layout(self, layout):
        """Recursively clear a layout"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                self.clear_layout(child.layout())

    def zoom_in(self):
        """Zoom in the content"""
        if self.zoom_factor < self.max_zoom:
            self.zoom_factor = min(self.zoom_factor * 1.2, self.max_zoom)
            self.apply_zoom()

    def zoom_out(self):
        """Zoom out the content"""
        if self.zoom_factor > self.min_zoom:
            self.zoom_factor = max(self.zoom_factor / 1.2, self.min_zoom)
            self.apply_zoom()

    def reset_zoom(self):
        """Reset zoom to 100%"""
        self.zoom_factor = 1.0
        self.apply_zoom()

    def apply_zoom(self):
        """Apply current zoom factor to content"""
        if hasattr(self.content_widget, 'setStyleSheet'):
            # Apply zoom using CSS transform
            self.content_widget.setStyleSheet(f"""
                QWidget {{
                    font-size: {int(12 * self.zoom_factor)}px;
                }}
                QLabel {{
                    font-size: {int(11 * self.zoom_factor)}px;
                }}
                QPushButton {{
                    font-size: {int(10 * self.zoom_factor)}px;
                    padding: {int(4 * self.zoom_factor)}px;
                }}
                QLineEdit, QTextEdit, QSpinBox, QComboBox {{
                    font-size: {int(11 * self.zoom_factor)}px;
                    padding: {int(2 * self.zoom_factor)}px;
                }}
            """)

    def wheelEvent(self, a0):
        """Handle mouse wheel for zoom when Ctrl is pressed"""
        from PyQt6.QtCore import Qt
        if a0.modifiers() & Qt.KeyboardModifier.ControlModifier:
            # Zoom with Ctrl+Wheel
            if a0.angleDelta().y() > 0:
                self.zoom_in()
            else:
                self.zoom_out()
            a0.accept()
        else:
            # Normal scrolling
            super().wheelEvent(a0)

class ResponsiveWidget(QWidget):
    """Base widget with responsive behavior"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.min_width = 300
        self.min_height = 200
        self.preferred_width = 600
        self.preferred_height = 400
        
        # Set size policies
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    def set_responsive_size(self, min_width, min_height, preferred_width, preferred_height):
        """Set responsive size parameters"""
        self.min_width = min_width
        self.min_height = min_height
        self.preferred_width = preferred_width
        self.preferred_height = preferred_height
        
        self.setMinimumSize(min_width, min_height)
    
    def sizeHint(self):
        """Return preferred size"""
        return QSize(self.preferred_width, self.preferred_height)

class ResponsiveLayout:
    """Helper class for creating responsive layouts"""
    
    @staticmethod
    def create_vbox(margin=None, spacing=None):
        """Create a responsive vertical box layout"""
        layout = QVBoxLayout()
        layout.setContentsMargins(
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN
        )
        layout.setSpacing(spacing if spacing is not None else LAYOUT_SPACING)
        return layout
    
    @staticmethod
    def create_hbox(margin=None, spacing=None):
        """Create a responsive horizontal box layout"""
        layout = QHBoxLayout()
        layout.setContentsMargins(
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN
        )
        layout.setSpacing(spacing if spacing is not None else LAYOUT_SPACING)
        return layout
    
    @staticmethod
    def create_grid(margin=None, spacing=None):
        """Create a responsive grid layout"""
        layout = QGridLayout()
        layout.setContentsMargins(
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN,
            margin if margin is not None else LAYOUT_MARGIN
        )
        layout.setSpacing(spacing if spacing is not None else WIDGET_SPACING)
        return layout
    
    @staticmethod
    def create_form_layout():
        """Create a form layout optimized for responsive design"""
        from PyQt6.QtWidgets import QFormLayout
        layout = QFormLayout()
        layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        layout.setSpacing(WIDGET_SPACING)
        layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        return layout

class ResponsiveSplitter(QSplitter):
    """Splitter that maintains proportions when resizing"""
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setChildrenCollapsible(False)
        self.initial_sizes = []
    
    def addWidget(self, widget):
        """Add widget and track initial size"""
        super().addWidget(widget)
        if hasattr(widget, 'sizeHint'):
            if self.orientation() == Qt.Orientation.Horizontal:
                self.initial_sizes.append(widget.sizeHint().width())
            else:
                self.initial_sizes.append(widget.sizeHint().height())
        else:
            self.initial_sizes.append(200)  # Default size
    
    def resizeEvent(self, a0):
        """Maintain proportions when resizing"""
        super().resizeEvent(a0)
        if self.initial_sizes and self.count() > 0:
            total_initial = sum(self.initial_sizes)
            if total_initial > 0:
                if self.orientation() == Qt.Orientation.Horizontal:
                    available_width = self.width() - (self.count() - 1) * self.handleWidth()
                    new_sizes = [int(available_width * size / total_initial) for size in self.initial_sizes]
                else:
                    available_height = self.height() - (self.count() - 1) * self.handleWidth()
                    new_sizes = [int(available_height * size / total_initial) for size in self.initial_sizes]
                
                self.setSizes(new_sizes)

class TabWidgetResponsive:
    """Helper for making tab widgets responsive"""
    
    @staticmethod
    def setup_tab_widget(tab_widget):
        """Setup a tab widget for responsive behavior"""
        tab_widget.setTabPosition(tab_widget.TabPosition.North)
        tab_widget.setMovable(False)
        tab_widget.setTabsClosable(False)
        
        # Make tabs expand to fill available space
        tab_widget.tabBar().setExpanding(True)
        
        return tab_widget

def setup_responsive_window(window, default_size, min_size):
    """Setup a window for responsive behavior with improved screen size detection and constraints"""
    from PyQt6.QtWidgets import QApplication

    # Get screen geometry
    screen = QApplication.primaryScreen()
    screen_geometry = screen.availableGeometry()
    screen_width = screen_geometry.width()
    screen_height = screen_geometry.height()

    # Calculate safe screen usage percentages (leave room for OS elements)
    max_width_percentage = 0.90  # Use max 90% of screen width
    max_height_percentage = 0.85  # Use max 85% of screen height

    # Calculate maximum allowed dimensions
    max_allowed_width = int(screen_width * max_width_percentage)
    max_allowed_height = int(screen_height * max_height_percentage)

    # Calculate responsive sizes based on screen size
    responsive_width = min(default_size[0], max_allowed_width)
    responsive_height = min(default_size[1], max_allowed_height)

    # Adjust minimum sizes for very small screens
    adjusted_min_width = min(min_size[0], max_allowed_width)
    adjusted_min_height = min(min_size[1], max_allowed_height)

    # Ensure minimum size is respected but doesn't exceed screen
    final_width = max(responsive_width, adjusted_min_width)
    final_height = max(responsive_height, adjusted_min_height)

    # Final safety check - ensure window fits on screen
    final_width = min(final_width, max_allowed_width)
    final_height = min(final_height, max_allowed_height)

    # Set minimum size (adjusted for small screens)
    window.setMinimumSize(adjusted_min_width, adjusted_min_height)
    window.resize(final_width, final_height)

    # Set size policy for proper scaling
    window.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    # Center window on screen
    window.move(
        (screen_width - final_width) // 2,
        (screen_height - final_height) // 2
    )

    return window

def get_screen_size_category():
    """Determine screen size category for adaptive UI behavior"""
    from PyQt6.QtWidgets import QApplication

    screen = QApplication.primaryScreen()
    screen_geometry = screen.availableGeometry()
    screen_width = screen_geometry.width()
    screen_height = screen_geometry.height()

    # Define screen size categories
    if screen_width <= 800 or screen_height <= 600:
        return "very_small"
    elif screen_width <= 1024 or screen_height <= 768:
        return "small"
    elif screen_width <= 1366 or screen_height <= 900:
        return "medium"
    elif screen_width <= 1920 or screen_height <= 1080:
        return "large"
    else:
        return "very_large"

def setup_responsive_window_with_fallback(window, default_size, min_size, small_screen_size=None, compact_mode_threshold="small"):
    """Setup responsive window with fallback for very small screens"""
    from PyQt6.QtWidgets import QApplication

    screen_category = get_screen_size_category()

    # Get screen dimensions for small screen detection
    screen = QApplication.primaryScreen()
    screen_geometry = screen.availableGeometry()
    screen_width = screen_geometry.width()
    screen_height = screen_geometry.height()

    # Use small screen fallback sizes if provided and screen is very small
    if (screen_category == "very_small" and small_screen_size is not None and
        (screen_width <= 800 or screen_height <= 600)):

        # Use small screen fallback as both default and minimum
        effective_default = small_screen_size
        effective_min = small_screen_size
    else:
        effective_default = default_size
        effective_min = min_size

    # Enable compact mode for small screens
    if screen_category in ["very_small", "small"] and hasattr(window, 'enable_compact_mode'):
        window.enable_compact_mode(True)

    # Use enhanced responsive setup
    return setup_responsive_window(window, effective_default, effective_min)

def make_widget_responsive(widget, min_width=None, min_height=None):
    """Make any widget responsive"""
    if min_width and min_height:
        widget.setMinimumSize(min_width, min_height)
    
    widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    return widget

def create_scrollable_content(content_widget):
    """Wrap content in a scroll area"""
    scroll_area = ResponsiveScrollArea()
    scroll_area.setWidget(content_widget)
    return scroll_area

def optimize_layout_for_small_screens(layout, compact_mode=False):
    """Optimize layout spacing for smaller screens"""
    if compact_mode:
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(3)
    else:
        layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        layout.setSpacing(LAYOUT_SPACING)