"""
Optimized File Integration for Adventure Chess Creator

This module integrates the file system optimizer with the existing lazy loading system
to provide enhanced performance and search capabilities:

- Enhanced data managers with search integration
- Optimized file loading with indexing
- Search-aware UI components
- Performance monitoring and analytics

Key Features:
- Seamless integration with existing lazy loading
- Enhanced search capabilities in editors
- Optimized directory scanning
- Performance analytics and monitoring
"""

import logging
from typing import List, Dict, Any, Optional, Callable
from pathlib import Path

from file_system_optimizer import get_file_system_optimizer, SearchResult
from lazy_data_integration import LazyIntegratedDataManager, get_lazy_data_manager
from enhanced_search_components import EnhancedSearchWidget

logger = logging.getLogger(__name__)

class OptimizedDataManager(LazyIntegratedDataManager):
    """
    Enhanced data manager that combines lazy loading with file system optimization
    """
    
    def __init__(self):
        super().__init__()
        self.optimizer = get_file_system_optimizer()
        
        # Initialize index on startup
        self._initialize_index()
    
    def _initialize_index(self):
        """Initialize the file index in background"""
        def index_worker():
            try:
                from config import PIECES_DIR, ABILITIES_DIR
                indexed_count = self.optimizer.update_index([PIECES_DIR, ABILITIES_DIR])
                logger.info(f"File index initialized: {indexed_count} files indexed")
            except Exception as e:
                logger.error(f"Error initializing file index: {e}")
        
        # Run indexing in background
        import threading
        index_thread = threading.Thread(target=index_worker, daemon=True)
        index_thread.start()
    
    def search_pieces(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """Search pieces using the optimized index"""
        return self.optimizer.search_files(
            query=query,
            file_type="piece",
            max_results=max_results
        )
    
    def search_abilities(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """Search abilities using the optimized index"""
        return self.optimizer.search_files(
            query=query,
            file_type="ability",
            max_results=max_results
        )
    
    def get_piece_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """Get piece name suggestions for autocomplete"""
        return self.optimizer.get_file_suggestions(
            partial_name=partial_name,
            file_type="piece",
            limit=limit
        )
    
    def get_ability_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """Get ability name suggestions for autocomplete"""
        return self.optimizer.get_file_suggestions(
            partial_name=partial_name,
            file_type="ability",
            limit=limit
        )
    
    def get_optimized_file_list(self, directory: str) -> Dict[str, Any]:
        """Get optimized file list using cached directory scanning"""
        return self.optimizer.optimize_directory_scanning(directory)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get combined performance statistics"""
        optimizer_stats = self.optimizer.get_index_statistics()
        
        # Combine with lazy loading stats if available
        combined_stats = {
            'file_system_optimization': optimizer_stats,
            'lazy_loading': {
                'cache_manager_active': self.cache_manager is not None,
                'pieces_manager_active': self.pieces_manager is not None,
                'abilities_manager_active': self.abilities_manager is not None
            }
        }
        
        return combined_stats
    
    def preload_search_results(self, search_results: List[SearchResult], 
                              callback: Optional[Callable] = None):
        """Preload files from search results for faster access"""
        try:
            for result in search_results:
                filename = result.filename
                
                if result.file_type == "piece":
                    self.load_piece_lazy(filename, callback, priority=3)
                elif result.file_type == "ability":
                    self.load_ability_lazy(filename, callback, priority=3)
                    
        except Exception as e:
            logger.error(f"Error preloading search results: {e}")

class OptimizedFileSelector:
    """
    Enhanced file selector that uses both lazy loading and search optimization
    """
    
    def __init__(self, file_type: str = "piece"):
        self.file_type = file_type
        self.data_manager = get_optimized_data_manager()
        self.search_widget = None
    
    def create_search_widget(self, parent=None) -> EnhancedSearchWidget:
        """Create an enhanced search widget for file selection"""
        if self.search_widget is None:
            self.search_widget = EnhancedSearchWidget(parent)
            
            # Configure for specific file type
            if self.file_type == "piece":
                self.search_widget.file_type_combo.setCurrentText("Pieces")
            elif self.file_type == "ability":
                self.search_widget.file_type_combo.setCurrentText("Abilities")
        
        return self.search_widget
    
    def get_file_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """Get file suggestions for the configured type"""
        if self.file_type == "piece":
            return self.data_manager.get_piece_suggestions(partial_name, limit)
        elif self.file_type == "ability":
            return self.data_manager.get_ability_suggestions(partial_name, limit)
        return []
    
    def search_files(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """Search files of the configured type"""
        if self.file_type == "piece":
            return self.data_manager.search_pieces(query, max_results)
        elif self.file_type == "ability":
            return self.data_manager.search_abilities(query, max_results)
        return []

class PerformanceMonitor:
    """
    Monitor and report on file system performance
    """
    
    def __init__(self):
        self.data_manager = get_optimized_data_manager()
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        return self.data_manager.get_performance_stats()
    
    def generate_performance_report(self) -> str:
        """Generate a human-readable performance report"""
        try:
            stats = self.get_comprehensive_stats()
            
            report = "📊 Adventure Chess Creator - Performance Report\n"
            report += "=" * 50 + "\n\n"
            
            # File System Optimization Stats
            fs_stats = stats.get('file_system_optimization', {})
            report += "🗂️ File System Optimization:\n"
            report += f"  Total Files Indexed: {fs_stats.get('total_files', 0)}\n"
            report += f"  Total Size: {fs_stats.get('total_size_bytes', 0) / 1024:.1f} KB\n"
            
            by_type = fs_stats.get('by_type', {})
            for file_type, type_stats in by_type.items():
                report += f"  {file_type.title()}s: {type_stats.get('count', 0)} files\n"
            
            # Search Performance
            search_stats = fs_stats.get('search_stats', {})
            report += f"\n🔍 Search Performance:\n"
            report += f"  Total Searches: {search_stats.get('total_searches', 0)}\n"
            report += f"  Average Search Time: {search_stats.get('avg_search_time_ms', 0):.2f}ms\n"
            report += f"  Cache Hits: {search_stats.get('cache_hits', 0)}\n"
            
            # Lazy Loading Stats
            lazy_stats = stats.get('lazy_loading', {})
            report += f"\n⚡ Lazy Loading Status:\n"
            report += f"  Cache Manager: {'✅ Active' if lazy_stats.get('cache_manager_active') else '❌ Inactive'}\n"
            report += f"  Pieces Manager: {'✅ Active' if lazy_stats.get('pieces_manager_active') else '❌ Inactive'}\n"
            report += f"  Abilities Manager: {'✅ Active' if lazy_stats.get('abilities_manager_active') else '❌ Inactive'}\n"
            
            report += "\n" + "=" * 50 + "\n"
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return f"Error generating performance report: {e}"
    
    def log_performance_summary(self):
        """Log a performance summary"""
        try:
            report = self.generate_performance_report()
            logger.info(f"Performance Summary:\n{report}")
        except Exception as e:
            logger.error(f"Error logging performance summary: {e}")

# Integration patches for existing editors
class EditorIntegrationPatches:
    """
    Patches to integrate optimized file system with existing editors
    """
    
    @staticmethod
    def patch_piece_editor(piece_editor):
        """Add optimized search to piece editor"""
        try:
            # Add search widget if not already present
            if not hasattr(piece_editor, 'optimized_search'):
                piece_editor.optimized_search = OptimizedFileSelector("piece")
                
                # Replace or enhance existing search functionality
                if hasattr(piece_editor, 'ability_search_edit'):
                    # Enhance ability search with suggestions
                    def on_ability_search_changed():
                        query = piece_editor.ability_search_edit.text()
                        if len(query) >= 2:
                            suggestions = piece_editor.optimized_search.data_manager.get_ability_suggestions(query, 5)
                            # Display suggestions in UI
                            if hasattr(piece_editor, 'ability_suggestions_list'):
                                piece_editor.ability_suggestions_list.clear()
                                for suggestion in suggestions:
                                    piece_editor.ability_suggestions_list.addItem(suggestion)
                                piece_editor.ability_suggestions_list.setVisible(len(suggestions) > 0)
                        else:
                            # Hide suggestions when query is too short
                            if hasattr(piece_editor, 'ability_suggestions_list'):
                                piece_editor.ability_suggestions_list.setVisible(False)
                    
                    piece_editor.ability_search_edit.textChanged.connect(on_ability_search_changed)
            
            logger.info("Piece editor enhanced with optimized search")
            
        except Exception as e:
            logger.error(f"Error patching piece editor: {e}")
    
    @staticmethod
    def patch_ability_editor(ability_editor):
        """Add optimized search to ability editor"""
        try:
            # Add search widget if not already present
            if not hasattr(ability_editor, 'optimized_search'):
                ability_editor.optimized_search = OptimizedFileSelector("ability")
                
                # Enhance load combo with search suggestions
                if hasattr(ability_editor, 'load_combo'):
                    def on_load_combo_text_changed():
                        query = ability_editor.load_combo.currentText()
                        if len(query) >= 2:
                            suggestions = ability_editor.optimized_search.get_file_suggestions(query, 10)
                            
                            # Update combo box items
                            ability_editor.load_combo.clear()
                            ability_editor.load_combo.addItems(suggestions)
                            ability_editor.load_combo.setEditText(query)
                    
                    ability_editor.load_combo.editTextChanged.connect(on_load_combo_text_changed)
            
            logger.info("Ability editor enhanced with optimized search")
            
        except Exception as e:
            logger.error(f"Error patching ability editor: {e}")
    
    @staticmethod
    def apply_all_patches(main_app):
        """Apply all optimization patches to the main application"""
        try:
            # Patch editors if they exist
            if hasattr(main_app, 'piece_editor'):
                EditorIntegrationPatches.patch_piece_editor(main_app.piece_editor)
            
            if hasattr(main_app, 'ability_editor'):
                EditorIntegrationPatches.patch_ability_editor(main_app.ability_editor)
            
            # Initialize performance monitoring
            monitor = PerformanceMonitor()
            monitor.log_performance_summary()
            
            logger.info("All file system optimization patches applied successfully")
            
        except Exception as e:
            logger.error(f"Error applying optimization patches: {e}")

# Global optimized data manager instance
_optimized_data_manager: Optional[OptimizedDataManager] = None

def get_optimized_data_manager() -> OptimizedDataManager:
    """Get or create the global optimized data manager instance"""
    global _optimized_data_manager
    if _optimized_data_manager is None:
        _optimized_data_manager = OptimizedDataManager()
    return _optimized_data_manager

def reset_optimized_data_manager():
    """Reset the global optimized data manager (for testing)"""
    global _optimized_data_manager
    if _optimized_data_manager:
        _optimized_data_manager.shutdown()
    _optimized_data_manager = None

# Convenience function for easy integration
def enable_file_system_optimization(main_app=None):
    """
    Enable file system optimization for the entire application
    
    Args:
        main_app: Main application instance (optional)
    
    Returns:
        OptimizedDataManager instance
    """
    try:
        # Get optimized data manager (initializes if needed)
        data_manager = get_optimized_data_manager()
        
        # Apply patches if main app provided
        if main_app:
            EditorIntegrationPatches.apply_all_patches(main_app)
        
        # Log success
        monitor = PerformanceMonitor()
        logger.info("File system optimization enabled successfully")
        monitor.log_performance_summary()
        
        return data_manager
        
    except Exception as e:
        logger.error(f"Error enabling file system optimization: {e}")
        raise
