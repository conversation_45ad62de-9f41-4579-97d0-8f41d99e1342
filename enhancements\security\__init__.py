"""
Security Enhancement Module

Consolidated security and reliability features:
- File path validation and input sanitization
- Automatic crash recovery mechanisms
- Secure file operations with proper validation
- Security-enhanced data manager wrappers
"""

from .security_system import (
    SecurityValidator,
    CrashRecoveryManager,
    SecureDataManager,
    SecureDirectDataManager,
    SecurePydanticDataManager,
    security_validator,
    crash_recovery_manager,
    secure_data_manager,
    secure_direct_data_manager,
    secure_pydantic_data_manager,
    get_recovery_options
)

# Alias for backward compatibility
SecurityEnhancementManager = SecureDataManager

__all__ = [
    'SecurityValidator',
    'CrashRecoveryManager',
    'SecureDataManager',
    'SecureDirectDataManager',
    'SecurePydanticDataManager',
    'SecurityEnhancementManager',  # Alias for SecureDataManager
    'security_validator',
    'crash_recovery_manager',
    'secure_data_manager',
    'secure_direct_data_manager',
    'secure_pydantic_data_manager',
    'get_recovery_options'
]
