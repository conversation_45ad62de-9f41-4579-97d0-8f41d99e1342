"""
UI Components Package for Adventure Chess Creator

This package contains refactored UI components from the original
ui_shared_components.py file, organized into focused modules:

- grid_components.py: Grid-based widgets (GridToggleWidget, AreaEffectGridWidget)
- file_operations.py: File operation widgets (FileOperationsWidget)
- status_display.py: Status and validation widgets (ValidationStatusWidget)
- ui_utilities.py: Utility functions for UI creation

The original ui_shared_components.py (462 lines) has been refactored
into 4 focused modules for better maintainability and organization.

Original file archived at: archive/ui_components_refactoring/ui_shared_components_original.py
"""

# Import all components for backward compatibility
from .grid_components import GridToggleWidget, AreaEffectGridWidget
from .file_operations import FileOperationsWidget
from .status_display import ValidationStatusWidget
from .ui_utilities import (
    create_section_header,
    create_info_box,
    create_legend_item,
    create_dialog_buttons,
    create_grid_instructions
)

# Re-export all components
__all__ = [
    # Grid components
    "GridToggleWidget",
    "AreaEffectGridWidget",
    
    # File operations
    "FileOperationsWidget",
    
    # Status display
    "ValidationStatusWidget",
    
    # Utility functions
    "create_section_header",
    "create_info_box",
    "create_legend_item",
    "create_dialog_buttons",
    "create_grid_instructions"
]

# Version info
__version__ = "1.1.0"
__refactored__ = "2025-06-25"
