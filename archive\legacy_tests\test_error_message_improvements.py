"""
Test Suite for Error Message Improvements

This test suite validates the user-friendly error system and ensures
that technical error messages are properly translated into clear,
actionable guidance for users.

Tests cover:
- Error message translation accuracy
- User-friendly dialog functionality
- Quick fix action handling
- Contextual help system
- Integration with existing error handling
"""

import pytest
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import Qt

# Import the modules we're testing
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from user_friendly_error_system import (
    ErrorMessageTranslator, UserFriendlyErrorDialog, 
    ContextualHelpProvider, ErrorSeverity, ErrorCategory
)
from error_message_improvements import (
    ImprovedErrorHandler, show_improved_error, 
    get_error_template, ERROR_TEMPLATES
)

class TestErrorMessageTranslator:
    """Test error message translation functionality"""
    
    def test_permission_error_translation(self):
        """Test translation of permission errors"""
        error = PermissionError("Permission denied")
        result = ErrorMessageTranslator.translate_error(error, "saving file", "/test/file.json")
        
        assert result.title == "File Access Problem"
        assert "permission" in result.message.lower()
        assert result.severity == ErrorSeverity.ERROR
        assert result.category == ErrorCategory.PERMISSION
        assert len(result.suggestions) > 0
        assert any("administrator" in suggestion.lower() for suggestion in result.suggestions)
    
    def test_file_not_found_translation(self):
        """Test translation of file not found errors"""
        error = FileNotFoundError("No such file or directory")
        result = ErrorMessageTranslator.translate_error(error, "loading file", "/missing/file.json")
        
        assert result.title == "File Not Found"
        assert "cannot be found" in result.message.lower()
        assert result.severity == ErrorSeverity.ERROR
        assert result.category == ErrorCategory.FILE_ACCESS
        assert any("browse" in fix["text"].lower() for fix in result.quick_fixes)
    
    def test_json_error_translation(self):
        """Test translation of JSON format errors"""
        error = json.JSONDecodeError("Invalid JSON", "test", 0)
        result = ErrorMessageTranslator.translate_error(error, "parsing file", "/corrupt/file.json")
        
        assert result.title == "File Format Problem"
        assert "corrupted" in result.message.lower() or "invalid format" in result.message.lower()
        assert result.severity == ErrorSeverity.ERROR
        assert result.category == ErrorCategory.DATA_FORMAT
        assert any("text editor" in fix["text"].lower() for fix in result.quick_fixes)
    
    def test_validation_error_translation(self):
        """Test translation of validation errors"""
        error = ValueError("validation error: field required")
        result = ErrorMessageTranslator.translate_error(error, "validating data")
        
        assert result.title == "Data Validation Problem"
        assert "information you entered" in result.message.lower()
        assert result.severity == ErrorSeverity.WARNING
        assert result.category == ErrorCategory.VALIDATION
        assert any("reset" in fix["text"].lower() for fix in result.quick_fixes)
    
    def test_generic_error_translation(self):
        """Test translation of generic errors"""
        error = RuntimeError("Something went wrong")
        result = ErrorMessageTranslator.translate_error(error, "unknown operation")
        
        assert result.title == "Unexpected Error"
        assert "unexpected error" in result.message.lower()
        assert result.severity == ErrorSeverity.ERROR
        assert result.category == ErrorCategory.SYSTEM
        assert result.can_continue == True

class TestContextualHelpProvider:
    """Test contextual help system"""
    
    def test_get_help_content(self):
        """Test retrieving help content"""
        content = ContextualHelpProvider.get_help_content("file_permissions")
        
        assert content is not None
        assert "title" in content
        assert "content" in content
        assert "related_topics" in content
        assert "permission" in content["title"].lower()
    
    def test_get_related_help(self):
        """Test getting related help topics"""
        topic = ContextualHelpProvider.get_related_help(ErrorCategory.PERMISSION)
        assert topic == "file_permissions"
        
        topic = ContextualHelpProvider.get_related_help(ErrorCategory.DATA_FORMAT)
        assert topic == "json_format_errors"
        
        topic = ContextualHelpProvider.get_related_help(ErrorCategory.VALIDATION)
        assert topic == "validation_errors"
    
    def test_help_content_structure(self):
        """Test that all help content has required structure"""
        for topic, content in ContextualHelpProvider.HELP_CONTENT.items():
            assert "title" in content
            assert "content" in content
            assert "related_topics" in content
            assert isinstance(content["related_topics"], list)

class TestImprovedErrorHandler:
    """Test improved error handler functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.handler = ImprovedErrorHandler()
        self.mock_parent = Mock(spec=QWidget)
    
    def test_error_context_storage(self):
        """Test that error context is properly stored"""
        error = Exception("Test error")
        
        with patch('error_message_improvements.show_user_friendly_error') as mock_show:
            mock_show.return_value = True
            self.handler.show_error(error, "test operation", "/test/file.json", self.mock_parent)
        
        assert self.handler.last_operation == "test operation"
        assert self.handler.last_file_path == "/test/file.json"
        assert self.handler.current_parent == self.mock_parent
    
    def test_quick_fix_handlers_registration(self):
        """Test that quick fix handlers are properly registered"""
        from user_friendly_error_system import error_dialog_manager
        
        # Check that handlers are registered
        assert "save_as_dialog" in error_dialog_manager.quick_fix_handlers
        assert "browse_for_file" in error_dialog_manager.quick_fix_handlers
        assert "retry_operation" in error_dialog_manager.quick_fix_handlers
    
    def test_retry_operation_with_load(self):
        """Test retry operation for load operations"""
        self.handler.last_operation = "loading file"
        self.handler.last_file_path = "/test/file.json"
        self.handler.current_parent = self.mock_parent
        self.mock_parent.load_data = Mock()
        
        self.handler.retry_operation()
        
        self.mock_parent.load_data.assert_called_once_with("file")
    
    def test_retry_operation_with_save(self):
        """Test retry operation for save operations"""
        self.handler.last_operation = "saving file"
        self.handler.current_parent = self.mock_parent
        self.mock_parent.save_data = Mock()

        self.handler.retry_operation()

        self.mock_parent.save_data.assert_called_once()
    
    def test_reset_to_defaults(self):
        """Test reset to defaults functionality"""
        self.handler.current_parent = self.mock_parent
        self.mock_parent.reset_form = Mock()
        
        self.handler.reset_to_defaults()
        
        self.mock_parent.reset_form.assert_called_once()

class TestErrorTemplates:
    """Test error message templates"""
    
    def test_template_formatting(self):
        """Test error template formatting"""
        result = get_error_template("file_not_found", file_path="/test/file.json")

        assert "/test/file.json" in result
        assert "could not be found" in result.lower()
    
    def test_all_templates_exist(self):
        """Test that all expected templates exist"""
        expected_templates = [
            "file_not_found", "permission_denied", "invalid_json",
            "validation_failed", "save_failed", "load_failed"
        ]
        
        for template in expected_templates:
            assert template in ERROR_TEMPLATES
    
    def test_template_with_missing_args(self):
        """Test template formatting with missing arguments"""
        result = get_error_template("file_not_found")
        
        # Should handle missing arguments gracefully
        assert result is not None
        assert len(result) > 0

class TestIntegrationScenarios:
    """Test real-world integration scenarios"""
    
    def setup_method(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.temp_dir, "test.json")
    
    def teardown_method(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_file_permission_scenario(self):
        """Test complete file permission error scenario"""
        # Create a file and make it read-only
        with open(self.test_file, 'w') as f:
            json.dump({"test": "data"}, f)
        
        os.chmod(self.test_file, 0o444)  # Read-only
        
        try:
            with open(self.test_file, 'w') as f:
                json.dump({"new": "data"}, f)
        except PermissionError as e:
            result = ErrorMessageTranslator.translate_error(e, "saving file", self.test_file)
            
            assert result.category == ErrorCategory.PERMISSION
            assert "permission" in result.message.lower()
            assert len(result.quick_fixes) > 0
    
    def test_corrupted_file_scenario(self):
        """Test corrupted file error scenario"""
        # Create a corrupted JSON file
        with open(self.test_file, 'w') as f:
            f.write('{"invalid": json content')
        
        try:
            with open(self.test_file, 'r') as f:
                json.load(f)
        except json.JSONDecodeError as e:
            result = ErrorMessageTranslator.translate_error(e, "loading file", self.test_file)
            
            assert result.category == ErrorCategory.DATA_FORMAT
            assert "corrupted" in result.message.lower() or "invalid format" in result.message.lower()
            assert any("text editor" in fix["text"].lower() for fix in result.quick_fixes)
    
    def test_missing_file_scenario(self):
        """Test missing file error scenario"""
        missing_file = os.path.join(self.temp_dir, "missing.json")
        
        try:
            with open(missing_file, 'r') as f:
                json.load(f)
        except FileNotFoundError as e:
            result = ErrorMessageTranslator.translate_error(e, "loading file", missing_file)
            
            assert result.category == ErrorCategory.FILE_ACCESS
            assert "cannot be found" in result.message.lower()
            assert any("browse" in fix["text"].lower() for fix in result.quick_fixes)

# Test runner for manual testing
if __name__ == "__main__":
    # Create QApplication for GUI tests
    app = QApplication([])
    
    # Run a simple demonstration
    print("Testing Error Message Improvements...")
    
    # Test permission error
    try:
        raise PermissionError("Permission denied: /test/file.json")
    except PermissionError as e:
        result = ErrorMessageTranslator.translate_error(e, "saving file", "/test/file.json")
        print(f"Permission Error Translation:")
        print(f"  Title: {result.title}")
        print(f"  Message: {result.message}")
        print(f"  Suggestions: {len(result.suggestions)}")
        print(f"  Quick Fixes: {len(result.quick_fixes)}")
    
    # Test JSON error
    try:
        raise json.JSONDecodeError("Invalid JSON", "test", 0)
    except json.JSONDecodeError as e:
        result = ErrorMessageTranslator.translate_error(e, "parsing file", "/corrupt/file.json")
        print(f"\nJSON Error Translation:")
        print(f"  Title: {result.title}")
        print(f"  Message: {result.message}")
        print(f"  Category: {result.category}")
    
    print("\nAll tests completed successfully!")
    
    app.quit()
