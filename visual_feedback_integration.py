"""
Visual Feedback Integration for Adventure Chess Creator

This module integrates the visual feedback enhancements into existing editors
and provides seamless integration with the current application architecture.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QWidget, Q<PERSON>oxLayout, QHBoxLayout

from visual_feedback_enhancements import (
    VisualFeedbackIntegrator,
    EnhancedLoadingIndicator,
    RealTimeValidationWidget,
    OperationFeedbackManager,
    StatusBarEnhancement,
    create_validation_rules_for_piece_editor,
    create_validation_rules_for_ability_editor
)

logger = logging.getLogger(__name__)

class VisualFeedbackManager:
    """Central manager for all visual feedback enhancements"""
    
    def __init__(self):
        self.enhanced_editors = {}
        self.operation_managers = {}
        self.validation_widgets = {}
    
    def enhance_piece_editor(self, piece_editor):
        """Enhance piece editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                piece_editor, 
                enable_validation=True, 
                enable_operations=True
            )
            
            # Add piece-specific validation rules
            validation_rules = create_validation_rules_for_piece_editor()
            VisualFeedbackIntegrator.add_validation_rules(piece_editor, validation_rules)
            
            # Setup data change tracking
            self._setup_piece_editor_tracking(piece_editor)
            
            # Enhance grid widgets if present
            self._enhance_piece_editor_grids(piece_editor)
            
            self.enhanced_editors['piece_editor'] = piece_editor
            logger.info("Piece editor enhanced with visual feedback")
            
        except Exception as e:
            logger.error(f"Error enhancing piece editor: {e}")
    
    def enhance_ability_editor(self, ability_editor):
        """Enhance ability editor with visual feedback"""
        try:
            # Apply basic enhancements
            VisualFeedbackIntegrator.enhance_editor(
                ability_editor,
                enable_validation=True,
                enable_operations=True
            )
            
            # Add ability-specific validation rules
            validation_rules = create_validation_rules_for_ability_editor()
            VisualFeedbackIntegrator.add_validation_rules(ability_editor, validation_rules)
            
            # Setup data change tracking
            self._setup_ability_editor_tracking(ability_editor)
            
            # Enhance grid widgets if present
            self._enhance_ability_editor_grids(ability_editor)
            
            self.enhanced_editors['ability_editor'] = ability_editor
            logger.info("Ability editor enhanced with visual feedback")
            
        except Exception as e:
            logger.error(f"Error enhancing ability editor: {e}")
    
    def _setup_piece_editor_tracking(self, piece_editor):
        """Setup data change tracking for piece editor"""
        if hasattr(piece_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_piece_editor_signals(piece_editor)
    
    def _setup_ability_editor_tracking(self, ability_editor):
        """Setup data change tracking for ability editor"""
        if hasattr(ability_editor, 'validation_widget'):
            # Connect form field changes to validation
            self._connect_ability_editor_signals(ability_editor)
    
    def _connect_piece_editor_signals(self, piece_editor):
        """Connect piece editor signals to validation"""
        try:
            # Connect to common piece editor fields
            if hasattr(piece_editor, 'name_input'):
                piece_editor.name_input.textChanged.connect(
                    lambda: self._update_piece_validation(piece_editor)
                )
            
            if hasattr(piece_editor, 'value_input'):
                piece_editor.value_input.valueChanged.connect(
                    lambda: self._update_piece_validation(piece_editor)
                )
            
            # Setup timer for debounced validation
            if not hasattr(piece_editor, '_validation_timer'):
                piece_editor._validation_timer = QTimer()
                piece_editor._validation_timer.setSingleShot(True)
                piece_editor._validation_timer.timeout.connect(
                    lambda: self._perform_piece_validation(piece_editor)
                )
            
        except Exception as e:
            logger.error(f"Error connecting piece editor signals: {e}")
    
    def _connect_ability_editor_signals(self, ability_editor):
        """Connect ability editor signals to validation"""
        try:
            # Connect to common ability editor fields
            if hasattr(ability_editor, 'name_input'):
                ability_editor.name_input.textChanged.connect(
                    lambda: self._update_ability_validation(ability_editor)
                )
            
            if hasattr(ability_editor, 'cost_input'):
                ability_editor.cost_input.valueChanged.connect(
                    lambda: self._update_ability_validation(ability_editor)
                )
            
            # Setup timer for debounced validation
            if not hasattr(ability_editor, '_validation_timer'):
                ability_editor._validation_timer = QTimer()
                ability_editor._validation_timer.setSingleShot(True)
                ability_editor._validation_timer.timeout.connect(
                    lambda: self._perform_ability_validation(ability_editor)
                )
            
        except Exception as e:
            logger.error(f"Error connecting ability editor signals: {e}")
    
    def _update_piece_validation(self, piece_editor):
        """Trigger piece validation update"""
        if hasattr(piece_editor, '_validation_timer'):
            piece_editor._validation_timer.stop()
            piece_editor._validation_timer.start(500)  # 500ms debounce
    
    def _update_ability_validation(self, ability_editor):
        """Trigger ability validation update"""
        if hasattr(ability_editor, '_validation_timer'):
            ability_editor._validation_timer.stop()
            ability_editor._validation_timer.start(500)  # 500ms debounce
    
    def _perform_piece_validation(self, piece_editor):
        """Perform piece validation"""
        try:
            if hasattr(piece_editor, 'validation_widget'):
                # Collect current piece data
                data = self._collect_piece_data(piece_editor)
                piece_editor.validation_widget.update_data(data)
        except Exception as e:
            logger.error(f"Error performing piece validation: {e}")
    
    def _perform_ability_validation(self, ability_editor):
        """Perform ability validation"""
        try:
            if hasattr(ability_editor, 'validation_widget'):
                # Collect current ability data
                data = self._collect_ability_data(ability_editor)
                ability_editor.validation_widget.update_data(data)
        except Exception as e:
            logger.error(f"Error performing ability validation: {e}")
    
    def _collect_piece_data(self, piece_editor) -> Dict[str, Any]:
        """Collect current piece data for validation"""
        data = {}
        
        try:
            # Collect name
            if hasattr(piece_editor, 'name_input'):
                data['name'] = piece_editor.name_input.text()
            
            # Collect value
            if hasattr(piece_editor, 'value_input'):
                data['value'] = piece_editor.value_input.value()
            
            # Collect movement pattern
            if hasattr(piece_editor, 'movement_manager'):
                data['movement_pattern'] = piece_editor.movement_manager.get_pattern()
            
            # Collect other piece-specific data
            if hasattr(piece_editor, 'collect_data'):
                editor_data = piece_editor.collect_data()
                data.update(editor_data)
                
        except Exception as e:
            logger.error(f"Error collecting piece data: {e}")
        
        return data
    
    def _collect_ability_data(self, ability_editor) -> Dict[str, Any]:
        """Collect current ability data for validation"""
        data = {}
        
        try:
            # Collect name
            if hasattr(ability_editor, 'name_input'):
                data['name'] = ability_editor.name_input.text()
            
            # Collect cost
            if hasattr(ability_editor, 'cost_input'):
                data['cost'] = ability_editor.cost_input.value()
            
            # Collect tags
            if hasattr(ability_editor, 'tag_manager'):
                data['tags'] = ability_editor.tag_manager.get_selected_tags()
            
            # Collect other ability-specific data
            if hasattr(ability_editor, 'collect_data'):
                editor_data = ability_editor.collect_data()
                data.update(editor_data)
                
        except Exception as e:
            logger.error(f"Error collecting ability data: {e}")
        
        return data
    
    def _enhance_piece_editor_grids(self, piece_editor):
        """Enhance grid widgets in piece editor"""
        try:
            # Look for movement pattern grid
            if hasattr(piece_editor, 'movement_manager'):
                movement_manager = piece_editor.movement_manager
                if hasattr(movement_manager, 'pattern_grid'):
                    VisualFeedbackIntegrator.enhance_grid_widget(movement_manager.pattern_grid)
            
            # Look for other grids
            self._find_and_enhance_grids(piece_editor)
            
        except Exception as e:
            logger.error(f"Error enhancing piece editor grids: {e}")
    
    def _enhance_ability_editor_grids(self, ability_editor):
        """Enhance grid widgets in ability editor"""
        try:
            # Look for area effect grids
            if hasattr(ability_editor, 'area_effect_grid'):
                VisualFeedbackIntegrator.enhance_grid_widget(ability_editor.area_effect_grid)
            
            # Look for other grids
            self._find_and_enhance_grids(ability_editor)
            
        except Exception as e:
            logger.error(f"Error enhancing ability editor grids: {e}")
    
    def _find_and_enhance_grids(self, widget):
        """Recursively find and enhance grid widgets"""
        try:
            # Look for grid widgets in children
            for child in widget.findChildren(QWidget):
                if hasattr(child, 'buttons') and hasattr(child, 'grid'):
                    VisualFeedbackIntegrator.enhance_grid_widget(child)
        except Exception as e:
            logger.error(f"Error finding grids: {e}")
    
    def start_operation(self, editor_type: str, operation_id: str, description: str):
        """Start an operation with feedback"""
        if editor_type in self.enhanced_editors:
            editor = self.enhanced_editors[editor_type]
            if hasattr(editor, 'operation_manager'):
                editor.operation_manager.start_operation(operation_id, description)
    
    def update_operation(self, editor_type: str, operation_id: str, progress: int, details: str = ""):
        """Update operation progress"""
        if editor_type in self.enhanced_editors:
            editor = self.enhanced_editors[editor_type]
            if hasattr(editor, 'operation_manager'):
                editor.operation_manager.update_operation(operation_id, progress, details)
    
    def complete_operation(self, editor_type: str, operation_id: str, success: bool, message: str = ""):
        """Complete an operation"""
        if editor_type in self.enhanced_editors:
            editor = self.enhanced_editors[editor_type]
            if hasattr(editor, 'operation_manager'):
                editor.operation_manager.complete_operation(operation_id, success, message)
    
    def update_validation_status(self, editor_type: str, is_valid: bool, message: str = ""):
        """Update validation status in status bar"""
        if editor_type in self.enhanced_editors:
            editor = self.enhanced_editors[editor_type]
            if hasattr(editor, 'enhanced_status_bar'):
                editor.enhanced_status_bar.update_validation_status(is_valid, message)
    
    def update_save_status(self, editor_type: str, is_saved: bool, auto_save: bool = False):
        """Update save status in status bar"""
        if editor_type in self.enhanced_editors:
            editor = self.enhanced_editors[editor_type]
            if hasattr(editor, 'enhanced_status_bar'):
                editor.enhanced_status_bar.update_save_status(is_saved, auto_save)

# Global instance
visual_feedback_manager = VisualFeedbackManager()

def integrate_visual_feedback_into_piece_editor(piece_editor):
    """Integrate visual feedback into piece editor"""
    visual_feedback_manager.enhance_piece_editor(piece_editor)
    return visual_feedback_manager

def integrate_visual_feedback_into_ability_editor(ability_editor):
    """Integrate visual feedback into ability editor"""
    visual_feedback_manager.enhance_ability_editor(ability_editor)
    return visual_feedback_manager

def get_visual_feedback_manager():
    """Get the global visual feedback manager"""
    return visual_feedback_manager
