#!/usr/bin/env python3
"""
Enhanced Pydantic Validation Rules for Adventure Chess Creator

This module provides strengthened validation rules for all Pydantic models
with security considerations and comprehensive data integrity checks.

Key Features:
1. Enhanced field validation with security checks
2. Cross-field validation for data consistency
3. Business logic validation rules
4. Input sanitization at the model level
5. Comprehensive error reporting with actionable messages
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union
from pydantic import field_validator, model_validator

logger = logging.getLogger(__name__)


class ValidationRules:
    """
    Enhanced validation rules for Adventure Chess Creator models
    """
    
    # Security patterns to check for
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',                # JavaScript URLs
        r'vbscript:',                 # VBScript URLs
        r'on\w+\s*=',                 # Event handlers
        r'eval\s*\(',                 # eval() calls
        r'exec\s*\(',                 # exec() calls
        r'__import__',                # Python imports
        r'subprocess',                # Subprocess calls
        r'os\.system',                # OS system calls
    ]
    
    # Maximum lengths for various fields
    MAX_LENGTHS = {
        'name': 100,
        'description': 1000,
        'filename': 255,
        'tag_name': 50,
        'version': 20,
    }
    
    # Valid ranges for numeric fields
    NUMERIC_RANGES = {
        'max_points': (0, 999),
        'starting_points': (0, 999),
        'distance': (1, 8),
        'duration': (1, 100),
        'coordinate': (0, 7),
        'pattern_value': (0, 1),
    }
    
    @classmethod
    def validate_string_field(cls, value: str, field_name: str, 
                             allow_empty: bool = False) -> str:
        """
        Enhanced string validation with security checks
        
        Args:
            value: String value to validate
            field_name: Name of the field being validated
            allow_empty: Whether empty strings are allowed
            
        Returns:
            Validated and sanitized string
            
        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if allow_empty:
                return ""
            else:
                raise ValueError(f"{field_name} cannot be None")
        
        # Convert to string if not already
        if not isinstance(value, str):
            value = str(value)
        
        # Check for empty string
        if not value.strip() and not allow_empty:
            raise ValueError(f"{field_name} cannot be empty")
        
        # Check length
        max_length = cls.MAX_LENGTHS.get(field_name, 500)
        if len(value) > max_length:
            raise ValueError(f"{field_name} exceeds maximum length of {max_length} characters")
        
        # Security validation - check for dangerous patterns
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError(f"{field_name} contains potentially dangerous content")
        
        # Remove control characters
        sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', value)
        
        # Normalize whitespace
        sanitized = ' '.join(sanitized.split())
        
        return sanitized
    
    @classmethod
    def validate_numeric_field(cls, value: Union[int, float], field_name: str) -> Union[int, float]:
        """
        Enhanced numeric validation with range checks
        
        Args:
            value: Numeric value to validate
            field_name: Name of the field being validated
            
        Returns:
            Validated numeric value
            
        Raises:
            ValueError: If validation fails
        """
        if value is None:
            raise ValueError(f"{field_name} cannot be None")
        
        # Convert to appropriate numeric type
        if isinstance(value, str):
            try:
                # Try integer first
                if '.' not in value:
                    value = int(value)
                else:
                    value = float(value)
            except ValueError:
                raise ValueError(f"{field_name} must be a valid number")
        
        if not isinstance(value, (int, float)):
            raise ValueError(f"{field_name} must be a number")
        
        # Check for special float values
        if isinstance(value, float):
            if not (value == value):  # NaN check
                raise ValueError(f"{field_name} cannot be NaN")
            if value == float('inf') or value == float('-inf'):
                raise ValueError(f"{field_name} cannot be infinite")
        
        # Range validation
        if field_name in cls.NUMERIC_RANGES:
            min_val, max_val = cls.NUMERIC_RANGES[field_name]
            if value < min_val or value > max_val:
                raise ValueError(f"{field_name} must be between {min_val} and {max_val}")
        
        return value
    
    @classmethod
    def validate_coordinate(cls, coord: List[int]) -> List[int]:
        """
        Validate coordinate values for 8x8 board
        
        Args:
            coord: Coordinate as [x, y] list
            
        Returns:
            Validated coordinate
            
        Raises:
            ValueError: If validation fails
        """
        if not isinstance(coord, list):
            raise ValueError("Coordinate must be a list")
        
        if len(coord) != 2:
            raise ValueError("Coordinate must have exactly 2 elements [x, y]")
        
        for i, val in enumerate(coord):
            if not isinstance(val, int):
                raise ValueError(f"Coordinate element {i} must be an integer")
            
            if val < 0 or val > 7:
                raise ValueError(f"Coordinate element {i} must be between 0 and 7")
        
        return coord
    
    @classmethod
    def validate_pattern_8x8(cls, pattern: List[List[int]]) -> List[List[int]]:
        """
        Validate 8x8 pattern grid
        
        Args:
            pattern: 8x8 grid pattern
            
        Returns:
            Validated pattern
            
        Raises:
            ValueError: If validation fails
        """
        if not isinstance(pattern, list):
            raise ValueError("Pattern must be a list")
        
        if len(pattern) != 8:
            raise ValueError("Pattern must have exactly 8 rows")
        
        for row_idx, row in enumerate(pattern):
            if not isinstance(row, list):
                raise ValueError(f"Pattern row {row_idx} must be a list")
            
            if len(row) != 8:
                raise ValueError(f"Pattern row {row_idx} must have exactly 8 columns")
            
            for col_idx, val in enumerate(row):
                if not isinstance(val, int):
                    raise ValueError(f"Pattern value at [{row_idx}][{col_idx}] must be an integer")
                
                if val not in [0, 1]:
                    raise ValueError(f"Pattern value at [{row_idx}][{col_idx}] must be 0 or 1")
        
        return pattern
    
    @classmethod
    def validate_enum_field(cls, value: str, valid_values: List[str], field_name: str) -> str:
        """
        Validate enum field values
        
        Args:
            value: Value to validate
            valid_values: List of valid enum values
            field_name: Name of the field being validated
            
        Returns:
            Validated enum value
            
        Raises:
            ValueError: If validation fails
        """
        if value is None:
            raise ValueError(f"{field_name} cannot be None")
        
        if not isinstance(value, str):
            value = str(value)
        
        # Sanitize the value
        value = cls.validate_string_field(value, field_name)
        
        if value not in valid_values:
            raise ValueError(f"{field_name} must be one of: {', '.join(valid_values)}")
        
        return value
    
    @classmethod
    def validate_version_string(cls, version: str) -> str:
        """
        Validate version string format
        
        Args:
            version: Version string to validate
            
        Returns:
            Validated version string
            
        Raises:
            ValueError: If validation fails
        """
        if not version:
            raise ValueError("Version cannot be empty")
        
        # Basic semantic version pattern (major.minor.patch)
        version_pattern = r'^\d+\.\d+\.\d+(?:-[a-zA-Z0-9]+)?$'
        
        if not re.match(version_pattern, version):
            raise ValueError("Version must follow semantic versioning format (e.g., '1.0.0')")
        
        return version
    
    @classmethod
    def validate_ability_tags(cls, tags: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate ability tags structure and content
        
        Args:
            tags: List of ability tag dictionaries
            
        Returns:
            Validated tags list
            
        Raises:
            ValueError: If validation fails
        """
        if not isinstance(tags, list):
            raise ValueError("Ability tags must be a list")
        
        validated_tags = []
        
        for i, tag in enumerate(tags):
            if not isinstance(tag, dict):
                raise ValueError(f"Tag {i} must be a dictionary")
            
            # Validate required fields
            if 'name' not in tag:
                raise ValueError(f"Tag {i} missing required 'name' field")
            
            # Validate tag name
            tag_name = cls.validate_string_field(tag['name'], f"tag_{i}_name")
            
            # Validate tag data if present
            validated_tag = {'name': tag_name}
            
            for key, value in tag.items():
                if key == 'name':
                    continue
                
                # Validate each field in the tag
                if isinstance(value, str):
                    validated_tag[key] = cls.validate_string_field(value, f"tag_{i}_{key}", allow_empty=True)
                elif isinstance(value, (int, float)):
                    validated_tag[key] = cls.validate_numeric_field(value, f"tag_{i}_{key}")
                elif isinstance(value, list):
                    # Handle list fields (coordinates, patterns, etc.)
                    if key.endswith('_pattern') or key == 'pattern':
                        validated_tag[key] = cls.validate_pattern_8x8(value)
                    elif key.endswith('_coordinate') or key in ['coordinate', 'position']:
                        validated_tag[key] = cls.validate_coordinate(value)
                    else:
                        validated_tag[key] = value  # Pass through other lists
                else:
                    validated_tag[key] = value  # Pass through other types
            
            validated_tags.append(validated_tag)
        
        return validated_tags


# Enhanced validation functions for use in Pydantic models
def enhanced_name_validator(value: str) -> str:
    """Enhanced name field validator"""
    return ValidationRules.validate_string_field(value, 'name')

def enhanced_description_validator(value: str) -> str:
    """Enhanced description field validator"""
    return ValidationRules.validate_string_field(value, 'description', allow_empty=True)

def enhanced_version_validator(value: str) -> str:
    """Enhanced version field validator"""
    return ValidationRules.validate_version_string(value)

def enhanced_points_validator(value: int) -> int:
    """Enhanced points field validator"""
    return ValidationRules.validate_numeric_field(value, 'max_points')

def enhanced_coordinate_validator(value: List[int]) -> List[int]:
    """Enhanced coordinate field validator"""
    return ValidationRules.validate_coordinate(value)

def enhanced_pattern_validator(value: List[List[int]]) -> List[List[int]]:
    """Enhanced pattern field validator"""
    return ValidationRules.validate_pattern_8x8(value)

def enhanced_tags_validator(value: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Enhanced ability tags validator"""
    return ValidationRules.validate_ability_tags(value)
