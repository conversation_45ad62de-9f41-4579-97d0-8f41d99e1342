"""
Area Effect tag configuration for ability editor.
Handles area-of-effect ability configurations matching old editor exactly.
"""

from PyQt6.QtWidgets import (
    QSpinBox, QComboBox, QPushButton, QLabel, QWidget,
    QVBoxLayout, QFormLayout, QGridLayout
)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class AreaEffectConfig(BaseTagConfig):
    """Configuration for areaEffect tag abilities matching old editor exactly."""

    def __init__(self, editor):
        super().__init__(editor, "areaEffect")
        # Initialize area effect data structures
        self.area_effect_target = [4, 4]  # Where ability targets
        self.area_effect_center = [4, 4]  # Center of effect area
        self.custom_area_pattern = [[False for _ in range(8)] for _ in range(8)]  # 8x8 boolean grid
        self.area_effect_grid = []

    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for area effect configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting area effect UI creation")

            # Main layout
            layout = QVBoxLayout()

            # Controls
            controls_layout = QFormLayout()

            # Effect size spinner (1-8 to match old editor exactly)
            area_size = QSpinBox()
            area_size.setRange(1, 8)
            area_size.setValue(1)
            area_size.setToolTip("Size of the area effect (1=1x1, 2=2x2, etc.)")
            area_size.valueChanged.connect(self.update_area_effect_preview)
            self.store_widget("area_size", area_size)
            self.connect_change_signals(area_size)
            controls_layout.addRow("Effect Size:", area_size)
            self.log_debug("Added area size spinner")

            # Effect shape combo (exact items from old editor)
            area_shape = QComboBox()
            area_shape.addItems(["Circle", "Square", "Cross", "Line", "Custom"])
            area_shape.setToolTip("Shape of the area effect")
            area_shape.currentTextChanged.connect(self.update_area_effect_preview)
            area_shape.currentTextChanged.connect(self.on_area_shape_changed)
            self.store_widget("area_shape", area_shape)
            self.connect_change_signals(area_shape)
            controls_layout.addRow("Effect Shape:", area_shape)
            self.log_debug("Added area shape dropdown")

            layout.addLayout(controls_layout)

            # Instructions for interaction
            instructions = QLabel("Right-click: Move target (🎯) | Shift+Right-click: Move effect center")
            instructions.setWordWrap(True)
            instructions.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
            layout.addWidget(instructions)

            # Visual preview
            preview_label = QLabel("Preview:")
            layout.addWidget(preview_label)

            # Create preview widget
            area_effect_preview = QWidget()
            area_effect_preview.setFixedSize(200, 200)
            area_effect_preview.setStyleSheet("border: 1px solid #4a5568; background: #2d3748;")
            self.store_widget("area_effect_preview", area_effect_preview)
            layout.addWidget(area_effect_preview)

            # Create mini grid for preview
            self.area_effect_grid = []
            grid_layout = QGridLayout(area_effect_preview)
            grid_layout.setSpacing(1)

            for r in range(8):
                row = []
                for c in range(8):
                    btn = QPushButton()
                    btn.setFixedSize(22, 22)
                    btn.setEnabled(True)
                    btn.setCheckable(True)  # Make buttons checkable for selection
                    # Left click to toggle effect squares, right click to set target
                    btn.clicked.connect(lambda checked, row=r, col=c: self.handle_effect_square_toggle(row, col, checked))
                    btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                    btn.customContextMenuRequested.connect(
                        lambda pos, row=r, col=c: self.handle_target_selection(row, col)
                    )
                    grid_layout.addWidget(btn, r, c)
                    row.append(btn)
                self.area_effect_grid.append(row)

            # Add to parent layout
            parent_layout.addLayout(layout)

            # Initialize preview
            self.update_area_effect_preview()

            self.log_debug("Area effect UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def on_area_shape_changed(self, shape):
        """Handle area shape change - enable/disable size spinner for custom shapes."""
        try:
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                area_size.setEnabled(shape != "Custom")

            self.log_debug(f"Area shape changed to: {shape}")
        except Exception as e:
            self.log_error(f"Error handling shape change: {e}")

    def handle_effect_square_toggle(self, row, col, checked):
        """Handle left-click to toggle effect squares."""
        try:
            # Update the custom pattern
            self.custom_area_pattern[row][col] = checked

            # Auto-change to custom shape when squares are manually selected
            area_shape = self.get_widget_by_name("area_shape")
            if area_shape and area_shape.currentText() != "Custom":
                area_shape.setCurrentText("Custom")

            self.update_area_effect_preview()

            # Mark as changed
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()

            self.log_debug(f"Effect square toggled at: {row}, {col} = {checked}")

        except Exception as e:
            self.log_error(f"Error handling effect square toggle: {e}")

    def handle_target_selection(self, row, col):
        """Handle right-click to set target square or effect center."""
        try:
            from PyQt6.QtWidgets import QApplication
            modifiers = QApplication.keyboardModifiers()

            if modifiers & Qt.KeyboardModifier.ShiftModifier:
                # Shift+Right-click: Move effect center
                self.area_effect_center = [row, col]
                self.log_debug(f"Effect center moved to: {row}, {col}")
            else:
                # Right-click: Move target
                self.area_effect_target = [row, col]
                self.log_debug(f"Target moved to: {row}, {col}")

            self.update_area_effect_preview()

            # Mark as changed
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()

        except Exception as e:
            self.log_error(f"Error handling target selection: {e}")



    def update_area_effect_preview(self):
        """Update the area effect visual preview matching old editor exactly."""
        try:
            if not self.area_effect_grid:
                return

            area_size = self.get_widget_by_name("area_size")
            area_shape = self.get_widget_by_name("area_shape")

            if not area_size or not area_shape:
                return

            size = area_size.value()  # 1=1x1, 2=2x2, etc.
            shape = area_shape.currentText()

            target_r, target_c = self.area_effect_target
            center_r, center_c = self.area_effect_center

            # Clear all squares first and sync with custom pattern
            for r in range(8):
                for c in range(8):
                    btn = self.area_effect_grid[r][c]
                    is_light = (r + c) % 2 == 0

                    # Set base chess board styling
                    if is_light:
                        btn.setStyleSheet("background: #f0d9b5; border: 1px solid #b58863;")
                    else:
                        btn.setStyleSheet("background: #b58863; border: 1px solid #8b4513;")
                    btn.setText("")

                    # Sync button checked state with custom pattern for Custom mode
                    if shape == "Custom":
                        btn.setChecked(self.custom_area_pattern[r][c])

            # Handle custom pattern
            if shape == "Custom":
                for r in range(8):
                    for c in range(8):
                        if self.custom_area_pattern[r][c]:
                            btn = self.area_effect_grid[r][c]
                            if r == target_r and c == target_c:
                                # Target square in effect area - special color
                                btn.setStyleSheet("background: #ff9900; border: 2px solid #cc6600; font-weight: bold; color: white;")
                                btn.setText("🎯")
                            else:
                                btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                                btn.setText("💥")

                # Add target marker if not in effect area
                if not (hasattr(self, 'custom_area_pattern') and self.custom_area_pattern and
                       target_r < len(self.custom_area_pattern) and
                       target_c < len(self.custom_area_pattern[target_r]) and
                       self.custom_area_pattern[target_r][target_c]):
                    target_btn = self.area_effect_grid[target_r][target_c]
                    target_btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
                    target_btn.setText("🎯")
                return

            # Calculate affected squares based on size and shape (centered on effect center)
            affected_squares = set()

            if shape == "Square":
                # Square pattern: size x size area centered on effect center
                half_size = size // 2
                for r in range(max(0, center_r - half_size), min(8, center_r + size - half_size)):
                    for c in range(max(0, center_c - half_size), min(8, center_c + size - half_size)):
                        affected_squares.add((r, c))

            elif shape == "Circle":
                # Circular pattern centered on effect center
                for r in range(8):
                    for c in range(8):
                        distance = max(abs(r - center_r), abs(c - center_c))
                        if distance <= size:
                            affected_squares.add((r, c))

            elif shape == "Cross":
                # Cross pattern
                for i in range(size + 1):
                    # Horizontal line
                    if 0 <= center_c - i < 8:
                        affected_squares.add((center_r, center_c - i))
                    if 0 <= center_c + i < 8:
                        affected_squares.add((center_r, center_c + i))
                    # Vertical line
                    if 0 <= center_r - i < 8:
                        affected_squares.add((center_r - i, center_c))
                    if 0 <= center_r + i < 8:
                        affected_squares.add((center_r + i, center_c))

            elif shape == "Line":
                # Line pattern (horizontal for now)
                for i in range(size + 1):
                    if 0 <= center_c - i < 8:
                        affected_squares.add((center_r, center_c - i))
                    if 0 <= center_c + i < 8:
                        affected_squares.add((center_r, center_c + i))

            # Apply the effect visualization
            for r, c in affected_squares:
                if 0 <= r < 8 and 0 <= c < 8:
                    btn = self.area_effect_grid[r][c]
                    if r == target_r and c == target_c and r == center_r and c == center_c:
                        # Target and effect center are same - combined symbol
                        btn.setStyleSheet("background: #ff9900; border: 2px solid #cc6600; font-weight: bold; color: white;")
                        btn.setText("🎯")
                    elif r == target_r and c == target_c:
                        # Target square in effect area
                        btn.setStyleSheet("background: #ff6600; border: 2px solid #cc3300; font-weight: bold; color: white;")
                        btn.setText("🎯")
                    elif r == center_r and c == center_c:
                        # Effect center
                        btn.setStyleSheet("background: #cc9900; border: 2px solid #996600; font-weight: bold; color: white;")
                        btn.setText("⊕")
                    else:
                        # Regular effect square
                        btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                        btn.setText("💥")

            # Add target marker if not in effect area
            if (target_r, target_c) not in affected_squares:
                target_btn = self.area_effect_grid[target_r][target_c]
                target_btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; color: white;")
                target_btn.setText("🎯")

            # Add effect center marker if not same as target and not in effect area
            if ((center_r, center_c) not in affected_squares and
                (center_r != target_r or center_c != target_c)):
                center_btn = self.area_effect_grid[center_r][center_c]
                center_btn.setStyleSheet("background: #9966cc; border: 2px solid #663399; font-weight: bold; color: white;")
                center_btn.setText("⊕")

        except Exception as e:
            self.log_error(f"Error updating area effect preview: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting area effect data population with: {data}")

            # Populate area size
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                size_value = data.get("areaEffectSize", 1)
                self.log_debug(f"Setting area size to: {size_value}")
                area_size.setValue(size_value)

            # Populate area shape
            area_shape = self.get_widget_by_name("area_shape")
            if area_shape:
                shape_value = data.get("areaEffectShape", "Circle")
                self.log_debug(f"Setting area shape to: {shape_value}")
                index = area_shape.findText(shape_value)
                if index >= 0:
                    area_shape.setCurrentIndex(index)

            # Load position data
            if 'areaEffectTarget' in data:
                self.area_effect_target = data['areaEffectTarget'][:]  # Copy
            else:
                self.area_effect_target = [4, 4]  # Default

            if 'areaEffectCenter' in data:
                self.area_effect_center = data['areaEffectCenter'][:]  # Copy
            else:
                self.area_effect_center = [4, 4]  # Default

            if 'customAreaPattern' in data:
                self.custom_area_pattern = data['customAreaPattern']
            else:
                self.custom_area_pattern = None

            # Update preview after loading data
            self.update_area_effect_preview()

            self.log_debug("Area effect data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the area effect configuration data
        """
        try:
            data = {}

            # Collect area size
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                data["areaEffectSize"] = area_size.value()

            # Collect area shape
            area_shape = self.get_widget_by_name("area_shape")
            if area_shape:
                data["areaEffectShape"] = area_shape.currentText()

            # Collect position data
            data["areaEffectTarget"] = self.area_effect_target[:]  # Copy
            data["areaEffectCenter"] = self.area_effect_center[:]  # Copy

            # Collect custom pattern if exists
            if self.custom_area_pattern:
                data["customAreaPattern"] = self.custom_area_pattern

            self.log_debug(f"Collected area effect data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
