"""
Core module for Adventure Chess Creator

This module contains the foundational classes and interfaces that form
the backbone of the Adventure Chess Creator application.

Submodules:
- base_classes: Abstract base classes for widgets, managers, handlers, and editors
- handlers: Specialized data handlers for pieces and abilities
- managers: Manager classes for various application components
- interfaces: Interface definitions for data handling and UI interaction
"""

# Import key base classes for easy access
from .base_classes.base_widgets import BaseWidget, BaseFormWidget, BaseTabWidget, BaseDataWidget
from .base_classes.base_manager import <PERSON><PERSON>ana<PERSON>, BaseUIManager, BaseDataManager, BaseConfigurationManager
from .base_classes.base_data_handler import BaseDataHandler
from .base_classes.base_editor import BaseEditor

# Import specialized implementations
from .handlers.specialized_data_handlers import <PERSON>hanced<PERSON><PERSON>ceData<PERSON>and<PERSON>, EnhancedAbilityDataHandler
from .interfaces.editor_data_interface import EditorDataInterface

__all__ = [
    # Base classes
    'BaseWidget', 'BaseFormWidget', 'BaseTabWidget', 'BaseDataWidget',
    'BaseManager', 'BaseUIManager', 'BaseDataManager', 'BaseConfigurationManager',
    'BaseDataHandler', 'BaseEditor',
    
    # Specialized implementations
    'EnhancedPieceDataHandler', 'EnhancedAbilityDataHandler',
    'EditorDataInterface'
]
