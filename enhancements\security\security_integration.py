#!/usr/bin/env python3
"""
Security Integration Module for Adventure Chess Creator

This module integrates security enhancements throughout the application,
providing secure wrappers for existing data managers and UI components.

Key Features:
1. Secure wrappers for DirectDataManager and PydanticDataManager
2. Enhanced error handling with security considerations
3. Automatic backup and recovery integration
4. Input validation at all entry points
5. Secure file operations with proper validation
"""

import logging
from typing import Dict, Any, Optional, Tuple, List, Union
from pathlib import Path

from security_enhancements import (
    SecurityValidator, 
    CrashRecoveryManager, 
    SecureDataManager,
    security_validator,
    crash_recovery_manager,
    secure_data_manager
)
from enhanced_validation_rules import ValidationRules

logger = logging.getLogger(__name__)


class SecureDirectDataManager:
    """
    Security-enhanced wrapper for DirectDataManager
    """
    
    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save piece data with validation and backup
        
        Args:
            piece_data: Piece data dictionary
            filename: Optional filename
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Import here to avoid circular imports
            from utils.direct_data_manager import DirectDataManager
            from config import PIECES_DIR
            
            # Validate input data
            is_valid, error = security_validator.validate_user_input(piece_data, "piece_data")
            if not is_valid:
                return False, f"Input validation failed: {error}"
            
            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)
            
            # Create recovery point before saving
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                piece_data, "piece"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")
            
            # Use secure data manager for the actual save
            if not filename:
                filename = piece_data.get('name', 'unnamed_piece')
            
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(PIECES_DIR) / filename
            
            return secure_data_manager.secure_save_file(piece_data, file_path)
            
        except Exception as e:
            error_msg = f"Secure piece save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Securely load piece data with validation
        
        Args:
            filename: Filename to load
            
        Returns:
            Tuple of (piece_data, error_message)
        """
        try:
            from config import PIECES_DIR
            
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(PIECES_DIR) / filename
            
            # Use secure data manager for loading
            data, error = secure_data_manager.secure_load_file(file_path)
            
            if data is not None:
                # Additional validation for piece-specific fields
                try:
                    ValidationRules.validate_string_field(data.get('name', ''), 'name')
                    if 'max_points' in data:
                        ValidationRules.validate_numeric_field(data['max_points'], 'max_points')
                    if 'starting_points' in data:
                        ValidationRules.validate_numeric_field(data['starting_points'], 'starting_points')
                except ValueError as ve:
                    return None, f"Piece data validation failed: {str(ve)}"
            
            return data, error
            
        except Exception as e:
            error_msg = f"Secure piece load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save ability data with validation and backup
        
        Args:
            ability_data: Ability data dictionary
            filename: Optional filename
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            from config import ABILITIES_DIR
            
            # Validate input data
            is_valid, error = security_validator.validate_user_input(ability_data, "ability_data")
            if not is_valid:
                return False, f"Input validation failed: {error}"
            
            # Validate ability tags if present
            if 'tags' in ability_data:
                try:
                    ValidationRules.validate_ability_tags(ability_data['tags'])
                except ValueError as ve:
                    return False, f"Ability tags validation failed: {str(ve)}"
            
            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)
            
            # Create recovery point before saving
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                ability_data, "ability"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")
            
            # Use secure data manager for the actual save
            if not filename:
                filename = ability_data.get('name', 'unnamed_ability')
            
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(ABILITIES_DIR) / filename
            
            return secure_data_manager.secure_save_file(ability_data, file_path)
            
        except Exception as e:
            error_msg = f"Secure ability save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Securely load ability data with validation
        
        Args:
            filename: Filename to load
            
        Returns:
            Tuple of (ability_data, error_message)
        """
        try:
            from config import ABILITIES_DIR
            
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(ABILITIES_DIR) / filename
            
            # Use secure data manager for loading
            data, error = secure_data_manager.secure_load_file(file_path)
            
            if data is not None:
                # Additional validation for ability-specific fields
                try:
                    ValidationRules.validate_string_field(data.get('name', ''), 'name')
                    if 'tags' in data:
                        ValidationRules.validate_ability_tags(data['tags'])
                except ValueError as ve:
                    return None, f"Ability data validation failed: {str(ve)}"
            
            return data, error
            
        except Exception as e:
            error_msg = f"Secure ability load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


class SecurePydanticDataManager:
    """
    Security-enhanced wrapper for PydanticDataManager
    """
    
    def __init__(self):
        from schemas.data_manager import PydanticDataManager
        self.pydantic_manager = PydanticDataManager()
    
    def save_piece(self, piece, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save piece with enhanced validation
        
        Args:
            piece: Pydantic Piece model instance
            filename: Optional filename
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Validate piece data
            piece_dict = piece.model_dump()
            is_valid, error = security_validator.validate_user_input(piece_dict, "piece")
            if not is_valid:
                return False, f"Piece validation failed: {error}"
            
            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)
            
            # Create recovery point
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                piece_dict, "piece_pydantic"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")
            
            # Use original manager with additional security
            return self.pydantic_manager.save_piece(piece, filename)
            
        except Exception as e:
            error_msg = f"Secure Pydantic piece save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def load_piece(self, filename: str):
        """
        Securely load piece with enhanced validation
        
        Args:
            filename: Filename to load
            
        Returns:
            Tuple of (piece_model, error_message)
        """
        try:
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            
            # Use original manager
            return self.pydantic_manager.load_piece(filename)
            
        except Exception as e:
            error_msg = f"Secure Pydantic piece load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    def save_ability(self, ability, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Securely save ability with enhanced validation
        
        Args:
            ability: Pydantic Ability model instance
            filename: Optional filename
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Validate ability data
            ability_dict = ability.model_dump()
            is_valid, error = security_validator.validate_user_input(ability_dict, "ability")
            if not is_valid:
                return False, f"Ability validation failed: {error}"
            
            # Sanitize filename if provided
            if filename:
                filename = security_validator.sanitize_filename(filename)
            
            # Create recovery point
            recovery_success, recovery_error = crash_recovery_manager.create_recovery_point(
                ability_dict, "ability_pydantic"
            )
            if not recovery_success:
                logger.warning(f"Recovery point creation failed: {recovery_error}")
            
            # Use original manager with additional security
            return self.pydantic_manager.save_ability(ability, filename)
            
        except Exception as e:
            error_msg = f"Secure Pydantic ability save failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def load_ability(self, filename: str):
        """
        Securely load ability with enhanced validation
        
        Args:
            filename: Filename to load
            
        Returns:
            Tuple of (ability_model, error_message)
        """
        try:
            # Sanitize filename
            filename = security_validator.sanitize_filename(filename)
            
            # Use original manager
            return self.pydantic_manager.load_ability(filename)
            
        except Exception as e:
            error_msg = f"Secure Pydantic ability load failed: {str(e)}"
            logger.error(error_msg)
            return None, error_msg


# Global secure manager instances
secure_direct_data_manager = SecureDirectDataManager()
secure_pydantic_data_manager = SecurePydanticDataManager()


def get_recovery_options(data_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get available recovery options for the user
    
    Args:
        data_type: Optional filter by data type
        
    Returns:
        List of recovery options with metadata
    """
    try:
        recovery_files = crash_recovery_manager.find_recovery_files(data_type)
        
        options = []
        for recovery_file in recovery_files:
            try:
                # Get file metadata
                stat = recovery_file.stat()
                
                # Parse timestamp from filename
                parts = recovery_file.stem.split('_')
                if len(parts) >= 3:
                    file_data_type = parts[0]
                    timestamp = parts[-1]
                else:
                    file_data_type = "unknown"
                    timestamp = "unknown"
                
                options.append({
                    'file_path': str(recovery_file),
                    'data_type': file_data_type,
                    'timestamp': timestamp,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })
                
            except Exception as e:
                logger.warning(f"Error processing recovery file {recovery_file}: {e}")
        
        # Sort by modification time (newest first)
        options.sort(key=lambda x: x['modified'], reverse=True)
        
        return options
        
    except Exception as e:
        logger.error(f"Error getting recovery options: {e}")
        return []
