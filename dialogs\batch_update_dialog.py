"""
Batch Update Dialog for Adventure Chess
Provides UI for updating all files to latest structure
"""

import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QProgressBar, QCheckBox, QGroupBox, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from utils.batch_update import batch_update_manager


class BatchUpdateWorker(QThread):
    """Worker thread for batch update operations"""
    
    progress_updated = pyqtSignal(str)  # Progress message
    finished_signal = pyqtSignal(dict)  # Results dictionary
    
    def __init__(self, remove_outdated_tags=False):
        super().__init__()
        self.remove_outdated_tags = remove_outdated_tags
    
    def run(self):
        """Run the batch update process"""
        try:
            self.progress_updated.emit("Starting batch update...")
            results = batch_update_manager.update_all_files(self.remove_outdated_tags)
            self.finished_signal.emit(results)
        except Exception as e:
            self.progress_updated.emit(f"Error: {str(e)}")
            self.finished_signal.emit({'error': str(e)})


class BatchUpdateDialog(QDialog):
    """Dialog for batch updating all Adventure Chess files"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Batch Update All Files - Adventure Chess")
        self.setMinimumSize(600, 500)
        self.worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the dialog UI"""
        # Set dark theme for the dialog
        self.setStyleSheet("""
            QDialog {
                background-color: #1a202c;
                color: #e2e8f0;
            }
            QLabel {
                color: #e2e8f0;
            }
            QGroupBox {
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QCheckBox {
                color: #e2e8f0;
            }
            QPushButton {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #4a5568;
                border-color: #718096;
            }
            QPushButton:pressed {
                background-color: #1a202c;
            }
        """)

        layout = QVBoxLayout()
        
        # Title and description
        title = QLabel("🔄 Batch Update All Files")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)
        
        description = QLabel(
            "Update all ability and piece files to the latest structure while preserving data.\n"
            "Outdated tags will be flagged as errors and moved to the bottom of files."
        )
        description.setWordWrap(True)
        description.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(description)
        
        # Options group
        options_group = QGroupBox("Update Options")
        options_layout = QVBoxLayout()
        
        self.remove_outdated_check = QCheckBox("Remove outdated tags (second save)")
        self.remove_outdated_check.setToolTip(
            "If checked, outdated tags will be removed. Otherwise, they'll be flagged as errors."
        )
        options_layout.addWidget(self.remove_outdated_check)
        
        info_label = QLabel(
            "💡 First run: Outdated tags are flagged as errors\n"
            "💡 Second run: Check the option above to remove flagged tags"
        )
        info_label.setStyleSheet("""
            QLabel {
                background-color: #2d3748;
                color: #e2e8f0;
                padding: 10px;
                border: 1px solid #4a5568;
                border-radius: 5px;
                margin: 5px 0;
            }
        """)
        options_layout.addWidget(info_label)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Progress area
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout()
        
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(200)
        self.progress_text.setReadOnly(True)
        self.progress_text.setStyleSheet("""
            QTextEdit {
                font-family: monospace;
                background-color: #1a202c;
                color: #e2e8f0;
                border: 1px solid #4a5568;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        progress_layout.addWidget(self.progress_text)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.start_btn = QPushButton("🚀 Start Update")
        self.start_btn.clicked.connect(self.start_update)
        self.start_btn.setStyleSheet("""
            QPushButton {
                font-weight: bold;
                padding: 10px 20px;
                background-color: #38a169;
                color: white;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2f855a;
            }
            QPushButton:disabled {
                background-color: #4a5568;
                color: #a0aec0;
            }
        """)
        button_layout.addWidget(self.start_btn)
        
        self.close_btn = QPushButton("Close")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def start_update(self):
        """Start the batch update process"""
        # Confirm with user
        reply = QMessageBox.question(
            self, "Confirm Batch Update",
            "This will update all ability and piece files. Make sure you have backups.\n\n"
            "Do you want to continue?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # Disable start button and show progress
        self.start_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.progress_text.clear()
        
        # Start worker thread
        remove_outdated = self.remove_outdated_check.isChecked()
        self.worker = BatchUpdateWorker(remove_outdated)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.finished_signal.connect(self.update_finished)
        self.worker.start()
    
    def update_progress(self, message):
        """Update progress display"""
        self.progress_text.append(message)
        self.progress_text.ensureCursorVisible()
    
    def update_finished(self, results):
        """Handle update completion"""
        self.progress_bar.setVisible(False)
        self.start_btn.setEnabled(True)
        
        if 'error' in results:
            self.progress_text.append(f"\n❌ Update failed: {results['error']}")
            QMessageBox.critical(self, "Update Failed", f"Batch update failed:\n{results['error']}")
            return
        
        # Display results
        self.progress_text.append("\n" + "="*50)
        self.progress_text.append("📊 BATCH UPDATE RESULTS")
        self.progress_text.append("="*50)
        
        self.progress_text.append(f"✅ Abilities updated: {results['abilities_updated']}")
        self.progress_text.append(f"✅ Pieces updated: {results['pieces_updated']}")
        
        if results['outdated_tags_found']:
            self.progress_text.append(f"\n⚠️ Outdated tags found: {len(results['outdated_tags_found'])}")
            for tag_info in results['outdated_tags_found']:
                self.progress_text.append(f"   - {tag_info}")
            self.progress_text.append("\n💡 Run again with 'Remove outdated tags' checked to clean them up")
        
        if results['outdated_tags_removed']:
            self.progress_text.append(f"\n🧹 Outdated tags removed: {len(results['outdated_tags_removed'])}")
            for tag_info in results['outdated_tags_removed']:
                self.progress_text.append(f"   - {tag_info}")
        
        if results['abilities_errors'] or results['pieces_errors']:
            self.progress_text.append(f"\n❌ Errors encountered:")
            for error in results['abilities_errors'] + results['pieces_errors']:
                self.progress_text.append(f"   - {error}")
        
        self.progress_text.append("\n✅ Batch update complete!")
        
        # Show summary dialog
        total_updated = results['abilities_updated'] + results['pieces_updated']
        total_errors = len(results['abilities_errors']) + len(results['pieces_errors'])
        
        if total_errors == 0:
            QMessageBox.information(
                self, "Update Complete",
                f"Successfully updated {total_updated} files!\n\n"
                f"Abilities: {results['abilities_updated']}\n"
                f"Pieces: {results['pieces_updated']}"
            )
        else:
            QMessageBox.warning(
                self, "Update Complete with Errors",
                f"Updated {total_updated} files with {total_errors} errors.\n"
                f"Check the progress log for details."
            )
