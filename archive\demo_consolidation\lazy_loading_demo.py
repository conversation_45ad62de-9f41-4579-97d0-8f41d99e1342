#!/usr/bin/env python3
"""
Lazy Loading System Demo for Adventure Chess Creator
Demonstrates how to integrate and use the lazy loading system
"""

import os
import sys
import logging
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTabWidget, QTextEdit, QGroupBox
)
from PyQt6.QtCore import Qt

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from lazy_ui_components import LazyComboBox, LazyListWidget, LazyLoadingPanel, LazyFileSelector
    from lazy_data_integration import get_lazy_data_manager
    from lazy_editor_integration import apply_all_lazy_patches
    from enhanced_cache_manager import get_cache_manager
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all lazy loading files are in the same directory")
    sys.exit(1)

class LazyLoadingDemoWindow(QMainWindow):
    """
    Demo window showing lazy loading capabilities
    """
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Adventure Chess Creator - Lazy Loading Demo")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialize data manager
        self.data_manager = get_lazy_data_manager()
        
        self.setup_ui()
        self.setup_connections()
        
        # Start demo
        self.start_demo()
    
    def setup_ui(self):
        """Setup the demo UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("🚀 Lazy Loading System Demo")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                text-align: center;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Tab widget for different demos
        self.tab_widget = QTabWidget()
        
        # Tab 1: File Selectors
        self.create_file_selector_tab()
        
        # Tab 2: Performance Comparison
        self.create_performance_tab()
        
        # Tab 3: Loading Status
        self.create_status_tab()
        
        layout.addWidget(self.tab_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.demo_btn = QPushButton("🎬 Run Demo")
        self.demo_btn.clicked.connect(self.run_demo)
        self.demo_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.clear_btn = QPushButton("🗑️ Clear Cache")
        self.clear_btn.clicked.connect(self.clear_cache)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        button_layout.addWidget(self.demo_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        central_widget.setLayout(layout)
    
    def create_file_selector_tab(self):
        """Create file selector demo tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # Description
        desc_label = QLabel("This tab demonstrates lazy file selectors that load file metadata quickly and full content on-demand.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("QLabel { padding: 10px; background-color: #ecf0f1; border-radius: 5px; }")
        layout.addWidget(desc_label)
        
        # File selectors
        selectors_layout = QHBoxLayout()
        
        # Check if directories exist
        try:
            from config import PIECES_DIR, ABILITIES_DIR
            
            # Pieces selector
            pieces_group = QGroupBox("Pieces (Lazy Loading)")
            pieces_layout = QVBoxLayout()
            
            if os.path.exists(PIECES_DIR):
                self.pieces_selector = LazyFileSelector(PIECES_DIR, "Select Piece")
                self.pieces_selector.file_selected.connect(self.on_piece_selected)
                pieces_layout.addWidget(self.pieces_selector)
            else:
                pieces_layout.addWidget(QLabel(f"Pieces directory not found: {PIECES_DIR}"))
            
            pieces_group.setLayout(pieces_layout)
            selectors_layout.addWidget(pieces_group)
            
            # Abilities selector
            abilities_group = QGroupBox("Abilities (Lazy Loading)")
            abilities_layout = QVBoxLayout()
            
            if os.path.exists(ABILITIES_DIR):
                self.abilities_selector = LazyFileSelector(ABILITIES_DIR, "Select Ability")
                self.abilities_selector.file_selected.connect(self.on_ability_selected)
                abilities_layout.addWidget(self.abilities_selector)
            else:
                abilities_layout.addWidget(QLabel(f"Abilities directory not found: {ABILITIES_DIR}"))
            
            abilities_group.setLayout(abilities_layout)
            selectors_layout.addWidget(abilities_group)
            
        except ImportError:
            selectors_layout.addWidget(QLabel("Config not available - using demo directories"))
            
            # Demo selectors
            demo_group = QGroupBox("Demo Files")
            demo_layout = QVBoxLayout()
            demo_layout.addWidget(QLabel("Create some .json files in the project directory to test"))
            demo_group.setLayout(demo_layout)
            selectors_layout.addWidget(demo_group)
        
        layout.addLayout(selectors_layout)
        
        # Output area
        self.file_output = QTextEdit()
        self.file_output.setMaximumHeight(200)
        self.file_output.setPlaceholderText("Selected file data will appear here...")
        self.file_output.setStyleSheet("""
            QTextEdit {
                font-family: monospace;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        layout.addWidget(QLabel("Selected File Data:"))
        layout.addWidget(self.file_output)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "📁 File Selectors")
    
    def create_performance_tab(self):
        """Create performance comparison tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # Description
        desc_label = QLabel("This tab shows performance metrics and comparisons between eager and lazy loading.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("QLabel { padding: 10px; background-color: #ecf0f1; border-radius: 5px; }")
        layout.addWidget(desc_label)
        
        # Performance metrics
        self.performance_output = QTextEdit()
        self.performance_output.setPlaceholderText("Performance metrics will appear here...")
        self.performance_output.setStyleSheet("""
            QTextEdit {
                font-family: monospace;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.performance_output)
        
        # Performance test button
        perf_btn = QPushButton("🏃 Run Performance Test")
        perf_btn.clicked.connect(self.run_performance_test)
        perf_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        layout.addWidget(perf_btn)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "⚡ Performance")
    
    def create_status_tab(self):
        """Create loading status tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # Description
        desc_label = QLabel("This tab shows real-time loading status and cache statistics.")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("QLabel { padding: 10px; background-color: #ecf0f1; border-radius: 5px; }")
        layout.addWidget(desc_label)
        
        # Lazy loading panel
        self.lazy_panel = LazyLoadingPanel()
        layout.addWidget(self.lazy_panel)
        
        # Status output
        self.status_output = QTextEdit()
        self.status_output.setMaximumHeight(200)
        self.status_output.setPlaceholderText("Loading status will appear here...")
        self.status_output.setStyleSheet("""
            QTextEdit {
                font-family: monospace;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        layout.addWidget(QLabel("Detailed Status:"))
        layout.addWidget(self.status_output)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "📊 Status")
    
    def setup_connections(self):
        """Setup signal connections"""
        # Add progress callback
        self.data_manager.add_progress_callback(self.on_progress_update)
    
    def start_demo(self):
        """Start the demo"""
        self.log_message("🚀 Lazy Loading Demo Started")
        self.log_message("✅ Data manager initialized")
        self.log_message("✅ UI components ready")
        self.log_message("📝 Select files to see lazy loading in action")
    
    def run_demo(self):
        """Run the demo sequence"""
        self.log_message("🎬 Running demo sequence...")
        
        # Start preloading
        self.data_manager.preload_recent_files(max_files=10)
        self.log_message("🔄 Started preloading recent files")
        
        # Show status
        status = self.data_manager.get_loading_status()
        self.log_message(f"📊 Loading status: {status}")
    
    def run_performance_test(self):
        """Run performance test"""
        self.performance_output.clear()
        self.performance_output.append("🏃 Running performance test...")
        
        import time
        import tempfile
        import json
        from pathlib import Path
        
        # Create test files
        temp_dir = tempfile.mkdtemp()
        num_files = 20
        
        try:
            self.performance_output.append(f"📁 Creating {num_files} test files...")
            
            for i in range(num_files):
                file_path = Path(temp_dir) / f"test_file_{i}.json"
                test_data = {
                    "name": f"Test Item {i}",
                    "description": f"Test description {i}" * 5,
                    "data": list(range(50))
                }
                
                with open(file_path, 'w') as f:
                    json.dump(test_data, f)
            
            # Test lazy loading
            start_time = time.time()
            metadata = self.data_manager.lazy_manager.load_file_metadata_lazy(temp_dir)
            lazy_time = time.time() - start_time
            
            self.performance_output.append(f"⚡ Lazy loading: {lazy_time:.3f} seconds")
            self.performance_output.append(f"📊 Files processed: {len(metadata)}")
            self.performance_output.append(f"🎯 Average per file: {(lazy_time/len(metadata)*1000):.1f}ms")
            
            # Memory usage
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.performance_output.append(f"💾 Memory usage: {memory_mb:.1f} MB")
            
        finally:
            # Clean up
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def on_piece_selected(self, filename: str, data: dict):
        """Handle piece selection"""
        self.file_output.clear()
        self.file_output.append(f"🎯 Piece Selected: {filename}")
        self.file_output.append(f"📝 Data: {json.dumps(data, indent=2)}")
        self.log_message(f"🎯 Loaded piece: {filename}")
    
    def on_ability_selected(self, filename: str, data: dict):
        """Handle ability selection"""
        self.file_output.clear()
        self.file_output.append(f"⚡ Ability Selected: {filename}")
        self.file_output.append(f"📝 Data: {json.dumps(data, indent=2)}")
        self.log_message(f"⚡ Loaded ability: {filename}")
    
    def on_progress_update(self, progress: int, message: str):
        """Handle progress updates"""
        self.log_message(f"📈 Progress: {progress}% - {message}")
    
    def clear_cache(self):
        """Clear the cache"""
        cache_manager = get_cache_manager()
        cache_manager.clear_all()
        self.log_message("🗑️ Cache cleared")
    
    def log_message(self, message: str):
        """Log message to status output"""
        if hasattr(self, 'status_output'):
            self.status_output.append(f"{message}")
        print(message)
    
    def closeEvent(self, event):
        """Handle window close"""
        self.data_manager.shutdown()
        event.accept()

def main():
    """Main demo function"""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show demo window
    demo_window = LazyLoadingDemoWindow()
    demo_window.show()
    
    # Run application
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
