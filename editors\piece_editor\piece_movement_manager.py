"""
Piece Movement Manager for Adventure Chess Creator

This module handles all movement pattern functionality for the piece editor:
- Movement pattern generation for standard types
- Custom pattern editor integration
- Movement type selection and validation
- Pattern preview and visualization
- Movement controls and UI updates

Extracted from piece_editor.py to improve maintainability and make
movement pattern management more modular and easier to test.
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from PyQt6.QtWidgets import QPushButton, QWidget, QGridLayout
from PyQt6.QtCore import Qt

# Import pattern editor dialog
from dialogs.pattern_editor_dialog import PatternEditorDialog

logger = logging.getLogger(__name__)


class PieceMovementManager:
    """
    Handles all movement pattern functionality for the piece editor.
    
    This class manages:
    - Movement pattern generation for standard types
    - Custom pattern editor integration
    - Movement type selection and validation
    - Pattern preview and visualization
    - Movement controls and UI updates
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the movement manager.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
        
    def generate_standard_pattern(self, movement_type: str, piece_pos: List[int]) -> List[List[int]]:
        """
        Generate the standard pattern for a movement type.
        
        Args:
            movement_type: Type of movement (orthogonal, diagonal, etc.)
            piece_pos: Position of the piece [row, col]
            
        Returns:
            8x8 pattern matrix with movement values
        """
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        piece_r, piece_c = piece_pos

        if movement_type == "orthogonal":
            # Rook pattern: orthogonal lines
            for c in range(8):
                if c != piece_c:
                    pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    pattern[r][piece_c] = 3

        elif movement_type == "diagonal":
            # Bishop pattern: diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            pattern[r][c] = 3

        elif movement_type == "any":
            # Queen pattern: orthogonal + diagonal lines
            for c in range(8):
                if c != piece_c:
                    pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    pattern[r][piece_c] = 3
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            pattern[r][c] = 3

        elif movement_type == "lShape":
            # Knight pattern: L-shaped moves
            knight_moves = [
                (-2, -1), (-2, 1), (-1, -2), (-1, 2),
                (1, -2), (1, 2), (2, -1), (2, 1)
            ]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    pattern[r][c] = 3

        elif movement_type == "king":
            # King pattern: adjacent squares (8 directions)
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue  # Skip piece position
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        pattern[r][c] = 3

        elif movement_type == "global":
            # Global pattern: entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != piece_pos:
                        pattern[r][c] = 3

        return pattern

    def on_movement_pattern_selected(self, movement_type: str) -> None:
        """
        Handle movement pattern selection from buttons.
        
        Args:
            movement_type: The selected movement type
        """
        try:
            # Update button selection
            for btn, btn_type in self.editor.movement_pattern_buttons:
                btn.setChecked(btn_type == movement_type)
            
            # Apply the movement preset
            self.apply_movement_preset(movement_type)
            self.editor.selected_movement_type = movement_type
            
            # Update UI
            self.update_movement_controls()
            self.editor.mark_unsaved_changes()
            
            logger.info(f"Movement pattern selected: {movement_type}")
            
        except Exception as e:
            logger.error(f"Error selecting movement pattern: {e}")

    def select_movement_preset(self, movement_type: str, button: QPushButton) -> None:
        """
        Select a movement preset and store full movement data.
        
        Args:
            movement_type: The movement type to select
            button: The button that was clicked
        """
        # Clear other button selections
        for btn, _ in self.editor.movement_pattern_buttons:
            if btn != button:
                btn.setChecked(False)

        # Apply the movement type if button is checked
        if button.isChecked():
            self.apply_movement_preset(movement_type)
            self.editor.selected_movement_type = movement_type
        else:
            # If unchecked, default to orthogonal
            self.apply_movement_preset("orthogonal")
            self.editor.selected_movement_type = "orthogonal"
            # Check the orthogonal button
            for btn, mt in self.editor.movement_pattern_buttons:
                if mt == "orthogonal":
                    btn.setChecked(True)
                    break

        self.update_movement_controls()
        self.editor.mark_unsaved_changes()

    def apply_movement_preset(self, movement_type: str) -> None:
        """
        Apply a movement preset to the current piece.
        
        Args:
            movement_type: The movement type to apply
        """
        if movement_type == "custom":
            # Open pattern editor for custom movement
            self.open_custom_pattern_editor()
        else:
            # Generate standard pattern for the movement type
            piece_pos = getattr(self.editor, 'custom_pattern_piece_pos', [3, 3])
            standard_pattern = self.generate_standard_pattern(movement_type, piece_pos)

            # Store the movement data with generated pattern
            self.editor.current_movement_data = {
                "type": movement_type,
                "pattern": standard_pattern,
                "piecePosition": piece_pos
            }

            # Update the current custom pattern to match the standard pattern
            self.editor.current_custom_pattern = standard_pattern

            # Update pattern preview
            self.update_movement_pattern_preview()

    def open_custom_pattern_editor(self) -> None:
        """Open the custom pattern editor dialog."""
        try:
            # Map movement types to preset highlights
            movement_to_preset_map = {
                'orthogonal': 'rook',
                'diagonal': 'bishop', 
                'any': 'queen',
                'lShape': 'knight',
                'king': 'king',
                'global': 'global'
            }

            # Get current movement type for highlighting
            current_movement = getattr(self.editor, 'selected_movement_type', 'orthogonal')
            preset_to_highlight = movement_to_preset_map.get(current_movement, None)

            # Prepare checkbox states with auto continue off board setting
            # King and Knight should NOT continue off board, all others should
            should_continue_off_board = current_movement not in ['king', 'lShape']

            checkbox_states = {
                'starting_square_checked': False,
                'continue_off_board_checked': should_continue_off_board
            }

            # Create dialog with highlighted preset and checkbox states
            dialog = PatternEditorDialog(
                movement_pattern=self.editor.current_custom_pattern,
                title="Movement Pattern Editor",
                parent=self.editor,
                checkbox_states=checkbox_states,
                highlighted_preset=preset_to_highlight
            )

            if dialog.exec() == dialog.DialogCode.Accepted:
                pattern, _ = dialog.get_pattern(), dialog.get_checkbox_states()
                if pattern is not None:
                    self.editor.current_custom_pattern = pattern
                    # Update movement data to custom type since pattern was edited
                    self.editor.current_movement_data = {
                        "type": "custom",
                        "pattern": pattern,
                        "piecePosition": getattr(self.editor, 'custom_pattern_piece_pos', [3, 3])
                    }
                    self.editor.selected_movement_type = "custom"
                    # Update button selection to custom
                    if hasattr(self.editor, 'movement_pattern_buttons'):
                        for btn, movement_type in self.editor.movement_pattern_buttons:
                            btn.setChecked(movement_type == "custom")

                    self.update_movement_pattern_preview()
                    self.update_movement_controls()
                    if hasattr(self.editor, 'log_console'):
                        self.editor.log_console.append("Custom pattern saved.")
                    self.editor.mark_unsaved_changes()
                    
        except Exception as e:
            logger.error(f"Error opening custom pattern editor: {e}")

    def update_movement_pattern_preview(self) -> None:
        """Update the movement pattern preview with actual pattern colors."""
        if not hasattr(self.editor, 'preview_grid_buttons') or not self.editor.preview_grid_buttons:
            return

        # Get current movement type and pattern
        movement_type = getattr(self.editor, 'selected_movement_type', 'orthogonal')

        # Generate or get the current pattern
        if hasattr(self.editor, 'current_custom_pattern') and self.editor.current_custom_pattern:
            pattern = self.editor.current_custom_pattern
        else:
            # Generate standard pattern for preview
            piece_pos = [3, 3]  # Center position for preview
            pattern = self.generate_standard_pattern(movement_type, piece_pos)

        # Set continue off board checkbox based on movement type
        self.set_continue_off_board_for_movement_type(movement_type)

        # Update the 8x8 preview grid with pattern colors
        for row in range(8):
            for col in range(8):
                btn = self.editor.preview_grid_buttons[row][col]

                if [row, col] == [3, 3]:  # Piece position (center)
                    # Piece position - match pattern editor styling
                    btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold; font-size: 8px; color: white;")
                    btn.setText("♔")
                else:
                    # Get pattern state
                    state = pattern[row][col] if pattern and len(pattern) > row and len(pattern[row]) > col else 0
                    btn.setText("")

                    # Apply pattern editor 6-color system styling
                    if state == 0:
                        btn.setStyleSheet("background: #2d3748; border: 1px solid #4a5568;")  # Empty
                    elif state == 1:
                        btn.setStyleSheet("background: #4444ff; border: 1px solid #0000aa;")  # Move only
                    elif state == 2:
                        btn.setStyleSheet("background: #ff4444; border: 1px solid #aa0000;")  # Attack only
                    elif state == 3:
                        btn.setStyleSheet("background: #aa44aa; border: 1px solid #660066;")  # Move and attack
                    elif state == 4:
                        btn.setStyleSheet("background: #ffff44; border: 1px solid #cccc00;")  # Action (Yellow)
                    else:  # state == 5
                        btn.setStyleSheet("background: #66ff66; border: 1px solid #44cc44;")  # Any

    def update_movement_controls(self) -> None:
        """Update movement controls based on selected type."""
        # Get current movement type from button selection or fallback to combo
        movement_type = getattr(self.editor, 'selected_movement_type', None)
        if not movement_type and hasattr(self.editor, 'move_combo'):
            movement_type = self.editor.move_combo.currentText().lower()

        # Update movement pattern preview
        self.update_movement_pattern_preview()

    def set_continue_off_board_for_movement_type(self, movement_type: str) -> None:
        """
        Set the continue off board checkbox based on movement type.
        
        Args:
            movement_type: The movement type to configure for
        """
        # King and Knight should NOT continue off board, all others should
        should_continue = movement_type not in ['king', 'lShape']
        
        # This would be used if we had a continue off board checkbox
        # For now, it's a placeholder for future implementation
        logger.debug(f"Movement type {movement_type} should continue off board: {should_continue}")

    def reset_movement_to_default(self) -> None:
        """Reset movement pattern to default (orthogonal)."""
        try:
            # Reset movement - select orthogonal button and generate pattern
            self.editor.selected_movement_type = "orthogonal"
            
            # Generate default orthogonal pattern
            default_pattern = self.generate_standard_pattern("orthogonal", [3, 3])
            self.editor.current_movement_data = {
                "type": "orthogonal",
                "pattern": default_pattern,
                "piecePosition": [3, 3]
            }
            self.editor.current_custom_pattern = default_pattern

            if hasattr(self.editor, 'movement_pattern_buttons'):
                # Clear all button selections first
                for btn, _ in self.editor.movement_pattern_buttons:
                    btn.setChecked(False)
                # Select orthogonal button
                for btn, movement_type in self.editor.movement_pattern_buttons:
                    if movement_type == "orthogonal":
                        btn.setChecked(True)
                        break
                        
            # Update UI
            self.update_movement_controls()
            self.update_movement_pattern_preview()
            
            logger.info("Movement pattern reset to default (orthogonal)")
            
        except Exception as e:
            logger.error(f"Error resetting movement to default: {e}")

    def get_movement_data(self) -> Dict[str, Any]:
        """
        Get the current movement data.
        
        Returns:
            Dictionary containing movement data
        """
        return {
            "type": getattr(self.editor, 'selected_movement_type', 'orthogonal'),
            "pattern": getattr(self.editor, 'current_custom_pattern', None),
            "piecePosition": getattr(self.editor, 'custom_pattern_piece_pos', [3, 3])
        }

    def set_movement_data(self, movement_data: Dict[str, Any]) -> None:
        """
        Set movement data from external source.
        
        Args:
            movement_data: Dictionary containing movement data
        """
        try:
            movement_type = movement_data.get('type', 'orthogonal')
            pattern = movement_data.get('pattern')
            piece_pos = movement_data.get('piecePosition', [3, 3])
            
            # Set movement type
            self.editor.selected_movement_type = movement_type
            
            # Set pattern
            if pattern:
                self.editor.current_custom_pattern = pattern
            else:
                # Generate standard pattern if none provided
                self.editor.current_custom_pattern = self.generate_standard_pattern(movement_type, piece_pos)
            
            # Set piece position
            self.editor.custom_pattern_piece_pos = piece_pos
            
            # Update movement data
            self.editor.current_movement_data = {
                "type": movement_type,
                "pattern": self.editor.current_custom_pattern,
                "piecePosition": piece_pos
            }
            
            # Update button selection
            if hasattr(self.editor, 'movement_pattern_buttons'):
                for btn, btn_movement_type in self.editor.movement_pattern_buttons:
                    btn.setChecked(btn_movement_type == movement_type)
            
            # Update UI
            self.update_movement_controls()
            self.update_movement_pattern_preview()
            
            logger.info(f"Movement data set: {movement_type}")
            
        except Exception as e:
            logger.error(f"Error setting movement data: {e}")
            raise
