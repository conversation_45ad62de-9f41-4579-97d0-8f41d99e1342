"""
Ability Data Handlers for Adventure Chess Creator

This module provides a simplified ability data handler that uses the new
base classes for consistent data handling across the application.

The AbilityDataHandler now inherits from EnhancedAbilityDataHandler which
provides standardized data operations while maintaining backward compatibility.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from PyQt6.QtWidgets import QMessageBox

# Local imports
from config import DEFAULT_ABILITY
from utils.simple_bridge import simple_bridge
from core.handlers.specialized_data_handlers import EnhancedAbilityDataHandler

logger = logging.getLogger(__name__)


class AbilityDataHandler(EnhancedAbilityDataHandler):
    """
    Simplified ability data handler that extends EnhancedAbilityDataHandler.

    This class now focuses on ability-specific functionality while
    inheriting standardized data operations from the base class.

    Provides:
    - Tag configuration data processing
    - Ability-specific UI updates
    - Tag management integration
    """

    def __init__(self, editor_instance):
        """
        Initialize the ability data handler.

        Args:
            editor_instance: The AbilityEditorWindow instance
        """
        super().__init__(editor_instance)
    
    def collect_ability_data(self) -> Dict[str, Any]:
        """
        Collect ability data using the base class with ability-specific processing.

        Returns:
            Dictionary containing all ability data
        """
        try:
            # Use base class collect_data method for standardized collection
            data = self.collect_data()

            logger.debug(f"Collected ability data: {data.get('name', 'Unnamed')}")
            return data

        except Exception as e:
            logger.error(f"Error collecting ability data: {e}")
            # Return minimal valid data structure
            return DEFAULT_ABILITY.copy()
    
    def populate_ability_data(self, data: Dict[str, Any]) -> bool:
        """
        Populate UI widgets with ability data using the base class.

        Args:
            data: Dictionary containing ability data

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use base class populate_data method for standardized population
            self.populate_data(data)

            # Perform ability-specific post-populate updates
            self.post_populate_ui_updates()

            logger.debug(f"Populated ability data: {data.get('name', 'Unnamed')}")
            return True
            
        except Exception as e:
            logger.error(f"Error populating ability data: {e}")
            if hasattr(self.editor, 'status_widget'):
                self.editor.status_widget.show_error("Error loading ability data")
            return False
    
    def _update_inline_selectors_safely(self, data: Dict[str, Any]) -> None:
        """
        Safely update inline selectors with data.
        
        Args:
            data: Ability data dictionary
        """
        try:
            if hasattr(self.editor, 'update_inline_selectors_with_data'):
                self.editor.update_inline_selectors_with_data()
        except Exception as e:
            logger.warning(f"Could not update inline selectors: {e}")
    
    def validate_ability_data(self, data: Dict[str, Any]) -> List[str]:
        """
        Validate ability data for consistency and completeness.
        
        Args:
            data: Dictionary containing ability data
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        try:
            # Check required fields
            if not data.get('name', '').strip():
                errors.append("Ability name is required")
            
            # Check cost is valid
            cost = data.get('cost', 0)
            if not isinstance(cost, (int, float)) or cost < 0:
                errors.append("Cost must be a non-negative number")
            
            # Check activation mode is valid
            valid_modes = ['auto', 'click']
            activation_mode = data.get('activationMode', 'auto')
            if activation_mode not in valid_modes:
                errors.append(f"Invalid activation mode: {activation_mode}")
            
            # Check tags are valid
            tags = data.get('tags', [])
            if not isinstance(tags, list):
                errors.append("Tags must be a list")
            
            # Additional validation can be added here
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def load_ability_from_file(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Load ability data from file.
        
        Args:
            filename: Name of the file to load (without .json extension)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Load data using SimpleBridge
            data, error = simple_bridge.load_ability_for_ui(filename)
            
            if error:
                logger.error(f"Failed to load ability from file: {error}")
                return False, error
            
            if not data:
                error_msg = "No data loaded from file"
                logger.error(error_msg)
                return False, error_msg
            
            # Populate UI with loaded data
            success = self.populate_ability_data(data)
            if not success:
                return False, "Failed to populate UI with loaded data"
            
            # Update editor state
            self.editor.current_filename = filename
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info(f"Successfully loaded ability: {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Exception loading ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def save_ability_to_file(self, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data to file.
        
        Args:
            filename: Optional filename (without .json extension)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Collect current data from UI
            data = self.collect_ability_data()
            
            # Validate data before saving
            validation_errors = self.validate_ability_data(data)
            if validation_errors:
                error_msg = "Validation failed:\n" + "\n".join(validation_errors)
                logger.error(error_msg)
                return False, error_msg
            
            # Save using SimpleBridge
            success, error = simple_bridge.save_ability_from_ui(self.editor, filename)
            
            if not success:
                logger.error(f"Failed to save ability: {error}")
                return False, error
            
            # Update editor state
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info(f"Successfully saved ability: {data.get('name', 'Unnamed')}")
            return True, None
            
        except Exception as e:
            error_msg = f"Exception saving ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def reset_to_defaults(self) -> None:
        """Reset the editor to default values."""
        try:
            default_data = DEFAULT_ABILITY.copy()
            self.populate_ability_data(default_data)
            
            # Clear filename and unsaved changes
            self.editor.current_filename = None
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info("Reset ability editor to defaults")
            
        except Exception as e:
            logger.error(f"Error resetting to defaults: {e}")
    
    def get_current_ability_name(self) -> str:
        """Get the name of the current ability."""
        try:
            data = self.collect_ability_data()
            return data.get('name', 'Unnamed Ability')
        except Exception as e:
            logger.error(f"Error getting ability name: {e}")
            return 'Unnamed Ability'
