"""
Ability Data Handlers for Adventure Chess Creator

This module handles all data operations for the ability editor:
- Data collection from UI widgets
- Data population to UI widgets  
- Data validation and transformation
- Integration with SimpleBridge and EditorDataInterface

Extracted from ability_editor.py to improve maintainability and isolate
data handling logic for easier debugging of loading/saving issues.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from PyQt6.QtWidgets import QMessageBox

# Local imports
from config import DEFAULT_ABILITY
from utils.simple_bridge import simple_bridge
from utils.editor_data_interface import EditorDataInterface

logger = logging.getLogger(__name__)


class AbilityDataHandler:
    """
    Handles all data operations for the ability editor.
    
    This class provides a clean interface between the UI and data storage,
    making it easier to debug loading/saving issues and maintain data consistency.
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the data handler.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
        """
        self.editor = editor_instance
        self.data_interface = EditorDataInterface()
    
    def collect_ability_data(self) -> Dict[str, Any]:
        """
        Collect all ability data from UI widgets.
        
        Returns:
            Dictionary containing all ability data
        """
        try:
            # Use the standardized data collection from EditorDataInterface
            data = self.data_interface.collect_data_from_ui(self.editor, "ability")

            # Collect tag-specific data from the tag manager
            if hasattr(self.editor, 'tag_manager'):
                tag_data = self.editor.tag_manager.collect_tag_data()
                data.update(tag_data)

            # Add version information
            data['version'] = '1.0.0'

            # Ensure required fields exist with defaults
            data.setdefault('name', '')
            data.setdefault('description', '')
            data.setdefault('cost', 0)
            data.setdefault('activationMode', 'auto')
            data.setdefault('tags', [])
            
            logger.debug(f"Collected ability data: {data.get('name', 'Unnamed')}")
            return data
            
        except Exception as e:
            logger.error(f"Error collecting ability data: {e}")
            # Return minimal valid data structure
            return DEFAULT_ABILITY.copy()
    
    def populate_ability_data(self, data: Dict[str, Any]) -> bool:
        """
        Populate UI widgets with ability data.
        
        Args:
            data: Dictionary containing ability data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate data structure
            if not isinstance(data, dict):
                logger.error("Invalid data type for ability data")
                return False
            
            # Check version and warn if outdated
            file_version = data.get('version', '0.0.0')
            current_version = '1.0.0'
            
            if file_version != current_version:
                if hasattr(self.editor, 'status_widget'):
                    self.editor.status_widget.show_warning(
                        f"⚠️ Outdated file format (v{file_version} → v{current_version}). Save to update."
                    )
            
            # Use standardized widget population
            self.data_interface.populate_ui_from_data(self.editor, data, "ability")
            
            # Update the editor's current data
            self.editor.current_data = data.copy()
            self.editor.current_ability = data.copy()  # Legacy compatibility

            # Set selected tags from data
            if hasattr(self.editor, 'tag_manager') and 'tags' in data:
                self.editor.tag_manager.set_selected_tags(data['tags'])
                # Populate tag-specific data after tags are set
                self.editor.tag_manager.populate_tag_data(data)

            # Update inline selectors with current data
            self._update_inline_selectors_safely(data)
            
            logger.info(f"Successfully populated ability data: {data.get('name', 'Unnamed')}")
            return True
            
        except Exception as e:
            logger.error(f"Error populating ability data: {e}")
            if hasattr(self.editor, 'status_widget'):
                self.editor.status_widget.show_error("Error loading ability data")
            return False
    
    def _update_inline_selectors_safely(self, data: Dict[str, Any]) -> None:
        """
        Safely update inline selectors with data.
        
        Args:
            data: Ability data dictionary
        """
        try:
            if hasattr(self.editor, 'update_inline_selectors_with_data'):
                self.editor.update_inline_selectors_with_data()
        except Exception as e:
            logger.warning(f"Could not update inline selectors: {e}")
    
    def validate_ability_data(self, data: Dict[str, Any]) -> List[str]:
        """
        Validate ability data for consistency and completeness.
        
        Args:
            data: Dictionary containing ability data
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        try:
            # Check required fields
            if not data.get('name', '').strip():
                errors.append("Ability name is required")
            
            # Check cost is valid
            cost = data.get('cost', 0)
            if not isinstance(cost, (int, float)) or cost < 0:
                errors.append("Cost must be a non-negative number")
            
            # Check activation mode is valid
            valid_modes = ['auto', 'click']
            activation_mode = data.get('activationMode', 'auto')
            if activation_mode not in valid_modes:
                errors.append(f"Invalid activation mode: {activation_mode}")
            
            # Check tags are valid
            tags = data.get('tags', [])
            if not isinstance(tags, list):
                errors.append("Tags must be a list")
            
            # Additional validation can be added here
            
        except Exception as e:
            logger.error(f"Error during validation: {e}")
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def load_ability_from_file(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Load ability data from file.
        
        Args:
            filename: Name of the file to load (without .json extension)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Load data using SimpleBridge
            data, error = simple_bridge.load_ability_for_ui(filename)
            
            if error:
                logger.error(f"Failed to load ability from file: {error}")
                return False, error
            
            if not data:
                error_msg = "No data loaded from file"
                logger.error(error_msg)
                return False, error_msg
            
            # Populate UI with loaded data
            success = self.populate_ability_data(data)
            if not success:
                return False, "Failed to populate UI with loaded data"
            
            # Update editor state
            self.editor.current_filename = filename
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info(f"Successfully loaded ability: {filename}")
            return True, None
            
        except Exception as e:
            error_msg = f"Exception loading ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def save_ability_to_file(self, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data to file.
        
        Args:
            filename: Optional filename (without .json extension)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Collect current data from UI
            data = self.collect_ability_data()
            
            # Validate data before saving
            validation_errors = self.validate_ability_data(data)
            if validation_errors:
                error_msg = "Validation failed:\n" + "\n".join(validation_errors)
                logger.error(error_msg)
                return False, error_msg
            
            # Save using SimpleBridge
            success, error = simple_bridge.save_ability_from_ui(self.editor, filename)
            
            if not success:
                logger.error(f"Failed to save ability: {error}")
                return False, error
            
            # Update editor state
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info(f"Successfully saved ability: {data.get('name', 'Unnamed')}")
            return True, None
            
        except Exception as e:
            error_msg = f"Exception saving ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def reset_to_defaults(self) -> None:
        """Reset the editor to default values."""
        try:
            default_data = DEFAULT_ABILITY.copy()
            self.populate_ability_data(default_data)
            
            # Clear filename and unsaved changes
            self.editor.current_filename = None
            if hasattr(self.editor, 'clear_unsaved_changes'):
                self.editor.clear_unsaved_changes()
            
            logger.info("Reset ability editor to defaults")
            
        except Exception as e:
            logger.error(f"Error resetting to defaults: {e}")
    
    def get_current_ability_name(self) -> str:
        """Get the name of the current ability."""
        try:
            data = self.collect_ability_data()
            return data.get('name', 'Unnamed Ability')
        except Exception as e:
            logger.error(f"Error getting ability name: {e}")
            return 'Unnamed Ability'
