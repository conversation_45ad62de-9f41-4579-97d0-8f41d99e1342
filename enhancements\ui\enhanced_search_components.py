"""
Enhanced Search Components for Adventure Chess Creator

This module provides advanced search UI components that leverage the file system optimizer:
- EnhancedSearchWidget: Advanced search with filters and suggestions
- SearchResultsWidget: Rich search results display
- FileIndexBrowser: Browse and manage the file index
- SearchAnalytics: Search performance analytics

Features:
- Real-time search suggestions
- Advanced filtering options
- Search result previews
- Performance analytics
- Index management tools
"""

import logging
from typing import List, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QListWidget, 
    QListWidgetItem, QLabel, QPushButton, QComboBox, QCheckBox,
    QTextEdit, QSplitter, QGroupBox, QProgressBar, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QSpinBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPalette

from file_system_optimizer import get_file_system_optimizer, SearchResult

logger = logging.getLogger(__name__)

class SearchWorker(QThread):
    """Background worker for search operations"""
    
    search_completed = pyqtSignal(list)  # List[SearchResult]
    search_error = pyqtSignal(str)
    
    def __init__(self, query: str, file_type: Optional[str] = None, max_results: int = 50):
        super().__init__()
        self.query = query
        self.file_type = file_type
        self.max_results = max_results
    
    def run(self):
        try:
            optimizer = get_file_system_optimizer()
            results = optimizer.search_files(
                query=self.query,
                file_type=self.file_type,
                max_results=self.max_results
            )
            self.search_completed.emit(results)
        except Exception as e:
            logger.error(f"Search error: {e}")
            self.search_error.emit(str(e))

class EnhancedSearchWidget(QWidget):
    """
    Advanced search widget with real-time suggestions and filtering
    """
    
    file_selected = pyqtSignal(str, dict)  # file_path, metadata
    search_performed = pyqtSignal(str)  # query
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.optimizer = get_file_system_optimizer()
        self.search_worker: Optional[SearchWorker] = None
        self.suggestion_timer = QTimer()
        self.suggestion_timer.setSingleShot(True)
        self.suggestion_timer.timeout.connect(self._update_suggestions)
        
        self.setup_ui()
        
        # Initialize index
        self.optimizer.update_index()
    
    def setup_ui(self):
        """Setup the search UI"""
        layout = QVBoxLayout()
        
        # Search input section
        search_section = self._create_search_section()
        layout.addWidget(search_section)
        
        # Results section
        results_section = self._create_results_section()
        layout.addWidget(results_section)
        
        # Analytics section
        analytics_section = self._create_analytics_section()
        layout.addWidget(analytics_section)
        
        self.setLayout(layout)
    
    def _create_search_section(self) -> QWidget:
        """Create the search input section"""
        section = QGroupBox("🔍 Enhanced Search")
        layout = QVBoxLayout()
        
        # Main search input
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search pieces and abilities...")
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.search_input.returnPressed.connect(self._perform_search)
        
        self.search_button = QPushButton("🔍 Search")
        self.search_button.clicked.connect(self._perform_search)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_button)
        layout.addLayout(search_layout)
        
        # Filters
        filters_layout = QHBoxLayout()
        
        # File type filter
        filters_layout.addWidget(QLabel("Type:"))
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(["All", "Pieces", "Abilities"])
        self.file_type_combo.currentTextChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.file_type_combo)
        
        # Max results
        filters_layout.addWidget(QLabel("Max Results:"))
        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(10, 200)
        self.max_results_spin.setValue(50)
        self.max_results_spin.valueChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.max_results_spin)
        
        filters_layout.addStretch()
        layout.addLayout(filters_layout)
        
        # Suggestions
        self.suggestions_list = QListWidget()
        self.suggestions_list.setMaximumHeight(100)
        self.suggestions_list.itemClicked.connect(self._on_suggestion_clicked)
        self.suggestions_list.hide()
        layout.addWidget(self.suggestions_list)
        
        section.setLayout(layout)
        return section
    
    def _create_results_section(self) -> QWidget:
        """Create the search results section"""
        section = QGroupBox("📋 Search Results")
        layout = QVBoxLayout()
        
        # Results info
        self.results_info = QLabel("Ready to search...")
        layout.addWidget(self.results_info)
        
        # Results list
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self._on_result_clicked)
        layout.addWidget(self.results_list)
        
        # Result preview
        self.result_preview = QTextEdit()
        self.result_preview.setMaximumHeight(100)
        self.result_preview.setReadOnly(True)
        layout.addWidget(self.result_preview)
        
        section.setLayout(layout)
        return section
    
    def _create_analytics_section(self) -> QWidget:
        """Create the analytics section"""
        section = QGroupBox("📊 Search Analytics")
        layout = QHBoxLayout()
        
        self.analytics_label = QLabel("No searches performed yet")
        layout.addWidget(self.analytics_label)
        
        self.refresh_index_btn = QPushButton("🔄 Refresh Index")
        self.refresh_index_btn.clicked.connect(self._refresh_index)
        layout.addWidget(self.refresh_index_btn)
        
        section.setLayout(layout)
        return section
    
    def _on_search_text_changed(self, text: str):
        """Handle search text changes for suggestions"""
        if len(text) >= 2:
            self.suggestion_timer.start(300)  # 300ms delay
        else:
            self.suggestions_list.hide()
    
    def _update_suggestions(self):
        """Update search suggestions"""
        try:
            query = self.search_input.text()
            if len(query) < 2:
                return
            
            file_type = self._get_selected_file_type()
            suggestions = self.optimizer.get_file_suggestions(query, file_type, limit=5)
            
            self.suggestions_list.clear()
            if suggestions:
                for suggestion in suggestions:
                    self.suggestions_list.addItem(suggestion)
                self.suggestions_list.show()
            else:
                self.suggestions_list.hide()
                
        except Exception as e:
            logger.error(f"Error updating suggestions: {e}")
    
    def _on_suggestion_clicked(self, item: QListWidgetItem):
        """Handle suggestion click"""
        self.search_input.setText(item.text())
        self.suggestions_list.hide()
        self._perform_search()
    
    def _on_filter_changed(self):
        """Handle filter changes"""
        if self.search_input.text().strip():
            self._perform_search()
    
    def _perform_search(self):
        """Perform the search"""
        query = self.search_input.text().strip()
        if not query:
            return
        
        # Hide suggestions
        self.suggestions_list.hide()
        
        # Show loading
        self.results_info.setText("🔄 Searching...")
        self.results_list.clear()
        self.result_preview.clear()
        
        # Start search worker
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.terminate()
        
        file_type = self._get_selected_file_type()
        max_results = self.max_results_spin.value()
        
        self.search_worker = SearchWorker(query, file_type, max_results)
        self.search_worker.search_completed.connect(self._on_search_completed)
        self.search_worker.search_error.connect(self._on_search_error)
        self.search_worker.start()
        
        self.search_performed.emit(query)
    
    def _get_selected_file_type(self) -> Optional[str]:
        """Get the selected file type filter"""
        type_text = self.file_type_combo.currentText()
        if type_text == "Pieces":
            return "piece"
        elif type_text == "Abilities":
            return "ability"
        return None
    
    @pyqtSlot(list)
    def _on_search_completed(self, results: List[SearchResult]):
        """Handle search completion"""
        self.results_list.clear()
        
        if not results:
            self.results_info.setText("No results found")
            return
        
        self.results_info.setText(f"Found {len(results)} results")
        
        for result in results:
            # Create result item
            item_text = f"📄 {result.filename}"
            if result.relevance_score > 0:
                item_text += f" (Score: {result.relevance_score:.1f})"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, result)
            
            # Add matched fields info
            if result.matched_fields:
                tooltip = f"Matched fields: {', '.join(result.matched_fields)}\n"
                tooltip += f"Preview: {result.preview_text}"
                item.setToolTip(tooltip)
            
            self.results_list.addItem(item)
        
        # Update analytics
        self._update_analytics()
    
    @pyqtSlot(str)
    def _on_search_error(self, error: str):
        """Handle search error"""
        self.results_info.setText(f"Search error: {error}")
        logger.error(f"Search error: {error}")
    
    def _on_result_clicked(self, item: QListWidgetItem):
        """Handle result click"""
        result: SearchResult = item.data(Qt.ItemDataRole.UserRole)
        if result:
            # Show preview
            preview_text = f"File: {result.filename}\n"
            preview_text += f"Type: {result.file_type}\n"
            preview_text += f"Relevance: {result.relevance_score:.2f}\n"
            preview_text += f"Preview: {result.preview_text}\n"
            
            if result.metadata.get('tags'):
                preview_text += f"Tags: {', '.join(result.metadata['tags'])}\n"
            
            self.result_preview.setText(preview_text)
            
            # Emit selection signal
            self.file_selected.emit(result.file_path, result.metadata)
    
    def _refresh_index(self):
        """Refresh the file index"""
        try:
            self.refresh_index_btn.setText("🔄 Refreshing...")
            self.refresh_index_btn.setEnabled(False)
            
            indexed_count = self.optimizer.update_index()
            
            self.refresh_index_btn.setText("🔄 Refresh Index")
            self.refresh_index_btn.setEnabled(True)
            
            self.results_info.setText(f"Index refreshed: {indexed_count} files processed")
            self._update_analytics()
            
        except Exception as e:
            logger.error(f"Error refreshing index: {e}")
            self.refresh_index_btn.setText("🔄 Refresh Index")
            self.refresh_index_btn.setEnabled(True)
    
    def _update_analytics(self):
        """Update analytics display"""
        try:
            stats = self.optimizer.get_index_statistics()
            search_stats = stats.get('search_stats', {})
            
            analytics_text = f"Files: {stats.get('total_files', 0)} | "
            analytics_text += f"Searches: {search_stats.get('total_searches', 0)} | "
            analytics_text += f"Avg Time: {search_stats.get('avg_search_time_ms', 0):.1f}ms"
            
            self.analytics_label.setText(analytics_text)
            
        except Exception as e:
            logger.error(f"Error updating analytics: {e}")

class FileIndexBrowser(QWidget):
    """
    Widget for browsing and managing the file index
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.optimizer = get_file_system_optimizer()
        self.setup_ui()
        self.refresh_data()
    
    def setup_ui(self):
        """Setup the browser UI"""
        layout = QVBoxLayout()
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_data)
        controls_layout.addWidget(self.refresh_btn)
        
        self.stats_label = QLabel("Loading...")
        controls_layout.addWidget(self.stats_label)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Index table
        self.index_table = QTableWidget()
        self.index_table.setColumnCount(6)
        self.index_table.setHorizontalHeaderLabels([
            "Filename", "Type", "Size", "Modified", "Tags", "Description"
        ])
        
        # Make table sortable and resizable
        self.index_table.setSortingEnabled(True)
        header = self.index_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)
        
        layout.addWidget(self.index_table)
        
        self.setLayout(layout)
    
    def refresh_data(self):
        """Refresh the index data display"""
        try:
            # Update statistics
            stats = self.optimizer.get_index_statistics()
            stats_text = f"Total Files: {stats.get('total_files', 0)} | "
            stats_text += f"Size: {stats.get('total_size_bytes', 0) / 1024:.1f} KB"
            self.stats_label.setText(stats_text)
            
            # Load and display index entries in table
            try:
                index_entries = self.optimizer.get_all_index_entries()
                self.index_table.setRowCount(len(index_entries))

                for row, entry in enumerate(index_entries):
                    # File path
                    self.index_table.setItem(row, 0, QTableWidgetItem(entry.get('file_path', '')))
                    # File type
                    self.index_table.setItem(row, 1, QTableWidgetItem(entry.get('file_type', '')))
                    # Size
                    size_kb = entry.get('size_bytes', 0) / 1024
                    self.index_table.setItem(row, 2, QTableWidgetItem(f"{size_kb:.1f} KB"))
                    # Last modified
                    last_modified = entry.get('last_modified', '')
                    self.index_table.setItem(row, 3, QTableWidgetItem(str(last_modified)))

            except AttributeError:
                # Fallback if get_all_index_entries method doesn't exist
                self.index_table.setRowCount(0)
            except Exception as e:
                logger.error(f"Error loading index entries: {e}")
                self.index_table.setRowCount(0)
            
        except Exception as e:
            logger.error(f"Error refreshing index browser: {e}")
            self.stats_label.setText("Error loading data")
