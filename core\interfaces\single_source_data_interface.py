"""
Single Source of Truth Data Interface for Adventure Chess Creator

This module provides a unified interface for accessing all application data
through the established single source of truth patterns. It enforces data
ownership rules and provides consistent access methods across all components.

The interface ensures that:
- All data access goes through the correct owner component
- Legacy aliases are properly redirected to canonical sources
- Data consistency is maintained across all operations
- Change tracking works correctly with the unified data model
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from ..managers.data_ownership_registry import data_ownership_registry, DataAccessPattern

logger = logging.getLogger(__name__)


class SingleSourceDataInterface:
    """
    Unified interface for accessing all application data through single source of truth patterns.
    
    This interface acts as a central hub for all data operations, ensuring that
    data is always accessed through the correct owner component and that legacy
    compatibility is maintained.
    """
    
    def __init__(self):
        """Initialize the single source data interface."""
        self._access_cache = {}
        self._change_listeners = {}
    
    # ========== CORE DATA ACCESS METHODS ==========
    
    def get_data(self, data_type: str, owner_instance: Any, key: Optional[str] = None) -> Any:
        """
        Get data from the single source of truth for the specified data type.
        
        Args:
            data_type: Type of data to retrieve
            owner_instance: Instance of the owner component
            key: Specific key to retrieve (if None, returns all data)
            
        Returns:
            Data value or dictionary
        """
        rule = data_ownership_registry.get_ownership_rule(data_type)
        if not rule:
            logger.error(f"No ownership rule found for data type: {data_type}")
            return None
        
        # Validate owner instance
        if not self._validate_owner_instance(rule, owner_instance):
            logger.error(f"Invalid owner instance for {data_type}")
            return None
        
        # Get data based on access pattern
        try:
            if rule.access_pattern == DataAccessPattern.DIRECT_PROPERTY:
                data_source = getattr(owner_instance, rule.owner_property.split('.')[0])
                if key:
                    return data_source.get(key) if isinstance(data_source, dict) else getattr(data_source, key, None)
                return data_source
            
            elif rule.access_pattern == DataAccessPattern.COMPUTED_PROPERTY:
                # For computed properties, use the property directly
                return getattr(owner_instance, rule.owner_property)
            
            elif rule.access_pattern == DataAccessPattern.MANAGER_MEDIATED:
                # Access through manager
                manager = getattr(owner_instance, rule.owner_property.split('.')[0])
                if hasattr(manager, 'get_data'):
                    return manager.get_data(key) if key else manager.get_all_data()
                return getattr(manager, rule.owner_property.split('.')[-1])
            
            elif rule.access_pattern == DataAccessPattern.INTERFACE_MEDIATED:
                # Access through interface
                interface = getattr(owner_instance, rule.owner_property.split('.')[0])
                if hasattr(interface, 'get_data'):
                    return interface.get_data(key) if key else interface.get_all_data()
                return getattr(interface, rule.owner_property.split('.')[-1])
            
        except Exception as e:
            logger.error(f"Error accessing data for {data_type}: {e}")
            return None
    
    def set_data(self, data_type: str, owner_instance: Any, value: Any, key: Optional[str] = None) -> bool:
        """
        Set data in the single source of truth for the specified data type.
        
        Args:
            data_type: Type of data to set
            owner_instance: Instance of the owner component
            value: Value to set
            key: Specific key to set (if None, replaces all data)
            
        Returns:
            True if successful, False otherwise
        """
        rule = data_ownership_registry.get_ownership_rule(data_type)
        if not rule:
            logger.error(f"No ownership rule found for data type: {data_type}")
            return False
        
        # Validate owner instance
        if not self._validate_owner_instance(rule, owner_instance):
            logger.error(f"Invalid owner instance for {data_type}")
            return False
        
        # Set data based on access pattern
        try:
            if rule.access_pattern == DataAccessPattern.DIRECT_PROPERTY:
                if key:
                    data_source = getattr(owner_instance, rule.owner_property.split('.')[0])
                    if isinstance(data_source, dict):
                        data_source[key] = value
                    else:
                        setattr(data_source, key, value)
                else:
                    setattr(owner_instance, rule.owner_property.split('.')[0], value)
                
            elif rule.access_pattern == DataAccessPattern.COMPUTED_PROPERTY:
                # For computed properties, set through the property
                setattr(owner_instance, rule.owner_property, value)
            
            elif rule.access_pattern == DataAccessPattern.MANAGER_MEDIATED:
                # Set through manager
                manager = getattr(owner_instance, rule.owner_property.split('.')[0])
                if hasattr(manager, 'set_data'):
                    manager.set_data(key, value) if key else manager.set_all_data(value)
                else:
                    setattr(manager, rule.owner_property.split('.')[-1], value)
            
            elif rule.access_pattern == DataAccessPattern.INTERFACE_MEDIATED:
                # Set through interface
                interface = getattr(owner_instance, rule.owner_property.split('.')[0])
                if hasattr(interface, 'set_data'):
                    interface.set_data(key, value) if key else interface.set_all_data(value)
                else:
                    setattr(interface, rule.owner_property.split('.')[-1], value)
            
            # Notify change listeners
            self._notify_data_change(data_type, key, value)
            return True
            
        except Exception as e:
            logger.error(f"Error setting data for {data_type}: {e}")
            return False
    
    # ========== LEGACY COMPATIBILITY METHODS ==========
    
    def get_legacy_data(self, legacy_alias: str, owner_instance: Any) -> Any:
        """
        Get data using a legacy alias, redirecting to the canonical source.
        
        Args:
            legacy_alias: Legacy field name or alias
            owner_instance: Instance that would have owned the legacy data
            
        Returns:
            Data value from canonical source
        """
        canonical_type = data_ownership_registry.get_canonical_data_type(legacy_alias)
        if canonical_type:
            logger.debug(f"Redirecting legacy access '{legacy_alias}' to '{canonical_type}'")
            return self.get_data(canonical_type, owner_instance)
        
        logger.warning(f"No canonical mapping found for legacy alias: {legacy_alias}")
        return None
    
    def set_legacy_data(self, legacy_alias: str, owner_instance: Any, value: Any) -> bool:
        """
        Set data using a legacy alias, redirecting to the canonical source.
        
        Args:
            legacy_alias: Legacy field name or alias
            owner_instance: Instance that would have owned the legacy data
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        canonical_type = data_ownership_registry.get_canonical_data_type(legacy_alias)
        if canonical_type:
            logger.debug(f"Redirecting legacy write '{legacy_alias}' to '{canonical_type}'")
            return self.set_data(canonical_type, owner_instance, value)
        
        logger.warning(f"No canonical mapping found for legacy alias: {legacy_alias}")
        return False
    
    # ========== VALIDATION AND UTILITY METHODS ==========
    
    def _validate_owner_instance(self, rule, owner_instance) -> bool:
        """
        Validate that the owner instance matches the ownership rule.
        
        Args:
            rule: DataOwnershipRule to validate against
            owner_instance: Instance to validate
            
        Returns:
            True if valid, False otherwise
        """
        expected_class = rule.owner_component
        actual_class = owner_instance.__class__.__name__
        
        # Allow inheritance - check if instance is of expected type or subclass
        return expected_class in [cls.__name__ for cls in owner_instance.__class__.__mro__]
    
    def _notify_data_change(self, data_type: str, key: Optional[str], value: Any) -> None:
        """
        Notify registered listeners of data changes.
        
        Args:
            data_type: Type of data that changed
            key: Specific key that changed (if any)
            value: New value
        """
        listeners = self._change_listeners.get(data_type, [])
        for listener in listeners:
            try:
                listener(data_type, key, value)
            except Exception as e:
                logger.warning(f"Error in data change listener: {e}")
    
    def add_change_listener(self, data_type: str, listener_func) -> None:
        """
        Add a change listener for a specific data type.
        
        Args:
            data_type: Type of data to listen for changes
            listener_func: Function to call on changes (data_type, key, value)
        """
        if data_type not in self._change_listeners:
            self._change_listeners[data_type] = []
        
        if listener_func not in self._change_listeners[data_type]:
            self._change_listeners[data_type].append(listener_func)
    
    def remove_change_listener(self, data_type: str, listener_func) -> None:
        """
        Remove a change listener for a specific data type.
        
        Args:
            data_type: Type of data to stop listening for changes
            listener_func: Function to remove from listeners
        """
        if data_type in self._change_listeners:
            if listener_func in self._change_listeners[data_type]:
                self._change_listeners[data_type].remove(listener_func)
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all data types and their current ownership status.
        
        Returns:
            Dictionary with data type information
        """
        return data_ownership_registry.get_ownership_summary()


# Global interface instance
single_source_data_interface = SingleSourceDataInterface()
