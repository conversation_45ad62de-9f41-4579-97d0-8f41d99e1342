"""
Debuff Piece tag configuration for the Adventure Chess Creator ability editor.
Handles temporary weakening of target pieces with negative effects.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QVBoxLayout, QFormLayout, QGroupBox, QLabel, QSpinBox, QCheckBox, QPushButton, QHBoxLayout
)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector, InlineAbilitySelector
from dialogs.range_editor_dialog import edit_target_range


class DebuffPieceConfig(BaseTagConfig):
    """Configuration for debuffPiece tag - temporary piece weakening."""

    def __init__(self, editor_instance):
        super().__init__(editor_instance, "debuffPiece")
        # Initialize movement range data
        self.movement_range_pattern = [[False] * 8 for _ in range(8)]
        self.movement_piece_position = [3, 3]
        self.movement_checkbox_states = {}
    
    def get_title(self) -> str:
        return "⬇️ Debuff Piece Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the debuff piece configuration UI."""
        try:
            # Main group box
            group = QGroupBox(self.get_title())
            layout = QVBoxLayout()
            
            # Description
            description = QLabel("Weaken target pieces with negative effects or ability blocks.")
            description.setWordWrap(True)
            layout.addWidget(description)
            
            # Target piece selector
            target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("debuff_target_selector", target_selector)
            layout.addWidget(target_selector)
            
            # Duration settings
            duration_layout = QFormLayout()
            
            duration_spin = QSpinBox()
            duration_spin.setRange(0, 20)
            duration_spin.setValue(1)  # Default per glossary V1.0.1
            duration_spin.setSuffix(" turns")
            duration_spin.setSpecialValueText("Indefinite")
            duration_spin.setToolTip("Duration of the debuff effect (0 = indefinite)")
            self.store_widget("debuff_duration_spin", duration_spin)
            duration_layout.addRow("Duration:", duration_spin)
            
            layout.addLayout(duration_layout)
            
            # Debuff options
            options_group = QGroupBox("Debuff Options")
            options_layout = QVBoxLayout()

            # Movement range editor
            movement_layout = QHBoxLayout()
            movement_check = QCheckBox("Restrict Movement Range")
            movement_check.setToolTip("Restrict piece movement to specific squares")
            self.store_widget("debuff_movement_check", movement_check)
            movement_layout.addWidget(movement_check)

            movement_button = QPushButton("Edit Movement Range")
            movement_button.setToolTip("Configure which squares the piece can move to")
            movement_button.clicked.connect(self.edit_movement_range)
            self.store_widget("debuff_movement_button", movement_button)
            movement_layout.addWidget(movement_button)
            options_layout.addLayout(movement_layout)

            # Abilities checkbox and inline selector
            abilities_layout = QVBoxLayout()
            ability_check = QCheckBox("Block Specific Abilities")
            ability_check.setToolTip("Block specific abilities from being used")
            self.store_widget("debuff_ability_check", ability_check)
            abilities_layout.addWidget(ability_check)

            ability_selector = InlineAbilitySelector(self.editor, "Blocked Abilities")
            ability_selector.setToolTip("Select which abilities to block")
            self.store_widget("debuff_ability_selector", ability_selector)
            abilities_layout.addWidget(ability_selector)
            options_layout.addLayout(abilities_layout)

            # Show/hide ability selector based on checkbox
            def toggle_ability_selector():
                ability_selector.setVisible(ability_check.isChecked())
            ability_check.toggled.connect(toggle_ability_selector)
            toggle_ability_selector()  # Set initial state

            options_group.setLayout(options_layout)
            layout.addWidget(options_group)
            
            group.setLayout(layout)
            parent_layout.addWidget(group)
            
            self.log_debug("Debuff piece configuration UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating debuff piece UI: {e}")

    def edit_movement_range(self):
        """Open the movement range editor dialog."""
        try:
            result = edit_target_range(
                initial_pattern=self.movement_range_pattern,
                piece_position=self.movement_piece_position,
                title="Edit Movement Restriction Range",
                parent=self.editor,
                checkbox_states=self.movement_checkbox_states
            )

            if result[0] is not None:  # User didn't cancel
                self.movement_range_pattern, self.movement_piece_position, self.movement_checkbox_states = result
                self.log_debug("Movement range updated")

        except Exception as e:
            self.log_error(f"Error editing movement range: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating debuff piece data: {data}")
            
            # Populate target selector
            target_selector = self.get_widget_by_name("debuff_target_selector")
            if target_selector:
                debuff_targets = data.get("debuffTargets", data.get("debuff_targets", []))
                if debuff_targets:
                    target_selector.set_pieces(debuff_targets)
            
            # Populate duration
            duration_spin = self.get_widget_by_name("debuff_duration_spin")
            if duration_spin:
                duration = data.get("debuffDuration", 1)
                duration_spin.setValue(duration)
            
            # Populate debuff options
            debuff_options = data.get("debuffOptions", {})

            # Movement restriction
            movement_check = self.get_widget_by_name("debuff_movement_check")
            if movement_check:
                movement_check.setChecked(debuff_options.get("movement", False))

            # Movement range data
            if "debuffMovementRange" in data:
                self.movement_range_pattern = data["debuffMovementRange"]
            if "debuffMovementPosition" in data:
                self.movement_piece_position = data["debuffMovementPosition"]
            if "debuffMovementCheckboxes" in data:
                self.movement_checkbox_states = data["debuffMovementCheckboxes"]

            # Ability blocking
            ability_check = self.get_widget_by_name("debuff_ability_check")
            if ability_check:
                ability_check.setChecked(debuff_options.get("abilities", False))

            # Blocked abilities
            ability_selector = self.get_widget_by_name("debuff_ability_selector")
            if ability_selector and "debuffBlockedAbilities" in data:
                ability_selector.set_abilities(data["debuffBlockedAbilities"])
            
            self.log_debug("Debuff piece data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating debuff piece data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}
            
            # Collect target pieces
            target_selector = self.get_widget_by_name("debuff_target_selector")
            if target_selector:
                pieces = target_selector.get_pieces()
                if pieces:
                    data["debuffTargets"] = pieces
            
            # Collect duration
            duration_spin = self.get_widget_by_name("debuff_duration_spin")
            if duration_spin:
                data["debuffDuration"] = duration_spin.value()
            
            # Collect debuff options
            debuff_options = {}

            # Movement restriction
            movement_check = self.get_widget_by_name("debuff_movement_check")
            if movement_check and movement_check.isChecked():
                debuff_options["movement"] = True
                # Store movement range data
                data["debuffMovementRange"] = self.movement_range_pattern
                data["debuffMovementPosition"] = self.movement_piece_position
                data["debuffMovementCheckboxes"] = self.movement_checkbox_states

            # Ability blocking
            ability_check = self.get_widget_by_name("debuff_ability_check")
            if ability_check and ability_check.isChecked():
                debuff_options["abilities"] = True
                # Store blocked abilities
                ability_selector = self.get_widget_by_name("debuff_ability_selector")
                if ability_selector:
                    blocked_abilities = ability_selector.get_abilities()
                    if blocked_abilities:
                        data["debuffBlockedAbilities"] = blocked_abilities

            if debuff_options:
                data["debuffOptions"] = debuff_options
            
            self.log_debug(f"Collected debuff piece data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting debuff piece data: {e}")
            return {}
