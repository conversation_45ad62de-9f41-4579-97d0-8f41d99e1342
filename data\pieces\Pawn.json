{"version": "1.0.0", "name": "Pawn", "description": "Moves forward one square, captures diagonally, with special first move", "role": "Pawn", "can_castle": false, "track_starting_position": true, "color_directional": true, "can_capture": true, "movement": {"type": "Pawn", "pattern": [], "piece_position": [4, 4]}, "points": {"max_points": 0, "starting_points": 0}, "recharge": {"type": "None", "turns": 0}, "abilities": ["pawn_movement_2", "en_passant"], "promotions": ["Queen", "Rook", "<PERSON>", "<PERSON>"]}