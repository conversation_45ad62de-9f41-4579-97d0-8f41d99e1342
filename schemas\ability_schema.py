"""
Pydantic schema for Adventure Chess abilities
Defines the main ability container and routing logic
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import Field, field_validator, model_validator

from .base import (
    BaseAdventureChessModel,
    ActivationMode,
    Coordinate,
    RangeMask8x8,
    create_default_range_mask,
    validate_range_mask_8x8
)


class Ability(BaseAdventureChessModel):
    """Main ability container that can handle any tag type"""
    
    # Basic Information
    name: str = Field(..., min_length=1, max_length=50, description="Ability name")
    description: str = Field(default="", max_length=500, description="Ability description")
    cost: int = Field(default=0, ge=0, description="Point cost for using this ability")
    activation_mode: ActivationMode = Field(default=ActivationMode.AUTO, description="How the ability is activated")
    
    # Tags define which ability types this ability uses
    tags: List[str] = Field(default_factory=list, description="List of ability tags")
    
    # Range configuration (common to many abilities)
    range_mask: Optional[RangeMask8x8] = Field(default=None, description="8x8 range pattern")
    piece_position: Optional[List[int]] = Field(default=None, description="Piece position [row, col]")
    range_friendly_only: bool = Field(default=False, description="Range only affects friendly pieces")
    range_enemy_only: bool = Field(default=False, description="Range only affects enemy pieces")
    range_include_start: bool = Field(default=False, description="Include starting square in range")
    range_include_self: bool = Field(default=False, description="Include self in range")

    # Area Effect Configuration
    area_size: int = Field(default=1, description="Area effect size")
    area_shape: str = Field(default="square", description="Area effect shape")

    # Move Configuration
    move_distance: int = Field(default=1, description="Move distance")
    move_direction: str = Field(default="any", description="Move direction")

    # Summon Configuration
    summon_max: int = Field(default=1, description="Maximum summons")
    summon_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Summon piece list")

    # Revival Configuration
    revival_sacrifice: bool = Field(default=False, description="Sacrifice for revival")
    revival_max_cost: int = Field(default=0, description="Maximum revival cost")
    revival_with_points: bool = Field(default=False, description="Revival with points")
    revival_points: int = Field(default=0, description="Revival points")
    revival_starting: bool = Field(default=False, description="Revival at starting position")
    revival_within_turn: int = Field(default=0, description="Revival within turn limit")
    revival_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Revival target list")

    # Capture Configuration
    capture_target: str = Field(default="enemy", description="Capture target type")

    # Carry Piece Configuration
    carry_range: int = Field(default=0, description="Carry range (0=self)")
    carry_drop_on_death: bool = Field(default=False, description="Drop carried piece on death")
    carry_share_abilities: bool = Field(default=False, description="Share abilities with carried piece")
    carry_starting_piece: bool = Field(default=False, description="Carry starting piece")
    carry_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Carry target list")

    # Pass Through Configuration
    pass_through_capture: str = Field(default="none", description="Pass through capture type")
    pass_through_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Pass through piece list")

    # Adjacency Configuration
    adjacency_distance: int = Field(default=1, description="Adjacency distance")
    adjacency_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Adjacency piece list")
    adjacency_pattern: Optional[Dict[str, Any]] = Field(default=None, description="Adjacency tile pattern")

    # Line of Sight Configuration
    los_ignore_friendly: bool = Field(default=False, description="Ignore friendly pieces for LOS")
    los_ignore_enemy: bool = Field(default=False, description="Ignore enemy pieces for LOS")
    los_ignore_all: bool = Field(default=False, description="Ignore all pieces for LOS")
    prevent_los: bool = Field(default=False, description="Prevent line of sight")

    # No Turn Cost Configuration
    no_turn_cost_limit: int = Field(default=0, description="No turn cost limit")

    # Share Space Configuration
    share_space_max: int = Field(default=1, description="Maximum pieces sharing space")
    share_space_same_type: bool = Field(default=False, description="Only same type can share space")
    share_space_friendly: bool = Field(default=True, description="Allow friendly pieces to share space")
    share_space_enemy: bool = Field(default=False, description="Allow enemy pieces to share space")
    share_space_any: bool = Field(default=False, description="Allow any pieces to share space")

    # Delay Configuration
    delay_turn: bool = Field(default=False, description="Delay turn")
    delay_turn_amount: int = Field(default=0, description="Turn delay amount")
    delay_action: bool = Field(default=False, description="Delay action")
    delay_action_amount: int = Field(default=0, description="Action delay amount")

    # Displace Piece Configuration
    displace_direction: str = Field(default="any", description="Displace direction")
    displace_distance: int = Field(default=1, description="Displace distance")
    displace_target_type: str = Field(default="enemy", description="Displace target type")
    displace_custom: bool = Field(default=False, description="Use custom displace pattern")
    displace_target_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Displace target list")
    displace_custom_pattern: Optional[Dict[str, Any]] = Field(default=None, description="Custom displace pattern")

    # Immobilize Configuration
    immobilize_duration: int = Field(default=1, description="Immobilize duration")
    immobilize_duration_enabled: bool = Field(default=False, description="Enable immobilize duration")
    immobilize_target_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Immobilize target list")

    # Pulse Effect Configuration
    pulse_interval: int = Field(default=1, description="Pulse interval")
    pulse_enabled: bool = Field(default=False, description="Enable pulse effect")

    # Fog of War Configuration
    fog_vision_type: str = Field(default="reveal", description="Fog vision type")
    fog_radius: int = Field(default=1, description="Fog radius")
    fog_duration: int = Field(default=1, description="Fog duration")
    fog_cost: int = Field(default=0, description="Fog cost")
    fog_custom_range: Optional[Dict[str, Any]] = Field(default=None, description="Custom fog range")

    # Obstacle Configuration
    obstacle_type: str = Field(default="wall", description="Obstacle type")
    remove_obstacle_type: str = Field(default="wall", description="Remove obstacle type")

    # Duplicate Configuration
    duplicate_limit: bool = Field(default=False, description="Enable duplicate limit")
    duplicate_limit_amount: int = Field(default=1, description="Duplicate limit amount")
    duplicate_offset: Optional[Dict[str, Any]] = Field(default=None, description="Duplicate offset pattern")

    # Convert Piece Configuration
    convert_target_type: str = Field(default="enemy", description="Convert target type")
    convert_target_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Convert target list")

    # Reaction Configuration
    reaction_event: str = Field(default="capture", description="Reaction event")
    reaction_uses_action: bool = Field(default=False, description="Reaction uses action")
    reaction_targets: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Reaction target list")

    # Buff Piece Configuration
    buff_duration: int = Field(default=1, description="Buff duration")
    buff_add_ability: bool = Field(default=False, description="Buff adds ability")
    buff_adjust_movement_attack: bool = Field(default=False, description="Buff adjusts movement/attack")
    buff_target_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Buff target list")
    buff_abilities: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Buff ability list")
    buff_movement_attack_data: Optional[Dict[str, Any]] = Field(default=None, description="Buff movement/attack data")

    # Debuff Piece Configuration
    debuff_duration: int = Field(default=1, description="Debuff duration")
    debuff_prevent_ability: bool = Field(default=False, description="Debuff prevents ability")
    debuff_prevent_los: bool = Field(default=False, description="Debuff prevents LOS")
    debuff_adjust_movement_attack: bool = Field(default=False, description="Debuff adjusts movement/attack")
    debuff_target_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Debuff target list")
    debuff_prevent_abilities: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Debuff prevent ability list")
    debuff_movement_attack_data: Optional[Dict[str, Any]] = Field(default=None, description="Debuff movement/attack data")

    # Invisible Configuration
    invisible_reveal_move: bool = Field(default=False, description="Reveal on move")
    invisible_reveal_move_amount: int = Field(default=0, description="Reveal on move amount")
    invisible_reveal_capture: bool = Field(default=False, description="Reveal on capture")
    invisible_reveal_capture_amount: int = Field(default=0, description="Reveal on capture amount")
    invisible_reveal_action: bool = Field(default=False, description="Reveal on action")
    invisible_reveal_action_amount: int = Field(default=0, description="Reveal on action amount")
    invisible_reveal_los: bool = Field(default=False, description="Reveal on LOS")

    # Trap Tile Configuration
    trap_capture: bool = Field(default=False, description="Trap captures")
    trap_immobilize: bool = Field(default=False, description="Trap immobilizes")
    trap_immobilize_amount: int = Field(default=0, description="Trap immobilize amount")
    trap_teleport: bool = Field(default=False, description="Trap teleports")
    trap_add_ability: bool = Field(default=False, description="Trap adds ability")
    trap_ability: str = Field(default="", description="Trap ability")
    trap_teleport_pattern: Optional[Dict[str, Any]] = Field(default=None, description="Trap teleport pattern")

    # Swap Places Configuration
    swap_list: List[Union[str, Dict[str, Any]]] = Field(default_factory=list, description="Swap target list")

    # Auto Cost Configuration (from editor)
    auto_cost_check: Optional[bool] = Field(default=None, description="Auto calculate cost from complexity")

    # Missing fields found in editor but not in schema
    carry_starting_piece_check: Optional[bool] = Field(default=None, description="Carry starting piece checkbox")

    # Dynamic fields for tag-specific data (for backward compatibility)
    extra_fields: Dict[str, Any] = Field(default_factory=dict, description="Additional tag-specific configuration data")
    
    # Dialog state persistence (for UI)
    dialog_states: Optional[Dict[str, Any]] = Field(default=None, alias="_dialogStates", description="UI dialog states")

    # Range/Pattern editor checkbox states
    range_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="rangeCheckboxStates", description="Range editor checkbox states")
    buff_pattern_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="buffPatternCheckboxStates", description="Buff pattern editor checkbox states")
    debuff_pattern_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="debuffPatternCheckboxStates", description="Debuff pattern editor checkbox states")
    carry_range_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="carryRangeCheckboxStates", description="Carry range editor checkbox states")
    carry_drop_range_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="carryDropRangeCheckboxStates", description="Carry drop range editor checkbox states")
    pass_through_range_checkbox_states: Optional[Dict[str, Any]] = Field(default=None, alias="passThroughRangeCheckboxStates", description="Pass through range editor checkbox states")


    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate ability name"""
        if not v or not v.strip():
            raise ValueError("Ability name cannot be empty")
        return v.strip()

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Validate and normalize tags - PERMISSIVE MODE for maximum flexibility"""
        if not isinstance(v, list):
            v = []  # Convert to empty list instead of raising error

        # Remove duplicates while preserving order
        seen = set()
        unique_tags = []
        for tag in v:
            if tag not in seen:
                seen.add(tag)
                unique_tags.append(tag)

        return unique_tags

    @field_validator('range_mask')
    @classmethod
    def validate_range_mask(cls, v):
        """Validate range mask"""
        if v is not None:
            return validate_range_mask_8x8(v)
        return v

    @field_validator('piece_position')
    @classmethod
    def validate_piece_position(cls, v):
        """Validate piece position coordinates"""
        if v is not None:
            if not isinstance(v, list) or len(v) != 2:
                raise ValueError("Piece position must be a list of [row, col]")
            if not all(isinstance(x, int) and 0 <= x <= 7 for x in v):
                raise ValueError("Piece position coordinates must be integers between 0 and 7")
        return v

    @model_validator(mode='after')
    def validate_range_exclusivity(self):
        """Ensure range_friendly_only and range_enemy_only are mutually exclusive"""
        if self.range_friendly_only and self.range_enemy_only:
            raise ValueError("range_friendly_only and range_enemy_only cannot both be True")
        return self
    
    def get_tag_data(self, tag: str) -> Dict[str, Any]:
        """Get data specific to a particular tag"""
        from .ability_tags import get_tag_model

        if tag not in self.tags:
            return {}

        model_class = get_tag_model(tag)
        if model_class is None:
            return {}

        # Extract fields that belong to this tag from extra_fields
        tag_data = {}
        model_fields = model_class.model_fields.keys()

        for field in model_fields:
            # Convert field names to match the format used in extra_fields
            # Handle both snake_case and camelCase variants
            field_variants = [
                field,
                self._snake_to_camel(field),
                self._camel_to_snake(field)
            ]

            for variant in field_variants:
                if variant in self.extra_fields:
                    tag_data[field] = self.extra_fields[variant]
                    break

        return tag_data
    
    def set_tag_data(self, tag: str, data: Dict[str, Any]):
        """Set data specific to a particular tag - PERMISSIVE MODE"""
        # Skip validation - allow any tag data for maximum flexibility
        # Update extra_fields with the tag-specific data
        for key, value in data.items():
            self.extra_fields[key] = value
    
    def has_tag(self, tag: str) -> bool:
        """Check if ability has a specific tag"""
        return tag in self.tags
    
    def add_tag(self, tag: str):
        """Add a tag to the ability"""
        # Import here to avoid circular imports
        from config import get_canonical_ability_tags
        canonical_tags = get_canonical_ability_tags()
        
        if tag not in canonical_tags:
            raise ValueError(f"Invalid ability tag: {tag}")
        
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str):
        """Remove a tag from the ability and clean up its data"""
        if tag in self.tags:
            self.tags.remove(tag)

            # Clean up tag-specific data from extra_fields
            from .ability_tags import get_tag_model

            model_class = get_tag_model(tag)
            if model_class is not None:
                model_fields = model_class.model_fields.keys()
                for field in model_fields:
                    # Remove both snake_case and camelCase variants
                    field_variants = [
                        field,
                        self._snake_to_camel(field),
                        self._camel_to_snake(field)
                    ]
                    for variant in field_variants:
                        self.extra_fields.pop(variant, None)
    
    def to_legacy_dict(self) -> Dict[str, Any]:
        """Convert to legacy dictionary format for backward compatibility"""
        data = self.model_dump(exclude_unset=True, by_alias=True)
        
        # Flatten extra_fields into the main dictionary
        if 'extra_fields' in data:
            extra = data.pop('extra_fields')
            data.update(extra)
        
        # Convert snake_case to camelCase for legacy compatibility
        legacy_mappings = {
            'activation_mode': 'activationMode',
            'range_mask': 'rangeMask',
            'piece_position': 'piecePosition',
            'range_friendly_only': 'rangeFriendlyOnly',
            'range_enemy_only': 'rangeEnemyOnly',
            'range_include_start': 'rangeIncludeStart',
            'range_include_self': 'rangeIncludeSelf',
            'area_size': 'areaSize',
            'area_shape': 'areaShape',
            'move_distance': 'moveDistance',
            'move_direction': 'moveDirection',
            'summon_max': 'summonMax',
            'summon_list': 'summonList',
            'revival_sacrifice': 'revivalSacrifice',
            'revival_max_cost': 'revivalMaxCost',
            'revival_with_points': 'revivalWithPoints',
            'revival_points': 'revivalPoints',
            'revival_starting': 'revivalStarting',
            'revival_within_turn': 'revivalWithinTurn',
            'revival_list': 'revivalList',
            'capture_target': 'captureTarget',
            'carry_range': 'carryRange',
            'carry_drop_on_death': 'carryDropOnDeath',
            'carry_share_abilities': 'carryShareAbilities',
            'carry_starting_piece': 'carryStartingPiece',
            'carry_list': 'carryList',
            'pass_through_capture': 'passThroughCapture',
            'pass_through_list': 'passThroughList',
            'adjacency_distance': 'adjacencyDistance',
            'adjacency_list': 'adjacencyList',
            'adjacency_pattern': 'adjacencyPattern',
            'los_ignore_friendly': 'losIgnoreFriendly',
            'los_ignore_enemy': 'losIgnoreEnemy',
            'los_ignore_all': 'losIgnoreAll',
            'prevent_los': 'preventLos',
            'no_turn_cost_limit': 'noTurnCostLimit',
            'share_space_max': 'shareSpaceMax',
            'share_space_same_type': 'shareSpaceSameType',
            'delay_turn': 'delayTurn',
            'delay_turn_amount': 'delayTurnAmount',
            'delay_action': 'delayAction',
            'delay_action_amount': 'delayActionAmount',
            'displace_direction': 'displaceDirection',
            'displace_distance': 'displaceDistance',
            'displace_target_type': 'displaceTargetType',
            'displace_custom': 'displaceCustom',
            'displace_target_list': 'displaceTargetList',
            'displace_custom_pattern': 'displaceCustomPattern',
            'immobilize_duration': 'immobilizeDuration',
            'immobilize_duration_enabled': 'immobilizeDurationEnabled',
            'immobilize_target_list': 'immobilizeTargetList',
            'pulse_interval': 'pulseInterval',
            'pulse_enabled': 'pulseEnabled',
            'fog_vision_type': 'fogVisionType',
            'fog_radius': 'fogRadius',
            'fog_duration': 'fogDuration',
            'fog_cost': 'fogCost',
            'fog_custom_range': 'fogCustomRange',
            'obstacle_type': 'obstacleType',
            'remove_obstacle_type': 'removeObstacleType',
            'duplicate_limit': 'duplicateLimit',
            'duplicate_limit_amount': 'duplicateLimitAmount',
            'duplicate_offset': 'duplicateOffset',
            'convert_target_type': 'convertTargetType',
            'convert_target_list': 'convertTargetList',
            'reaction_event': 'reactionEvent',
            'reaction_uses_action': 'reactionUsesAction',
            'reaction_targets': 'reactionTargets',
            'buff_duration': 'buffDuration',
            'buff_add_ability': 'buffAddAbility',
            'buff_adjust_movement_attack': 'buffAdjustMovementAttack',
            'buff_target_list': 'buffTargetList',
            'buff_abilities': 'buffAbilities',
            'buff_movement_attack_data': 'buffMovementAttackData',
            'debuff_duration': 'debuffDuration',
            'debuff_prevent_ability': 'debuffPreventAbility',
            'debuff_prevent_los': 'debuffPreventLos',
            'debuff_adjust_movement_attack': 'debuffAdjustMovementAttack',
            'debuff_target_list': 'debuffTargetList',
            'debuff_prevent_abilities': 'debuffPreventAbilities',
            'debuff_movement_attack_data': 'debuffMovementAttackData',
            'invisible_reveal_move': 'invisibleRevealMove',
            'invisible_reveal_move_amount': 'invisibleRevealMoveAmount',
            'invisible_reveal_capture': 'invisibleRevealCapture',
            'invisible_reveal_capture_amount': 'invisibleRevealCaptureAmount',
            'invisible_reveal_action': 'invisibleRevealAction',
            'invisible_reveal_action_amount': 'invisibleRevealActionAmount',
            'invisible_reveal_los': 'invisibleRevealLos',
            'trap_capture': 'trapCapture',
            'trap_immobilize': 'trapImmobilize',
            'trap_immobilize_amount': 'trapImmobilizeAmount',
            'trap_teleport': 'trapTeleport',
            'trap_add_ability': 'trapAddAbility',
            'trap_ability': 'trapAbility',
            'trap_teleport_pattern': 'trapTeleportPattern',
            'swap_list': 'swapList',
            'auto_cost_check': 'autoCostCheck',
            'carry_starting_piece_check': 'carryStartingPieceCheck'
        }
        
        for new_key, legacy_key in legacy_mappings.items():
            if new_key in data:
                data[legacy_key] = data.pop(new_key)
        
        return data
    
    @classmethod
    def from_legacy_dict(cls, data: Dict[str, Any]) -> 'Ability':
        """Create ability from legacy dictionary format"""
        # Create a copy to avoid modifying original
        normalized_data = data.copy()
        
        # Convert legacy field names to new schema
        legacy_mappings = {
            'activationMode': 'activation_mode',
            'rangeMask': 'range_mask',
            'piecePosition': 'piece_position',
            'rangeFriendlyOnly': 'range_friendly_only',
            'rangeEnemyOnly': 'range_enemy_only',
            'rangeIncludeStart': 'range_include_start',
            'rangeIncludeSelf': 'range_include_self',
            'areaSize': 'area_size',
            'areaShape': 'area_shape',
            'moveDistance': 'move_distance',
            'moveDirection': 'move_direction',
            'summonMax': 'summon_max',
            'summonList': 'summon_list',
            'revivalSacrifice': 'revival_sacrifice',
            'revivalMaxCost': 'revival_max_cost',
            'revivalWithPoints': 'revival_with_points',
            'revivalPoints': 'revival_points',
            'revivalStarting': 'revival_starting',
            'revivalWithinTurn': 'revival_within_turn',
            'revivalList': 'revival_list',
            'captureTarget': 'capture_target',
            'carryRange': 'carry_range',
            'carryDropOnDeath': 'carry_drop_on_death',
            'carryShareAbilities': 'carry_share_abilities',
            'carryStartingPiece': 'carry_starting_piece',
            'carryList': 'carry_list',
            'passThroughCapture': 'pass_through_capture',
            'passThroughList': 'pass_through_list',
            'adjacencyDistance': 'adjacency_distance',
            'adjacencyList': 'adjacency_list',
            'adjacencyPattern': 'adjacency_pattern',
            'losIgnoreFriendly': 'los_ignore_friendly',
            'losIgnoreEnemy': 'los_ignore_enemy',
            'losIgnoreAll': 'los_ignore_all',
            'preventLos': 'prevent_los',
            'noTurnCostLimit': 'no_turn_cost_limit',
            'shareSpaceMax': 'share_space_max',
            'shareSpaceSameType': 'share_space_same_type',
            'delayTurn': 'delay_turn',
            'delayTurnAmount': 'delay_turn_amount',
            'delayAction': 'delay_action',
            'delayActionAmount': 'delay_action_amount',
            'displaceDirection': 'displace_direction',
            'displaceDistance': 'displace_distance',
            'displaceTargetType': 'displace_target_type',
            'displaceCustom': 'displace_custom',
            'displaceTargetList': 'displace_target_list',
            'displaceCustomPattern': 'displace_custom_pattern',
            'immobilizeDuration': 'immobilize_duration',
            'immobilizeDurationEnabled': 'immobilize_duration_enabled',
            'immobilizeTargetList': 'immobilize_target_list',
            'pulseInterval': 'pulse_interval',
            'pulseEnabled': 'pulse_enabled',
            'fogVisionType': 'fog_vision_type',
            'fogRadius': 'fog_radius',
            'fogDuration': 'fog_duration',
            'fogCost': 'fog_cost',
            'fogCustomRange': 'fog_custom_range',
            'obstacleType': 'obstacle_type',
            'removeObstacleType': 'remove_obstacle_type',
            'duplicateLimit': 'duplicate_limit',
            'duplicateLimitAmount': 'duplicate_limit_amount',
            'duplicateOffset': 'duplicate_offset',
            'convertTargetType': 'convert_target_type',
            'convertTargetList': 'convert_target_list',
            'reactionEvent': 'reaction_event',
            'reactionUsesAction': 'reaction_uses_action',
            'reactionTargets': 'reaction_targets',
            'buffDuration': 'buff_duration',
            'buffAddAbility': 'buff_add_ability',
            'buffAdjustMovementAttack': 'buff_adjust_movement_attack',
            'buffTargetList': 'buff_target_list',
            'buffAbilities': 'buff_abilities',
            'buffMovementAttackData': 'buff_movement_attack_data',
            'debuffDuration': 'debuff_duration',
            'debuffPreventAbility': 'debuff_prevent_ability',
            'debuffPreventLos': 'debuff_prevent_los',
            'debuffAdjustMovementAttack': 'debuff_adjust_movement_attack',
            'debuffTargetList': 'debuff_target_list',
            'debuffPreventAbilities': 'debuff_prevent_abilities',
            'debuffMovementAttackData': 'debuff_movement_attack_data',
            'invisibleRevealMove': 'invisible_reveal_move',
            'invisibleRevealMoveAmount': 'invisible_reveal_move_amount',
            'invisibleRevealCapture': 'invisible_reveal_capture',
            'invisibleRevealCaptureAmount': 'invisible_reveal_capture_amount',
            'invisibleRevealAction': 'invisible_reveal_action',
            'invisibleRevealActionAmount': 'invisible_reveal_action_amount',
            'invisibleRevealLos': 'invisible_reveal_los',
            'trapCapture': 'trap_capture',
            'trapImmobilize': 'trap_immobilize',
            'trapImmobilizeAmount': 'trap_immobilize_amount',
            'trapTeleport': 'trap_teleport',
            'trapAddAbility': 'trap_add_ability',
            'trapAbility': 'trap_ability',
            'trapTeleportPattern': 'trap_teleport_pattern',
            'swapList': 'swap_list',
            'autoCostCheck': 'auto_cost_check',
            'carryStartingPieceCheck': 'carry_starting_piece_check'
        }
        
        for legacy_key, new_key in legacy_mappings.items():
            if legacy_key in normalized_data:
                normalized_data[new_key] = normalized_data.pop(legacy_key)
        
        # Extract known fields and put the rest in extra_fields
        known_fields = {
            'version', 'name', 'description', 'cost', 'activation_mode', 'tags',
            'range_mask', 'piece_position', 'range_friendly_only', 'range_enemy_only',
            'range_include_start', 'range_include_self',
            'area_size', 'area_shape', 'move_distance', 'move_direction',
            'summon_max', 'summon_list', 'revival_sacrifice', 'revival_max_cost',
            'revival_with_points', 'revival_points', 'revival_starting', 'revival_within_turn',
            'revival_list', 'capture_target', 'carry_range', 'carry_drop_on_death',
            'carry_share_abilities', 'carry_starting_piece', 'carry_list',
            'pass_through_capture', 'pass_through_list', 'adjacency_distance',
            'adjacency_list', 'adjacency_pattern', 'los_ignore_friendly',
            'los_ignore_enemy', 'los_ignore_all', 'prevent_los', 'no_turn_cost_limit',
            'share_space_max', 'share_space_same_type', 'delay_turn', 'delay_turn_amount',
            'delay_action', 'delay_action_amount', 'displace_direction', 'displace_distance',
            'displace_target_type', 'displace_custom', 'displace_target_list',
            'displace_custom_pattern', 'immobilize_duration', 'immobilize_duration_enabled',
            'immobilize_target_list', 'pulse_interval', 'pulse_enabled',
            'fog_vision_type', 'fog_radius', 'fog_duration', 'fog_cost', 'fog_custom_range',
            'obstacle_type', 'remove_obstacle_type', 'duplicate_limit', 'duplicate_limit_amount',
            'duplicate_offset', 'convert_target_type', 'convert_target_list',
            'reaction_event', 'reaction_uses_action', 'reaction_targets',
            'buff_duration', 'buff_add_ability', 'buff_adjust_movement_attack',
            'buff_target_list', 'buff_abilities', 'buff_movement_attack_data',
            'debuff_duration', 'debuff_prevent_ability', 'debuff_prevent_los',
            'debuff_adjust_movement_attack', 'debuff_target_list', 'debuff_prevent_abilities',
            'debuff_movement_attack_data', 'invisible_reveal_move', 'invisible_reveal_move_amount',
            'invisible_reveal_capture', 'invisible_reveal_capture_amount',
            'invisible_reveal_action', 'invisible_reveal_action_amount', 'invisible_reveal_los',
            'trap_capture', 'trap_immobilize', 'trap_immobilize_amount', 'trap_teleport',
            'trap_add_ability', 'trap_ability', 'trap_teleport_pattern', 'swap_list',
            'auto_cost_check', 'carry_starting_piece_check',
            'dialog_states', '_dialogStates', 'range_checkbox_states', 'rangeCheckboxStates',
            'buff_pattern_checkbox_states', 'buffPatternCheckboxStates',
            'debuff_pattern_checkbox_states', 'debuffPatternCheckboxStates',
            'carry_range_checkbox_states', 'carryRangeCheckboxStates',
            'carry_drop_range_checkbox_states', 'carryDropRangeCheckboxStates',
            'pass_through_range_checkbox_states', 'passThroughRangeCheckboxStates',
            'extra_fields'
        }
        
        extra_fields = {}
        main_fields = {}
        
        for key, value in normalized_data.items():
            if key in known_fields:
                main_fields[key] = value
            else:
                extra_fields[key] = value
        
        if extra_fields:
            main_fields['extra_fields'] = extra_fields
        
        return cls(**main_fields)

    def _snake_to_camel(self, snake_str: str) -> str:
        """Convert snake_case to camelCase"""
        components = snake_str.split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])

    def _camel_to_snake(self, camel_str: str) -> str:
        """Convert camelCase to snake_case"""
        import re
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def validate_all_tags(self) -> Dict[str, Any]:
        """Validate all tag data - PERMISSIVE MODE (no validation)"""
        # Return empty errors dict for maximum flexibility
        return {}

    def get_all_tag_data(self) -> Dict[str, Dict[str, Any]]:
        """Get data for all tags in a structured format"""
        all_data = {}

        for tag in self.tags:
            tag_data = self.get_tag_data(tag)
            if tag_data:
                all_data[tag] = tag_data

        return all_data

    def set_all_tag_data(self, tag_data_dict: Dict[str, Dict[str, Any]]):
        """Set data for multiple tags at once"""
        for tag, data in tag_data_dict.items():
            if tag in self.tags:
                self.set_tag_data(tag, data)

    def get_tag_defaults(self, tag: str) -> Dict[str, Any]:
        """Get default values for a specific tag"""
        from .ability_tags import get_tag_model

        model_class = get_tag_model(tag)
        if model_class is None:
            return {}

        # Create a default instance and get its values
        default_instance = model_class()
        return default_instance.model_dump(exclude_unset=True)
