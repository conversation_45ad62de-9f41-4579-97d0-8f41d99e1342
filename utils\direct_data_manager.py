#!/usr/bin/env python3
"""
Direct Data Manager for Adventure Chess
Simple, direct JSON save/load without legacy migration or validation overhead
"""

import json
import os
import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

from config import PIECES_DIR, ABILITIES_DIR

logger = logging.getLogger(__name__)


class DirectDataManager:
    """
    Direct data manager that saves and loads JSON without migration or validation overhead
    """

    # Field mapping for legacy format conversion
    LEGACY_TO_CAMEL_CASE = {
        'can_castle': 'canCastle',
        'track_starting_position': 'trackStartingPosition',
        'color_directional': 'colorDirectional',
        'can_capture': 'canCapture',
        'black_icon': 'blackIcon',
        'white_icon': 'whiteIcon',
        'max_points': 'maxPoints',
        'starting_points': 'startingPoints',
        'recharge_type': 'rechargeType',
        'turn_points': 'turnPoints',
        'adjacency_recharge_config': 'adjacencyRechargeConfig',
        'committed_recharge_turns': 'committedRechargeTurns',
        'enable_recharge': 'enableRecharge'
    }

    CAMEL_CASE_TO_LEGACY = {v: k for k, v in LEGACY_TO_CAMEL_CASE.items()}

    # Role mapping for legacy compatibility
    LEGACY_ROLE_MAPPING = {
        'King': 'Commander',
        'Soldier': 'Supporter',
        'Warrior': 'Supporter',
        'Support': 'Supporter',
        'Specialist': 'Supporter'
    }

    UI_TO_LEGACY_ROLE_MAPPING = {
        'Commander': 'Commander',  # Keep Commander as is
        'Supporter': 'Supporter'   # Keep Supporter as is
    }

    @staticmethod
    def _convert_legacy_to_ui_format(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert legacy snake_case format to camelCase format expected by UI

        Args:
            data: Dictionary with legacy field names

        Returns:
            Dictionary with camelCase field names
        """
        converted = data.copy()

        # Convert top-level fields
        for legacy_key, camel_key in DirectDataManager.LEGACY_TO_CAMEL_CASE.items():
            if legacy_key in converted:
                converted[camel_key] = converted.pop(legacy_key)

        # Convert legacy role names to current UI role names
        if 'role' in converted:
            legacy_role = converted['role']
            if legacy_role in DirectDataManager.LEGACY_ROLE_MAPPING:
                converted['role'] = DirectDataManager.LEGACY_ROLE_MAPPING[legacy_role]

        # Handle recharge system conversion
        if 'recharge' in converted:
            recharge = converted['recharge']
            recharge_type = recharge.get('type', 'None')

            # Convert old recharge format to new consolidated format
            if recharge_type == 'None':
                converted['enableRecharge'] = False
                converted['rechargeType'] = 'turnRecharge'  # Default
                converted['maxPoints'] = 0
                converted['startingPoints'] = 0
                converted['turnPoints'] = 0
                converted['adjacencyRechargeConfig'] = []
                converted['committedRechargeTurns'] = 0
            else:
                converted['enableRecharge'] = True
                if recharge_type == 'Turn':
                    converted['rechargeType'] = 'turnRecharge'
                    converted['turnPoints'] = recharge.get('turns', 0)
                    converted['maxPoints'] = recharge.get('turns', 0)  # Assume max = turns for legacy
                    converted['startingPoints'] = recharge.get('turns', 0)  # Assume starting = turns for legacy
                elif recharge_type == 'Adjacency':
                    converted['rechargeType'] = 'adjacencyRecharge'
                    converted['adjacencyRechargeConfig'] = recharge.get('config', [])
                elif recharge_type == 'Committed':
                    converted['rechargeType'] = 'committedRecharge'
                    converted['committedRechargeTurns'] = recharge.get('turns', 0)

                # Set defaults for unused recharge types
                if 'turnPoints' not in converted:
                    converted['turnPoints'] = 0
                if 'adjacencyRechargeConfig' not in converted:
                    converted['adjacencyRechargeConfig'] = []
                if 'committedRechargeTurns' not in converted:
                    converted['committedRechargeTurns'] = 0
                if 'maxPoints' not in converted:
                    converted['maxPoints'] = 0
                if 'startingPoints' not in converted:
                    converted['startingPoints'] = 0

            # Remove old recharge structure
            del converted['recharge']

        return converted

    @staticmethod
    def _convert_ui_to_legacy_format(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert camelCase UI format to legacy snake_case format for file storage

        Args:
            data: Dictionary with camelCase field names

        Returns:
            Dictionary with legacy field names
        """
        converted = data.copy()

        # Convert camelCase fields back to snake_case
        for camel_key, legacy_key in DirectDataManager.CAMEL_CASE_TO_LEGACY.items():
            if camel_key in converted:
                converted[legacy_key] = converted.pop(camel_key)

        # Handle recharge system conversion back to legacy format
        enable_recharge = converted.get('enable_recharge', False)
        recharge_type = converted.get('recharge_type', 'turnRecharge')

        if not enable_recharge:
            converted['recharge'] = {
                'type': 'None',
                'turns': 0
            }
        else:
            if recharge_type == 'turnRecharge':
                converted['recharge'] = {
                    'type': 'Turn',
                    'turns': converted.get('turn_points', 0)
                }
            elif recharge_type == 'adjacencyRecharge':
                converted['recharge'] = {
                    'type': 'Adjacency',
                    'config': converted.get('adjacency_recharge_config', [])
                }
            elif recharge_type == 'committedRecharge':
                converted['recharge'] = {
                    'type': 'Committed',
                    'turns': converted.get('committed_recharge_turns', 0)
                }

        # Remove new recharge fields (use snake_case names after conversion)
        for field in ['enable_recharge', 'recharge_type', 'max_points', 'starting_points',
                     'turn_points', 'adjacency_recharge_config', 'committed_recharge_turns']:
            converted.pop(field, None)

        return converted
    
    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece data directly to JSON file
        
        Args:
            piece_data: Dictionary containing piece data
            filename: Optional filename (without .json extension)
        
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = piece_data.get('name', 'unnamed_piece')
            
            # Clean filename
            filename = DirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(PIECES_DIR, filename)
            
            # Ensure directory exists
            os.makedirs(PIECES_DIR, exist_ok=True)
            
            # Convert UI format to legacy format for file storage
            legacy_data = DirectDataManager._convert_ui_to_legacy_format(piece_data)

            # Add version if not present
            if 'version' not in legacy_data:
                legacy_data['version'] = '1.0.0'

            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(legacy_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Piece saved successfully: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to save piece: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load piece data directly from JSON file
        
        Args:
            filename: Filename (with or without .json extension)
        
        Returns:
            Tuple of (piece_data, error_message)
        """
        try:
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(PIECES_DIR, filename)
            
            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"Piece file not found: {filename}"
                logger.error(error_msg)
                return None, error_msg
            
            # Load from file
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_piece_data = json.load(f)

            # Convert legacy format to UI format
            piece_data = DirectDataManager._convert_legacy_to_ui_format(raw_piece_data)

            logger.info(f"Piece loaded successfully: {file_path}")
            return piece_data, None
            
        except Exception as e:
            error_msg = f"Failed to load piece: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data directly to JSON file
        
        Args:
            ability_data: Dictionary containing ability data
            filename: Optional filename (without .json extension)
        
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = ability_data.get('name', 'unnamed_ability')
            
            # Clean filename
            filename = DirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            # Ensure directory exists
            os.makedirs(ABILITIES_DIR, exist_ok=True)
            
            # Add version if not present
            if 'version' not in ability_data:
                ability_data['version'] = '1.0.0'
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(ability_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Ability saved successfully: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to save ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load ability data directly from JSON file
        
        Args:
            filename: Filename (with or without .json extension)
        
        Returns:
            Tuple of (ability_data, error_message)
        """
        try:
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"Ability file not found: {filename}"
                logger.error(error_msg)
                return None, error_msg
            
            # Load from file
            with open(file_path, 'r', encoding='utf-8') as f:
                ability_data = json.load(f)
            
            logger.info(f"Ability loaded successfully: {file_path}")
            return ability_data, None
            
        except Exception as e:
            error_msg = f"Failed to load ability: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    @staticmethod
    def list_pieces() -> List[str]:
        """Get list of available piece files (without .json extension)"""
        try:
            if not os.path.exists(PIECES_DIR):
                return []
            
            pieces = []
            for filename in os.listdir(PIECES_DIR):
                if filename.endswith('.json'):
                    pieces.append(filename[:-5])  # Remove .json extension
            
            return sorted(pieces)
            
        except Exception as e:
            logger.error(f"Failed to list pieces: {str(e)}")
            return []
    
    @staticmethod
    def list_abilities() -> List[str]:
        """Get list of available ability files (without .json extension)"""
        try:
            if not os.path.exists(ABILITIES_DIR):
                return []
            
            abilities = []
            for filename in os.listdir(ABILITIES_DIR):
                if filename.endswith('.json'):
                    abilities.append(filename[:-5])  # Remove .json extension
            
            return sorted(abilities)
            
        except Exception as e:
            logger.error(f"Failed to list abilities: {str(e)}")
            return []
    
    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete a piece file"""
        try:
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = os.path.join(PIECES_DIR, filename)
            
            if not os.path.exists(file_path):
                return False, f"Piece file not found: {filename}"
            
            os.remove(file_path)
            logger.info(f"Piece deleted: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to delete piece: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete an ability file"""
        try:
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            if not os.path.exists(file_path):
                return False, f"Ability file not found: {filename}"
            
            os.remove(file_path)
            logger.info(f"Ability deleted: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to delete ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _clean_filename(filename: str) -> str:
        """Clean filename for safe file system usage"""
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')
        
        # Ensure not empty
        if not filename:
            filename = 'unnamed'
        
        return filename
