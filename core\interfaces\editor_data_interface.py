"""
Unified Data Interface for Adventure Chess Editors
Provides standardized data collection, UI population, and state management
"""

import logging
from typing import Dict, List, Any, Optional, Union
from PyQt6.QtWidgets import QCheckBox, QSpinBox, QComboBox, QLineEdit, QTextEdit, QMainWindow

logger = logging.getLogger(__name__)


class EditorDataInterface:
    """
    Universal data interface for all Adventure Chess editors
    Eliminates duplicate data handling patterns and provides consistent API
    """
    
    # Field mapping configurations for different editor types
    ABILITY_FIELD_MAPPINGS = [
        # Basic fields
        ('name_edit', 'name', 'text'),
        ('description_edit', 'description', 'plainText'),
        ('cost_spin', 'cost', 'value'),
        ('auto_cost_check', 'autoCostCheck', 'checked'),
        ('activation_combo', 'activationMode', 'currentText'),

        # Range configuration
        ('range_friendly_only_check', 'rangeFriendlyOnly', 'checked'),
        ('range_enemy_only_check', 'rangeEnemyOnly', 'checked'),
        ('range_include_start_check', 'rangeIncludeStart', 'checked'),
        ('range_include_self_check', 'rangeIncludeSelf', 'checked'),

        # Area effect configuration
        ('area_size_spin', 'areaSize', 'value'),
        ('area_shape_combo', 'areaShape', 'currentText'),

        # Summon configuration
        ('summon_max_spin', 'summonMax', 'value'),

        # Capture configuration
        ('capture_target_combo', 'captureTarget', 'currentText'),

        # Adjacency configuration
        ('adjacency_distance_spin', 'adjacencyDistance', 'value'),

        # No turn cost configuration
        ('no_turn_cost_limit_spin', 'noTurnCostLimit', 'value'),

        # Share space configuration
        ('share_space_max_spin', 'shareSpaceMax', 'value'),
        ('share_space_same_type_check', 'shareSpaceSameType', 'checked'),
        ('share_space_friendly_check', 'shareSpaceFriendly', 'checked'),
        ('share_space_enemy_check', 'shareSpaceEnemy', 'checked'),
        ('share_space_any_check', 'shareSpaceAny', 'checked'),

        # Displace configuration
        ('displace_direction_combo', 'displaceDirection', 'currentText'),
        ('displace_distance_spin', 'displaceDistance', 'value'),
        ('displace_custom_check', 'displaceCustom', 'checked'),

        # Immobilize configuration
        ('immobilize_duration_check', 'immobilizeDurationEnabled', 'checked'),
        ('immobilize_duration_spin', 'immobilizeDuration', 'value'),

        # Obstacle configuration
        ('obstacle_type_combo', 'obstacleType', 'currentText'),
        ('remove_obstacle_type_combo', 'removeObstacleType', 'currentText'),

        # Duplicate configuration
        ('duplicate_limit_check', 'duplicateLimitEnabled', 'checked'),
        ('duplicate_limit_spin', 'duplicateLimit', 'value'),

        # Reaction configuration
        ('reaction_uses_action_check', 'reactionUsesAction', 'checked'),

        # Buff configuration
        ('buff_duration_spin', 'buffDuration', 'value'),
        ('buff_add_ability_check', 'buffAddAbility', 'checked'),
        ('buff_movement_pattern_check', 'buffMovementPattern', 'checked'),

        # Debuff configuration
        ('debuff_duration_spin', 'debuffDuration', 'value'),
        ('debuff_prevent_ability_check', 'debuffPreventAbility', 'checked'),
        ('debuff_prevent_los_check', 'debuffPreventLos', 'checked'),
        ('debuff_movement_pattern_check', 'debuffMovementPattern', 'checked'),

        # Invisible configuration
        ('invisible_reveal_move_check', 'invisibleRevealMoveEnabled', 'checked'),
        ('invisible_reveal_move_spin', 'invisibleRevealMove', 'value'),
        ('invisible_reveal_capture_check', 'invisibleRevealCaptureEnabled', 'checked'),
        ('invisible_reveal_capture_spin', 'invisibleRevealCapture', 'value'),
        ('invisible_reveal_action_check', 'invisibleRevealActionEnabled', 'checked'),
        ('invisible_reveal_action_spin', 'invisibleRevealAction', 'value'),
        ('invisible_reveal_los_check', 'invisibleRevealLos', 'checked'),

        # Trap configuration
        ('trap_capture_check', 'trapCapture', 'checked'),
        ('trap_immobilize_check', 'trapImmobilizeEnabled', 'checked'),
        ('trap_immobilize_spin', 'trapImmobilize', 'value'),
        ('trap_teleport_check', 'trapTeleport', 'checked'),
        ('trap_add_ability_check', 'trapAddAbility', 'checked'),

        # Revival configuration
        ('revival_max_pieces_spin', 'revivalMaxPieces', 'value'),
        ('revival_sacrifice_check', 'revivalSacrifice', 'checked'),
        ('revival_max_cost_spin', 'revivalMaxCost', 'value'),
        ('revival_with_points_check', 'revivalWithPoints', 'checked'),
        ('revival_points_spin', 'revivalPoints', 'value'),
        ('revival_starting_check', 'revivalStarting', 'checked'),
        ('revival_within_turn_spin', 'revivalWithinTurn', 'value'),

        # Line of sight configuration
        ('los_ignore_enemy_check', 'losIgnoreEnemy', 'checked'),  # Fixed widget name
        ('los_ignore_all_check', 'losIgnoreAll', 'checked'),

        # Delay configuration
        ('delay_turn_check', 'delayTurnEnabled', 'checked'),
        ('delay_turn_spin', 'delayTurn', 'value'),
        ('delay_action_check', 'delayActionEnabled', 'checked'),
        ('delay_action_spin', 'delayAction', 'value'),

        # Pulse configuration
        ('pulse_interval_spin', 'pulseInterval', 'value'),

        # Fog configuration
        ('fog_vision_combo', 'fogVision', 'currentText'),
        ('fog_radius_spin', 'fogRadius', 'value'),
        ('fog_duration_spin', 'fogDuration', 'value'),
        ('fog_cost_spin', 'fogCost', 'value'),

        # Carry configuration
        ('carry_range_spin', 'carryRange', 'value'),
        ('carry_drop_on_death_check', 'carryDropOnDeath', 'checked'),
        ('carry_drop_mode_combo', 'carryDropMode', 'currentText'),
        ('carry_drop_range_spin', 'carryDropRange', 'value'),
        ('carry_drop_can_capture_check', 'carryDropCanCapture', 'checked'),
        ('carry_share_abilities_check', 'carryShareAbilities', 'checked'),
        ('carry_starting_piece_check', 'carryStartingPiece', 'checked'),
        
        # Share space configuration
        ('share_space_max_spin', 'shareSpaceMax', 'value'),
        ('share_space_same_type_check', 'shareSpaceSameType', 'checked'),
        ('share_space_friendly_check', 'shareSpaceFriendly', 'checked'),
        ('share_space_enemy_check', 'shareSpaceEnemy', 'checked'),
        ('share_space_any_check', 'shareSpaceAny', 'checked'),
        
        # Carry piece configuration
        ('carry_range_spin', 'carryRange', 'value'),
        ('carry_drop_death_check', 'carryDropOnDeath', 'checked'),
        ('carry_share_abilities_check', 'carryShareAbilities', 'checked'),
        ('carry_starting_piece_check', 'carryStartingPiece', 'checked'),
        
        # Delay configuration
        ('delay_turn_check', 'delayTurn', 'checked'),
        ('delay_turn_spin', 'delayTurnAmount', 'value'),
        ('delay_action_check', 'delayAction', 'checked'),
        ('delay_action_spin', 'delayActionAmount', 'value'),
        
        # Invisible configuration
        ('invisible_reveal_move_check', 'invisibleRevealMove', 'checked'),
        ('invisible_reveal_move_spin', 'invisibleRevealMoveAmount', 'value'),
        ('invisible_reveal_capture_check', 'invisibleRevealCapture', 'checked'),
        ('invisible_reveal_capture_spin', 'invisibleRevealCaptureAmount', 'value'),
        ('invisible_reveal_action_check', 'invisibleRevealAction', 'checked'),
        ('invisible_reveal_action_spin', 'invisibleRevealActionAmount', 'value'),
        ('invisible_reveal_los_check', 'invisibleRevealLos', 'checked'),
        
        # Trap tile configuration
        ('trap_capture_check', 'trapCapture', 'checked'),
        ('trap_immobilize_check', 'trapImmobilize', 'checked'),
        ('trap_immobilize_spin', 'trapImmobilizeAmount', 'value'),
        ('trap_teleport_check', 'trapTeleport', 'checked'),
        ('trap_add_ability_check', 'trapAddAbility', 'checked'),
        
        # Additional configurations
        ('no_turn_cost_limit_spin', 'noTurnCostLimit', 'value'),
        ('adjacency_distance_spin', 'adjacencyDistance', 'value'),
        ('los_ignore_friendly_check', 'losIgnoreFriendly', 'checked'),
        ('los_ignore_enemy_check', 'losIgnoreEnemy', 'checked'),
        ('los_ignore_all_check', 'losIgnoreAll', 'checked'),
        ('prevent_los_check', 'preventLos', 'checked'),
    ]
    
    PIECE_FIELD_MAPPINGS = [
        # Basic information
        ('name_edit', 'name', 'text'),
        ('description_edit', 'description', 'plainText'),
        ('role_combo', 'role', 'currentText'),
        ('can_castle_check', 'canCastle', 'checked'),
        ('track_starting_position_check', 'trackStartingPosition', 'checked'),
        ('color_directional_check', 'colorDirectional', 'checked'),
        ('can_capture_check', 'canCapture', 'checked'),

        # Icons
        ('black_icon_combo', 'blackIcon', 'currentText'),
        ('white_icon_combo', 'whiteIcon', 'currentText'),

        # Movement - Note: movement data is handled specially in collect_data_from_ui
        # ('move_combo', 'movement.type', 'currentText'),  # Removed - handled by movement system
        # ('capture_yes', 'canCapture', 'checked'),  # Removed - not a widget name

        # Points and Recharge system
        ('enable_points_recharge_check', 'enableRecharge', 'checked'),
        # Note: Points and recharge type-specific fields are handled in custom logic below
    ]
    
    @staticmethod
    def collect_data_from_ui(editor_instance, data_type: str) -> Dict[str, Any]:
        """
        Universal data collection method for any editor type
        
        Args:
            editor_instance: The editor instance (AbilityEditor or PieceEditor)
            data_type: Type of data ('ability' or 'piece')
            
        Returns:
            Dictionary with collected data using camelCase field names
        """
        try:
            # Initialize with default structure
            if data_type == "ability":
                data = {
                    'version': '1.0.0',
                    'name': '',
                    'description': '',
                    'cost': 0,
                    'activationMode': 'auto',
                    'tags': []
                }
                field_mappings = EditorDataInterface.ABILITY_FIELD_MAPPINGS
            elif data_type == "piece":
                data = {
                    'version': '1.0.0',
                    'name': '',
                    'description': '',
                    'role': 'Commander',
                    'canCastle': False,
                    'movement': {'type': 'orthogonal', 'distance': 1},
                    'canCapture': True,
                    'colorDirectional': False,
                    'abilities': []
                }
                field_mappings = EditorDataInterface.PIECE_FIELD_MAPPINGS
            else:
                raise ValueError(f"Unknown data type: {data_type}")
            
            # Collect data using field mappings
            for widget_attr, data_key, value_method in field_mappings:
                if hasattr(editor_instance, widget_attr):
                    widget = getattr(editor_instance, widget_attr)
                    if widget is not None:
                        try:
                            # Get the widget value
                            if value_method == 'text':
                                value = widget.text()
                            elif value_method == 'plainText':
                                value = widget.toPlainText()
                            elif value_method == 'value':
                                value = widget.value()
                            elif value_method == 'currentText':
                                value = widget.currentText()
                            elif value_method == 'checked':
                                value = widget.isChecked()
                            else:
                                continue

                            # Handle nested field keys (e.g., 'movement.distance')
                            if '.' in data_key:
                                keys = data_key.split('.')
                                current_dict = data
                                for key in keys[:-1]:
                                    if key not in current_dict:
                                        current_dict[key] = {}
                                    current_dict = current_dict[key]
                                current_dict[keys[-1]] = value
                            else:
                                data[data_key] = value

                        except (RuntimeError, AttributeError):
                            # Widget may have been deleted, skip
                            continue
            
            # Collect special data for abilities
            if data_type == "ability":
                # Collect selected tags
                if hasattr(editor_instance, 'tag_groups'):
                    tags = []
                    for tag, checkbox in editor_instance.tag_groups.items():
                        if checkbox.isChecked():
                            tags.append(tag)
                    data['tags'] = tags
                
                # Collect pattern data
                if hasattr(editor_instance, 'range_pattern'):
                    data['rangeMask'] = editor_instance.range_pattern
                if hasattr(editor_instance, 'range_piece_position'):
                    data['piecePosition'] = editor_instance.range_piece_position
                
                # Collect inline selector data
                EditorDataInterface._collect_inline_selector_data(editor_instance, data)
            
            # Collect special data for pieces
            elif data_type == "piece":

                # Handle movement data - use stored movement data if available
                if hasattr(editor_instance, 'current_movement_data') and editor_instance.current_movement_data:
                    movement_data = editor_instance.current_movement_data.copy()
                    data['movement'] = movement_data
                elif hasattr(editor_instance, 'selected_movement_type'):
                    # Fallback to selected movement type with pattern generation
                    movement_type = editor_instance.selected_movement_type
                    piece_pos = getattr(editor_instance, 'custom_pattern_piece_pos', [3, 3])

                    # Generate pattern for the movement type
                    if hasattr(editor_instance, 'generate_standard_pattern'):
                        pattern = editor_instance.generate_standard_pattern(movement_type, piece_pos)
                    else:
                        pattern = None

                    data['movement'] = {
                        'type': movement_type,
                        'pattern': pattern,
                        'piecePosition': piece_pos
                    }

                    # Use current custom pattern if available
                    if hasattr(editor_instance, 'current_custom_pattern') and editor_instance.current_custom_pattern:
                        data['movement']['pattern'] = editor_instance.current_custom_pattern

                # Ensure movement data exists with defaults
                if 'movement' not in data:
                    data['movement'] = {
                        'type': 'orthogonal',
                        'pattern': None,
                        'piecePosition': [3, 3]
                    }

                # Collect abilities list
                if hasattr(editor_instance, 'abilities'):
                    data['abilities'] = editor_instance.abilities.copy()

                # Collect promotion lists
                if hasattr(editor_instance, 'primary_promotions'):
                    data['promotions'] = editor_instance.primary_promotions.copy()
                if hasattr(editor_instance, 'secondary_promotions'):
                    data['secondaryPromotions'] = editor_instance.secondary_promotions.copy()

                # Collect recharge type-specific data (only save data for selected type)
                if hasattr(editor_instance, 'recharge_type_combo') and hasattr(editor_instance, 'enable_points_recharge_check'):
                    if editor_instance.enable_points_recharge_check.isChecked():
                        # Collect basic points data when recharge system is enabled
                        if hasattr(editor_instance, 'max_points_spin'):
                            data['maxPoints'] = editor_instance.max_points_spin.value()
                        if hasattr(editor_instance, 'starting_points_spin'):
                            data['startingPoints'] = editor_instance.starting_points_spin.value()
                        if hasattr(editor_instance, 'recharge_type_combo'):
                            data['rechargeType'] = editor_instance.recharge_type_combo.currentText()

                        recharge_type = editor_instance.recharge_type_combo.currentText()

                        if recharge_type == "turnRecharge" and hasattr(editor_instance, 'turn_recharge_spin'):
                            data['turnPoints'] = editor_instance.turn_recharge_spin.value()
                        elif recharge_type == "adjacencyRecharge" and hasattr(editor_instance, 'adjacency_config_btn'):
                            # Get adjacency configuration if available
                            if hasattr(editor_instance, 'adjacency_recharge_config'):
                                data['adjacencyRechargeConfig'] = editor_instance.adjacency_recharge_config
                        elif recharge_type == "committedRecharge" and hasattr(editor_instance, 'committed_turns_spin'):
                            data['committedRechargeTurns'] = editor_instance.committed_turns_spin.value()
            
            logger.debug(f"Collected {len(data)} fields for {data_type}")
            return data
            
        except Exception as e:
            logger.error(f"Error collecting {data_type} data: {e}")
            return data if 'data' in locals() else {}
    
    @staticmethod
    def _collect_inline_selector_data(editor_instance, data: Dict[str, Any]):
        """Collect data from inline selector widgets"""
        selector_mappings = [
            ('summon_selector', 'summonList', 'get_pieces'),
            ('revival_target_selector', 'revivalList', 'get_pieces'),
            ('carry_target_selector', 'carryList', 'get_pieces'),
            ('swap_selector', 'swapList', 'get_pieces'),
            ('displace_target_selector', 'displaceTargetList', 'get_pieces'),
            ('immobilize_target_selector', 'immobilizeTargetList', 'get_pieces'),
            ('convert_target_selector', 'convertTargetList', 'get_pieces'),
            ('buff_target_selector', 'buffTargetList', 'get_pieces'),
            ('debuff_target_selector', 'debuffTargetList', 'get_pieces'),
            ('reaction_target_selector', 'reactionTargets', 'get_pieces'),
            ('adjacency_selector', 'adjacencyList', 'get_pieces'),
            ('adjacency_recharge_selector', 'adjacencyRechargeList', 'get_pieces'),
            ('pass_through_selector', 'passThroughList', 'get_pieces'),
            ('buff_ability_selector', 'buffAbilities', 'get_abilities'),
            ('debuff_prevent_ability_selector', 'debuffPreventAbilities', 'get_abilities'),
        ]
        
        for widget_attr, data_key, method_name in selector_mappings:
            if hasattr(editor_instance, widget_attr):
                widget = getattr(editor_instance, widget_attr)
                if widget is not None:
                    try:
                        method = getattr(widget, method_name)
                        data[data_key] = method()
                    except (RuntimeError, AttributeError):
                        data[data_key] = []
    
    @staticmethod
    def populate_ui_from_data(editor_instance, data: Dict[str, Any], data_type: str):
        """
        Universal UI population method for any editor type
        
        Args:
            editor_instance: The editor instance (AbilityEditor or PieceEditor)
            data: Data dictionary with camelCase field names
            data_type: Type of data ('ability' or 'piece')
        """
        try:
            if data_type == "ability":
                field_mappings = EditorDataInterface.ABILITY_FIELD_MAPPINGS
            elif data_type == "piece":
                field_mappings = EditorDataInterface.PIECE_FIELD_MAPPINGS
            else:
                raise ValueError(f"Unknown data type: {data_type}")
            
            # Set widget values using field mappings
            for widget_attr, data_key, value_method in field_mappings:
                # Handle nested field keys (e.g., 'movement.distance')
                if '.' in data_key:
                    keys = data_key.split('.')
                    current_dict = data
                    for key in keys[:-1]:
                        if key not in current_dict:
                            current_dict = None
                            break
                        current_dict = current_dict[key]

                    if current_dict is not None and keys[-1] in current_dict:
                        value = current_dict[keys[-1]]
                    else:
                        continue
                else:
                    if data_key not in data:
                        continue
                    value = data[data_key]

                # Set widget value if widget exists
                if hasattr(editor_instance, widget_attr):
                    widget = getattr(editor_instance, widget_attr)

                    try:
                        if value_method == 'text':
                            widget.setText(str(value))
                        elif value_method == 'plainText':
                            widget.setPlainText(str(value))
                        elif value_method == 'value':
                            widget.setValue(int(value))
                        elif value_method == 'currentText':
                            index = widget.findText(str(value))
                            if index >= 0:
                                widget.setCurrentIndex(index)
                        elif value_method == 'checked':
                            widget.setChecked(bool(value))
                    except (RuntimeError, AttributeError):
                        # Widget may have been deleted, skip
                        continue
            
            # Set special data for abilities
            if data_type == "ability":
                # Set tag selections
                if 'tags' in data and hasattr(editor_instance, 'tag_groups'):
                    for tag, checkbox in editor_instance.tag_groups.items():
                        # Block signals to prevent on_tag_changed from interfering
                        checkbox.blockSignals(True)
                        checkbox.setChecked(tag in data['tags'])
                        checkbox.blockSignals(False)
                
                # Set pattern data
                if 'rangeMask' in data and hasattr(editor_instance, 'range_pattern'):
                    editor_instance.range_pattern = data['rangeMask']
                if 'piecePosition' in data and hasattr(editor_instance, 'range_piece_position'):
                    editor_instance.range_piece_position = data['piecePosition']
                
                # Set inline selector data
                EditorDataInterface._populate_inline_selector_data(editor_instance, data)
            
            # Set special data for pieces
            elif data_type == "piece":

                # Handle movement data with smart pattern detection
                if 'movement' in data:
                    movement = data['movement']

                    # Use smart pattern detection if available
                    if hasattr(editor_instance, 'detect_movement_pattern_type'):
                        detected_type, is_quick_pattern = editor_instance.detect_movement_pattern_type(movement)
                        movement_type = detected_type
                    else:
                        movement_type = movement.get('type', 'orthogonal')
                        is_quick_pattern = movement_type in ['orthogonal', 'diagonal', 'any', 'lShape', 'king', 'global']

                    # Store movement data in editor instance
                    if hasattr(editor_instance, 'current_movement_data'):
                        editor_instance.current_movement_data = movement.copy()
                    if hasattr(editor_instance, 'selected_movement_type'):
                        editor_instance.selected_movement_type = movement_type

                    # Store pattern data
                    if 'pattern' in movement and hasattr(editor_instance, 'current_custom_pattern'):
                        editor_instance.current_custom_pattern = movement['pattern']
                    if 'piecePosition' in movement and hasattr(editor_instance, 'custom_pattern_piece_pos'):
                        editor_instance.custom_pattern_piece_pos = movement['piecePosition']

                    # Set button selection based on detected pattern
                    if hasattr(editor_instance, 'movement_pattern_buttons'):
                        # Clear all button selections first
                        for btn, _ in editor_instance.movement_pattern_buttons:
                            btn.setChecked(False)
                        # Select the appropriate button
                        for btn, btn_movement_type in editor_instance.movement_pattern_buttons:
                            if btn_movement_type == movement_type:
                                btn.setChecked(True)
                                break

                    # Update movement controls
                    if hasattr(editor_instance, 'update_movement_controls'):
                        editor_instance.update_movement_controls()

                    # Log pattern detection result
                    if hasattr(editor_instance, 'log_console') and hasattr(editor_instance.log_console, 'append'):
                        if is_quick_pattern:
                            editor_instance.log_console.append(f"✓ Loaded {movement_type} quick pattern")
                        else:
                            editor_instance.log_console.append(f"✓ Loaded custom movement pattern")

                # Set abilities list
                if 'abilities' in data and hasattr(editor_instance, 'abilities'):
                    editor_instance.abilities = data['abilities'].copy()

                    # Update ability checkboxes to match loaded abilities
                    if hasattr(editor_instance, 'ability_checkboxes'):
                        for ability_name, checkbox in editor_instance.ability_checkboxes.items():
                            checkbox.setChecked(ability_name in editor_instance.abilities)

                # Set promotion lists
                if 'promotions' in data and hasattr(editor_instance, 'primary_promotions'):
                    editor_instance.primary_promotions = data['promotions'].copy()
                if 'secondaryPromotions' in data and hasattr(editor_instance, 'secondary_promotions'):
                    editor_instance.secondary_promotions = data['secondaryPromotions'].copy()

                # Set recharge type-specific data
                if hasattr(editor_instance, 'recharge_type_combo'):
                    recharge_type = data.get('rechargeType', 'turnRecharge')

                    # Set recharge type-specific values
                    if recharge_type == "turnRecharge" and 'turnPoints' in data and hasattr(editor_instance, 'turn_recharge_spin'):
                        editor_instance.turn_recharge_spin.setValue(data['turnPoints'])
                    elif recharge_type == "adjacencyRecharge" and 'adjacencyRechargeConfig' in data:
                        if hasattr(editor_instance, 'adjacency_recharge_config'):
                            editor_instance.adjacency_recharge_config = data['adjacencyRechargeConfig']
                    elif recharge_type == "committedRecharge" and 'committedRechargeTurns' in data and hasattr(editor_instance, 'committed_turns_spin'):
                        editor_instance.committed_turns_spin.setValue(data['committedRechargeTurns'])

                # Set custom pattern data
                if 'movement' in data:
                    movement = data['movement']
                    if 'pattern' in movement and hasattr(editor_instance, 'current_custom_pattern'):
                        editor_instance.current_custom_pattern = movement['pattern']
                    if 'piecePosition' in movement and hasattr(editor_instance, 'custom_pattern_piece_pos'):
                        editor_instance.custom_pattern_piece_pos = movement['piecePosition']
            
            logger.debug(f"Populated UI with {len(data)} fields for {data_type}")
            
        except Exception as e:
            logger.error(f"Error populating {data_type} UI: {e}")
    
    @staticmethod
    def _populate_inline_selector_data(editor_instance, data: Dict[str, Any]):
        """Populate inline selector widgets with data"""
        selector_mappings = [
            ('summon_selector', 'summonList', 'set_pieces'),
            ('revival_target_selector', 'revivalList', 'set_pieces'),
            ('carry_target_selector', 'carryList', 'set_pieces'),
            ('swap_selector', 'swapList', 'set_pieces'),
            ('displace_target_selector', 'displaceTargetList', 'set_pieces'),
            ('immobilize_target_selector', 'immobilizeTargetList', 'set_pieces'),
            ('convert_target_selector', 'convertTargetList', 'set_pieces'),
            ('buff_target_selector', 'buffTargetList', 'set_pieces'),
            ('debuff_target_selector', 'debuffTargetList', 'set_pieces'),
            ('reaction_target_selector', 'reactionTargets', 'set_pieces'),
            ('adjacency_selector', 'adjacencyList', 'set_pieces'),
            ('adjacency_recharge_selector', 'adjacencyRechargeList', 'set_pieces'),
            ('pass_through_selector', 'passThroughList', 'set_pieces'),
            ('buff_ability_selector', 'buffAbilities', 'set_abilities'),
            ('debuff_prevent_ability_selector', 'debuffPreventAbilities', 'set_abilities'),
        ]
        
        for widget_attr, data_key, method_name in selector_mappings:
            if data_key in data and hasattr(editor_instance, widget_attr):
                widget = getattr(editor_instance, widget_attr)
                if widget is not None:
                    try:
                        method = getattr(widget, method_name)
                        method(data[data_key])
                    except (RuntimeError, AttributeError):
                        # Widget may have been deleted, skip
                        continue
    
    @staticmethod
    def track_changes(editor_instance, has_changes: bool = True):
        """
        Universal change tracking for any editor
        
        Args:
            editor_instance: The editor instance
            has_changes: Whether there are unsaved changes
        """
        try:
            editor_instance.unsaved_changes = has_changes
            
            # Update window title
            title = editor_instance.windowTitle()
            if has_changes and not title.endswith("*"):
                editor_instance.setWindowTitle(title + "*")
            elif not has_changes and title.endswith("*"):
                editor_instance.setWindowTitle(title[:-1])
                
            logger.debug(f"Change tracking: {has_changes}")
            
        except Exception as e:
            logger.error(f"Error tracking changes: {e}")
