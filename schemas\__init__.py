"""
Adventure Chess Pydantic Schemas Package
Provides strongly-typed data models for pieces, abilities, and game components
"""

from .base import (
    Coordinate, 
    MovementType, 
    RechargeType, 
    ActivationMode,
    PieceRole,
    Pattern8x8,
    RangeMask8x8
)

from .piece_schema import Piece, Movement
from .ability_schema import Ability
from .ability_tags import *
from .data_manager import PydanticDataManager, pydantic_data_manager
from .migration import DataMigrationManager, CompatibilityLayer

__version__ = "1.0.0"

__all__ = [
    # Base types
    "Coordinate",
    "MovementType",
    "RechargeType",
    "ActivationMode",
    "PieceRole",
    "Pattern8x8",
    "RangeMask8x8",

    # Main models
    "Piece",
    "Movement",
    "Ability",

    # Data management
    "PydanticDataManager",
    "pydantic_data_manager",

    # Migration utilities
    "DataMigrationManager",
    "CompatibilityLayer",

    # Ability tag models (imported from ability_tags)
    "TAG_MODEL_REGISTRY",
    "get_tag_model",
    "validate_tag_data",
]
