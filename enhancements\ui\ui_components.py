"""
Comprehensive UI Components for Adventure Chess Creator

This consolidated module provides comprehensive UI components including:
- Enhanced search components with filters and suggestions
- Advanced validation rules and real-time validation
- Search results display and file browsing
- Validation feedback and error handling
- Performance analytics and index management

Key Features:
- Real-time search suggestions and filtering
- Advanced validation rules with custom validators
- Rich search results display with previews
- Validation feedback with visual indicators
- Performance analytics and monitoring
- Index management and optimization tools

Consolidates functionality from:
- enhanced_search_components.py (search UI components)
- enhanced_validation_rules.py (validation system)
"""

import logging
from typing import List, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QListWidget, 
    QListWidgetItem, QLabel, QPushButton, QComboBox, QCheckBox,
    QTextEdit, QSplitter, QGroupBox, QProgressBar, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QSpinBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPalette

from ..performance import get_file_system_optimizer, SearchResult

logger = logging.getLogger(__name__)

class SearchWorker(QThread):
    """Background worker for search operations"""
    
    search_completed = pyqtSignal(list)  # List[SearchResult]
    search_error = pyqtSignal(str)
    
    def __init__(self, query: str, file_type: Optional[str] = None, max_results: int = 50):
        super().__init__()
        self.query = query
        self.file_type = file_type
        self.max_results = max_results
    
    def run(self):
        try:
            optimizer = get_file_system_optimizer()
            results = optimizer.search_files(
                query=self.query,
                file_type=self.file_type,
                max_results=self.max_results
            )
            self.search_completed.emit(results)
        except Exception as e:
            logger.error(f"Search error: {e}")
            self.search_error.emit(str(e))

class EnhancedSearchWidget(QWidget):
    """
    Advanced search widget with real-time suggestions and filtering
    """
    
    file_selected = pyqtSignal(str, dict)  # file_path, metadata
    search_performed = pyqtSignal(str)  # query
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.optimizer = get_file_system_optimizer()
        self.search_worker: Optional[SearchWorker] = None
        self.suggestion_timer = QTimer()
        self.suggestion_timer.setSingleShot(True)
        self.suggestion_timer.timeout.connect(self._update_suggestions)
        
        self.setup_ui()
        
        # Initialize index
        self.optimizer.update_index()
    
    def setup_ui(self):
        """Setup the search UI"""
        layout = QVBoxLayout()
        
        # Search input section
        search_section = self._create_search_section()
        layout.addWidget(search_section)
        
        # Results section
        results_section = self._create_results_section()
        layout.addWidget(results_section)
        
        # Analytics section
        analytics_section = self._create_analytics_section()
        layout.addWidget(analytics_section)
        
        self.setLayout(layout)
    
    def _create_search_section(self) -> QWidget:
        """Create the search input section"""
        section = QGroupBox("🔍 Enhanced Search")
        layout = QVBoxLayout()
        
        # Main search input
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search pieces and abilities...")
        self.search_input.textChanged.connect(self._on_search_text_changed)
        self.search_input.returnPressed.connect(self._perform_search)
        
        self.search_button = QPushButton("🔍 Search")
        self.search_button.clicked.connect(self._perform_search)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_button)
        layout.addLayout(search_layout)
        
        # Filters
        filters_layout = QHBoxLayout()
        
        # File type filter
        filters_layout.addWidget(QLabel("Type:"))
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(["All", "Pieces", "Abilities"])
        self.file_type_combo.currentTextChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.file_type_combo)
        
        # Max results
        filters_layout.addWidget(QLabel("Max Results:"))
        self.max_results_spin = QSpinBox()
        self.max_results_spin.setRange(10, 200)
        self.max_results_spin.setValue(50)
        self.max_results_spin.valueChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.max_results_spin)
        
        filters_layout.addStretch()
        layout.addLayout(filters_layout)
        
        # Suggestions
        self.suggestions_list = QListWidget()
        self.suggestions_list.setMaximumHeight(100)
        self.suggestions_list.itemClicked.connect(self._on_suggestion_clicked)
        self.suggestions_list.hide()
        layout.addWidget(self.suggestions_list)
        
        section.setLayout(layout)
        return section
    
    def _create_results_section(self) -> QWidget:
        """Create the search results section"""
        section = QGroupBox("📋 Search Results")
        layout = QVBoxLayout()
        
        # Results info
        self.results_info = QLabel("Ready to search...")
        layout.addWidget(self.results_info)
        
        # Results list
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self._on_result_clicked)
        layout.addWidget(self.results_list)
        
        # Result preview
        self.result_preview = QTextEdit()
        self.result_preview.setMaximumHeight(100)
        self.result_preview.setReadOnly(True)
        layout.addWidget(self.result_preview)
        
        section.setLayout(layout)
        return section
    
    def _create_analytics_section(self) -> QWidget:
        """Create the analytics section"""
        section = QGroupBox("📊 Search Analytics")
        layout = QHBoxLayout()
        
        self.analytics_label = QLabel("No searches performed yet")
        layout.addWidget(self.analytics_label)
        
        self.refresh_index_btn = QPushButton("🔄 Refresh Index")
        self.refresh_index_btn.clicked.connect(self._refresh_index)
        layout.addWidget(self.refresh_index_btn)
        
        section.setLayout(layout)
        return section
    
    def _on_search_text_changed(self, text: str):
        """Handle search text changes for suggestions"""
        if len(text) >= 2:
            self.suggestion_timer.start(300)  # 300ms delay
        else:
            self.suggestions_list.hide()
    
    def _update_suggestions(self):
        """Update search suggestions"""
        try:
            query = self.search_input.text()
            if len(query) < 2:
                return
            
            file_type = self._get_selected_file_type()
            suggestions = self.optimizer.get_file_suggestions(query, file_type, limit=5)
            
            self.suggestions_list.clear()
            if suggestions:
                for suggestion in suggestions:
                    self.suggestions_list.addItem(suggestion)
                self.suggestions_list.show()
            else:
                self.suggestions_list.hide()
                
        except Exception as e:
            logger.error(f"Error updating suggestions: {e}")
    
    def _on_suggestion_clicked(self, item: QListWidgetItem):
        """Handle suggestion click"""
        self.search_input.setText(item.text())
        self.suggestions_list.hide()
        self._perform_search()
    
    def _on_filter_changed(self):
        """Handle filter changes"""
        if self.search_input.text().strip():
            self._perform_search()
    
    def _perform_search(self):
        """Perform the search"""
        query = self.search_input.text().strip()
        if not query:
            return
        
        # Hide suggestions
        self.suggestions_list.hide()
        
        # Show loading
        self.results_info.setText("🔄 Searching...")
        self.results_list.clear()
        self.result_preview.clear()
        
        # Start search worker
        if self.search_worker and self.search_worker.isRunning():
            self.search_worker.terminate()
        
        file_type = self._get_selected_file_type()
        max_results = self.max_results_spin.value()
        
        self.search_worker = SearchWorker(query, file_type, max_results)
        self.search_worker.search_completed.connect(self._on_search_completed)
        self.search_worker.search_error.connect(self._on_search_error)
        self.search_worker.start()
        
        self.search_performed.emit(query)
    
    def _get_selected_file_type(self) -> Optional[str]:
        """Get the selected file type filter"""
        type_text = self.file_type_combo.currentText()
        if type_text == "Pieces":
            return "piece"
        elif type_text == "Abilities":
            return "ability"
        return None
    
    @pyqtSlot(list)
    def _on_search_completed(self, results: List[SearchResult]):
        """Handle search completion"""
        self.results_list.clear()
        
        if not results:
            self.results_info.setText("No results found")
            return
        
        self.results_info.setText(f"Found {len(results)} results")
        
        for result in results:
            # Create result item
            item_text = f"📄 {result.filename}"
            if result.relevance_score > 0:
                item_text += f" (Score: {result.relevance_score:.1f})"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, result)
            
            # Add matched fields info
            if result.matched_fields:
                tooltip = f"Matched fields: {', '.join(result.matched_fields)}\n"
                tooltip += f"Preview: {result.preview_text}"
                item.setToolTip(tooltip)
            
            self.results_list.addItem(item)
        
        # Update analytics
        self._update_analytics()
    
    @pyqtSlot(str)
    def _on_search_error(self, error: str):
        """Handle search error"""
        self.results_info.setText(f"Search error: {error}")
        logger.error(f"Search error: {error}")
    
    def _on_result_clicked(self, item: QListWidgetItem):
        """Handle result click"""
        result: SearchResult = item.data(Qt.ItemDataRole.UserRole)
        if result:
            # Show preview
            preview_text = f"File: {result.filename}\n"
            preview_text += f"Type: {result.file_type}\n"
            preview_text += f"Relevance: {result.relevance_score:.2f}\n"
            preview_text += f"Preview: {result.preview_text}\n"
            
            if result.metadata.get('tags'):
                preview_text += f"Tags: {', '.join(result.metadata['tags'])}\n"
            
            self.result_preview.setText(preview_text)
            
            # Emit selection signal
            self.file_selected.emit(result.file_path, result.metadata)
    
    def _refresh_index(self):
        """Refresh the file index"""
        try:
            self.refresh_index_btn.setText("🔄 Refreshing...")
            self.refresh_index_btn.setEnabled(False)
            
            indexed_count = self.optimizer.update_index()
            
            self.refresh_index_btn.setText("🔄 Refresh Index")
            self.refresh_index_btn.setEnabled(True)
            
            self.results_info.setText(f"Index refreshed: {indexed_count} files processed")
            self._update_analytics()
            
        except Exception as e:
            logger.error(f"Error refreshing index: {e}")
            self.refresh_index_btn.setText("🔄 Refresh Index")
            self.refresh_index_btn.setEnabled(True)
    
    def _update_analytics(self):
        """Update analytics display"""
        try:
            stats = self.optimizer.get_index_statistics()
            search_stats = stats.get('search_stats', {})
            
            analytics_text = f"Files: {stats.get('total_files', 0)} | "
            analytics_text += f"Searches: {search_stats.get('total_searches', 0)} | "
            analytics_text += f"Avg Time: {search_stats.get('avg_search_time_ms', 0):.1f}ms"
            
            self.analytics_label.setText(analytics_text)
            
        except Exception as e:
            logger.error(f"Error updating analytics: {e}")

class FileIndexBrowser(QWidget):
    """
    Widget for browsing and managing the file index
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.optimizer = get_file_system_optimizer()
        self.setup_ui()
        self.refresh_data()
    
    def setup_ui(self):
        """Setup the browser UI"""
        layout = QVBoxLayout()
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_data)
        controls_layout.addWidget(self.refresh_btn)
        
        self.stats_label = QLabel("Loading...")
        controls_layout.addWidget(self.stats_label)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Index table
        self.index_table = QTableWidget()
        self.index_table.setColumnCount(6)
        self.index_table.setHorizontalHeaderLabels([
            "Filename", "Type", "Size", "Modified", "Tags", "Description"
        ])
        
        # Make table sortable and resizable
        self.index_table.setSortingEnabled(True)
        header = self.index_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        header.setStretchLastSection(True)
        
        layout.addWidget(self.index_table)
        
        self.setLayout(layout)
    
    def refresh_data(self):
        """Refresh the index data display"""
        try:
            # Update statistics
            stats = self.optimizer.get_index_statistics()
            stats_text = f"Total Files: {stats.get('total_files', 0)} | "
            stats_text += f"Size: {stats.get('total_size_bytes', 0) / 1024:.1f} KB"
            self.stats_label.setText(stats_text)
            
            # Load and display index entries in table
            try:
                index_entries = self.optimizer.get_all_index_entries()
                self.index_table.setRowCount(len(index_entries))

                for row, entry in enumerate(index_entries):
                    # File path
                    self.index_table.setItem(row, 0, QTableWidgetItem(entry.get('file_path', '')))
                    # File type
                    self.index_table.setItem(row, 1, QTableWidgetItem(entry.get('file_type', '')))
                    # Size
                    size_kb = entry.get('size_bytes', 0) / 1024
                    self.index_table.setItem(row, 2, QTableWidgetItem(f"{size_kb:.1f} KB"))
                    # Last modified
                    last_modified = entry.get('last_modified', '')
                    self.index_table.setItem(row, 3, QTableWidgetItem(str(last_modified)))

            except AttributeError:
                # Fallback if get_all_index_entries method doesn't exist
                self.index_table.setRowCount(0)
            except Exception as e:
                logger.error(f"Error loading index entries: {e}")
                self.index_table.setRowCount(0)
            
        except Exception as e:
            logger.error(f"Error refreshing index browser: {e}")
            self.stats_label.setText("Error loading data")


# ========== ENHANCED VALIDATION RULES ==========

import re
from typing import Any, Dict, List, Optional, Union
from pydantic import field_validator, model_validator

class ValidationRules:
    """
    Enhanced validation rules for Adventure Chess Creator models
    """

    # Security patterns to check for
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',                # JavaScript URLs
        r'vbscript:',                 # VBScript URLs
        r'on\w+\s*=',                 # Event handlers
        r'eval\s*\(',                 # eval() calls
        r'exec\s*\(',                 # exec() calls
        r'__import__',                # Python imports
        r'subprocess',                # Subprocess calls
        r'os\.system',                # OS system calls
    ]

    # Maximum lengths for various fields
    MAX_LENGTHS = {
        'name': 100,
        'description': 1000,
        'filename': 255,
        'tag_name': 50,
        'version': 20,
    }

    # Valid ranges for numeric fields
    NUMERIC_RANGES = {
        'max_points': (0, 999),
        'starting_points': (0, 999),
        'distance': (1, 8),
        'duration': (1, 100),
        'coordinate': (0, 7),
        'pattern_value': (0, 1),
    }

    @classmethod
    def validate_string_field(cls, value: str, field_name: str,
                             allow_empty: bool = False) -> str:
        """
        Enhanced string validation with security checks

        Args:
            value: String value to validate
            field_name: Name of the field being validated
            allow_empty: Whether empty strings are allowed

        Returns:
            Validated and sanitized string

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if allow_empty:
                return ""
            else:
                raise ValueError(f"{field_name} cannot be None")

        # Convert to string if not already
        if not isinstance(value, str):
            value = str(value)

        # Check for empty string
        if not value.strip() and not allow_empty:
            raise ValueError(f"{field_name} cannot be empty")

        # Check length
        max_length = cls.MAX_LENGTHS.get(field_name, 500)
        if len(value) > max_length:
            raise ValueError(f"{field_name} exceeds maximum length of {max_length} characters")

        # Security validation - check for dangerous patterns
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError(f"{field_name} contains potentially dangerous content")

        # Basic sanitization
        value = value.strip()

        return value

    @classmethod
    def validate_numeric_field(cls, value: Union[int, float], field_name: str) -> Union[int, float]:
        """
        Enhanced numeric validation with range checks

        Args:
            value: Numeric value to validate
            field_name: Name of the field being validated

        Returns:
            Validated numeric value

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            raise ValueError(f"{field_name} cannot be None")

        # Convert to appropriate numeric type
        if isinstance(value, str):
            try:
                if '.' in value:
                    value = float(value)
                else:
                    value = int(value)
            except ValueError:
                raise ValueError(f"{field_name} must be a valid number")

        if not isinstance(value, (int, float)):
            raise ValueError(f"{field_name} must be a number")

        # Check for valid range
        if field_name in cls.NUMERIC_RANGES:
            min_val, max_val = cls.NUMERIC_RANGES[field_name]
            if value < min_val or value > max_val:
                raise ValueError(f"{field_name} must be between {min_val} and {max_val}")

        # Check for reasonable bounds (prevent extremely large numbers)
        if abs(value) > 1000000:
            raise ValueError(f"{field_name} value is unreasonably large")

        return value

    @classmethod
    def validate_list_field(cls, value: List[Any], field_name: str,
                           max_items: int = 100, allow_empty: bool = True) -> List[Any]:
        """
        Enhanced list validation

        Args:
            value: List value to validate
            field_name: Name of the field being validated
            max_items: Maximum number of items allowed
            allow_empty: Whether empty lists are allowed

        Returns:
            Validated list

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if allow_empty:
                return []
            else:
                raise ValueError(f"{field_name} cannot be None")

        if not isinstance(value, list):
            raise ValueError(f"{field_name} must be a list")

        if not value and not allow_empty:
            raise ValueError(f"{field_name} cannot be empty")

        if len(value) > max_items:
            raise ValueError(f"{field_name} cannot have more than {max_items} items")

        return value

    @classmethod
    def validate_coordinate(cls, value: Union[int, List[int]], field_name: str) -> Union[int, List[int]]:
        """
        Validate coordinate values for chess board positions

        Args:
            value: Coordinate value (single int or list of ints)
            field_name: Name of the field being validated

        Returns:
            Validated coordinate

        Raises:
            ValueError: If validation fails
        """
        if isinstance(value, list):
            for i, coord in enumerate(value):
                if not isinstance(coord, int) or coord < 0 or coord > 7:
                    raise ValueError(f"{field_name}[{i}] must be an integer between 0 and 7")
            return value
        elif isinstance(value, int):
            if value < 0 or value > 7:
                raise ValueError(f"{field_name} must be an integer between 0 and 7")
            return value
        else:
            raise ValueError(f"{field_name} must be an integer or list of integers")

    @classmethod
    def validate_pattern_grid(cls, value: List[List[int]], field_name: str = "pattern") -> List[List[int]]:
        """
        Validate movement pattern grid

        Args:
            value: 2D grid representing movement pattern
            field_name: Name of the field being validated

        Returns:
            Validated pattern grid

        Raises:
            ValueError: If validation fails
        """
        if not isinstance(value, list):
            raise ValueError(f"{field_name} must be a list")

        if len(value) != 8:
            raise ValueError(f"{field_name} must have exactly 8 rows")

        for i, row in enumerate(value):
            if not isinstance(row, list):
                raise ValueError(f"{field_name} row {i} must be a list")

            if len(row) != 8:
                raise ValueError(f"{field_name} row {i} must have exactly 8 columns")

            for j, cell in enumerate(row):
                if not isinstance(cell, int) or cell < 0 or cell > 3:
                    raise ValueError(f"{field_name}[{i}][{j}] must be an integer between 0 and 3")

        return value


class EnhancedValidationMixin:
    """
    Mixin class to add enhanced validation to Pydantic models
    """

    @field_validator('name', mode='before')
    @classmethod
    def validate_name(cls, v):
        return ValidationRules.validate_string_field(v, 'name')

    @field_validator('description', mode='before')
    @classmethod
    def validate_description(cls, v):
        return ValidationRules.validate_string_field(v, 'description', allow_empty=True)

    @field_validator('version', mode='before')
    @classmethod
    def validate_version(cls, v):
        validated = ValidationRules.validate_string_field(v, 'version')
        # Additional version format validation
        if not re.match(r'^\d+\.\d+\.\d+$', validated):
            raise ValueError('version must be in format X.Y.Z (e.g., 1.0.0)')
        return validated


def create_piece_validation_rules():
    """Create validation rules specific to piece data"""

    def validate_piece_name(data):
        name = data.get('name', '')
        if not name or not name.strip():
            return False, "Piece name is required"
        if len(name) > 50:
            return False, "Piece name must be 50 characters or less"
        return True, "Piece name is valid"

    def validate_piece_value(data):
        value = data.get('value', 0)
        if not isinstance(value, (int, float)) or value < 0:
            return False, "Piece value must be a non-negative number"
        if value > 100:
            return False, "Piece value seems unusually high (max recommended: 100)"
        return True, "Piece value is valid"

    def validate_piece_movement(data):
        movement = data.get('movement', {})
        if not isinstance(movement, dict):
            return False, "Movement data must be a dictionary"

        # Check for required movement fields
        required_fields = ['pattern']
        for field in required_fields:
            if field not in movement:
                return False, f"Movement is missing required field: {field}"

        return True, "Piece movement is valid"

    return [
        ("Piece Name", validate_piece_name),
        ("Piece Value", validate_piece_value),
        ("Piece Movement", validate_piece_movement)
    ]


def create_ability_validation_rules():
    """Create validation rules specific to ability data"""

    def validate_ability_name(data):
        name = data.get('name', '')
        if not name or not name.strip():
            return False, "Ability name is required"
        if len(name) > 100:
            return False, "Ability name must be 100 characters or less"
        return True, "Ability name is valid"

    def validate_ability_cost(data):
        cost = data.get('cost', 0)
        if not isinstance(cost, (int, float)) or cost < 0:
            return False, "Ability cost must be a non-negative number"
        if cost > 50:
            return False, "Ability cost seems unusually high (max recommended: 50)"
        return True, "Ability cost is valid"

    def validate_ability_tags(data):
        tags = data.get('tags', [])
        if not isinstance(tags, list):
            return False, "Ability tags must be a list"
        if not tags:
            return False, "At least one ability tag is required"
        return True, "Ability tags are valid"

    return [
        ("Ability Name", validate_ability_name),
        ("Ability Cost", validate_ability_cost),
        ("Ability Tags", validate_ability_tags)
    ]


# ========== SEARCH RESULTS WIDGET ==========

class SearchResultsWidget(QWidget):
    """
    Standalone widget for displaying search results
    This provides the interface expected by the import statements
    """

    result_selected = pyqtSignal(str, dict)  # file_path, metadata

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup the results display UI"""
        layout = QVBoxLayout()

        # Results info
        self.results_info = QLabel("No search performed yet...")
        layout.addWidget(self.results_info)

        # Results list
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self._on_result_clicked)
        layout.addWidget(self.results_list)

        # Result preview
        self.result_preview = QTextEdit()
        self.result_preview.setMaximumHeight(100)
        self.result_preview.setReadOnly(True)
        layout.addWidget(self.result_preview)

        self.setLayout(layout)

    def display_results(self, results: List[SearchResult]):
        """Display search results"""
        self.results_list.clear()

        if not results:
            self.results_info.setText("No results found")
            return

        self.results_info.setText(f"Found {len(results)} results")

        for result in results:
            # Create result item
            item_text = f"📄 {result.filename}"
            if result.relevance_score > 0:
                item_text += f" (Score: {result.relevance_score:.1f})"

            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, result)

            # Add matched fields info
            if result.matched_fields:
                tooltip = f"Matched fields: {', '.join(result.matched_fields)}\n"
                tooltip += f"Preview: {result.preview_text}"
                item.setToolTip(tooltip)

            self.results_list.addItem(item)

    def _on_result_clicked(self, item: QListWidgetItem):
        """Handle result click"""
        result: SearchResult = item.data(Qt.ItemDataRole.UserRole)
        if result:
            # Show preview
            preview_text = f"File: {result.filename}\n"
            preview_text += f"Type: {result.file_type}\n"
            preview_text += f"Relevance: {result.relevance_score:.2f}\n"
            preview_text += f"Preview: {result.preview_text}\n"

            if result.metadata.get('tags'):
                preview_text += f"Tags: {', '.join(result.metadata['tags'])}\n"

            self.result_preview.setText(preview_text)

            # Emit selection signal
            self.result_selected.emit(result.file_path, result.metadata)
