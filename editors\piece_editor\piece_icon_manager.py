"""
Piece Icon Manager for Adventure Chess Creator

This module handles all icon functionality for the piece editor:
- Icon preview and display management
- Icon file handling and loading
- Icon selection and file operations
- Icon path processing and validation
- Icon UI updates and refreshing

Extracted from piece_editor.py to improve maintainability and make
icon management more modular and easier to test.
"""

import logging
import os
import shutil
from typing import Dict, Any, Optional, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton,
    QFileDialog, QMessageBox, QGroupBox, QFormLayout
)
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt

from config import ICONS_DIR

logger = logging.getLogger(__name__)


class PieceIconManager:
    """
    Handles all icon functionality for the piece editor.
    
    This class manages:
    - Icon preview and display management
    - Icon file handling and loading
    - Icon selection and file operations
    - Icon path processing and validation
    - Icon UI updates and refreshing
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the icon manager.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
        
        # Initialize icon widgets (will be created by UI components)
        self.black_icon_combo = None
        self.white_icon_combo = None
        self.black_icon_preview = None
        self.white_icon_preview = None
        self.black_icon_picker = None
        self.white_icon_picker = None
    
    def create_icon_ui(self, parent_layout) -> None:
        """
        Create the icon selection UI for integration into basic info section.

        Args:
            parent_layout: The layout to add the icon UI to
        """
        try:
            # Instructions
            instructions = QLabel("Select icons for black and white pieces")
            instructions.setWordWrap(True)
            instructions.setStyleSheet("color: #666; font-style: italic; margin-bottom: 8px; font-size: 11px;")
            parent_layout.addWidget(instructions)

            # Black icon selection
            black_icon_layout = QHBoxLayout()
            black_label = QLabel("Black:")
            black_label.setMinimumWidth(45)
            black_icon_layout.addWidget(black_label)

            self.black_icon_combo = QComboBox()
            self.black_icon_combo.currentTextChanged.connect(self.update_icon_previews)
            self.black_icon_combo.setMinimumWidth(120)
            black_icon_layout.addWidget(self.black_icon_combo)

            self.black_icon_preview = QLabel()
            self.black_icon_preview.setFixedSize(32, 32)
            self.black_icon_preview.setStyleSheet("border: 1px solid #ccc; background: #f5f5f5;")
            black_icon_layout.addWidget(self.black_icon_preview)

            self.black_icon_picker = QPushButton("...")
            self.black_icon_picker.setFixedSize(24, 24)
            self.black_icon_picker.setToolTip("Pick icon file")
            self.black_icon_picker.clicked.connect(lambda: self.select_icon('black'))
            black_icon_layout.addWidget(self.black_icon_picker)

            parent_layout.addLayout(black_icon_layout)

            # White icon selection
            white_icon_layout = QHBoxLayout()
            white_label = QLabel("White:")
            white_label.setMinimumWidth(45)
            white_icon_layout.addWidget(white_label)

            self.white_icon_combo = QComboBox()
            self.white_icon_combo.currentTextChanged.connect(self.update_icon_previews)
            self.white_icon_combo.setMinimumWidth(120)
            white_icon_layout.addWidget(self.white_icon_combo)

            self.white_icon_preview = QLabel()
            self.white_icon_preview.setFixedSize(32, 32)
            self.white_icon_preview.setStyleSheet("border: 1px solid #ccc; background: #f5f5f5;")
            white_icon_layout.addWidget(self.white_icon_preview)

            self.white_icon_picker = QPushButton("...")
            self.white_icon_picker.setFixedSize(24, 24)
            self.white_icon_picker.setToolTip("Pick icon file")
            self.white_icon_picker.clicked.connect(lambda: self.select_icon('white'))
            white_icon_layout.addWidget(self.white_icon_picker)

            parent_layout.addLayout(white_icon_layout)

            # Initialize icon combos and previews
            self.refresh_icon_combos()
            self.update_icon_previews()

            logger.info("Icon UI created successfully")

        except Exception as e:
            logger.error(f"Error creating icon UI: {e}")
            raise
    
    def refresh_icon_combos(self) -> None:
        """Refresh the icon combo boxes with available icon files."""
        try:
            # Check if combo boxes are properly initialized
            if self.black_icon_combo is None or self.white_icon_combo is None:
                logger.warning("Icon combo boxes not initialized, skipping refresh")
                return

            if not os.path.exists(ICONS_DIR):
                os.makedirs(ICONS_DIR, exist_ok=True)
                return

            # Get list of image files
            icon_files = [
                f for f in os.listdir(ICONS_DIR)
                if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg'))
            ]

            # Clear existing items
            self.black_icon_combo.clear()
            self.white_icon_combo.clear()

            # Add default "None" option
            self.black_icon_combo.addItem("(None)")
            self.white_icon_combo.addItem("(None)")

            # Add icon files
            for icon_file in sorted(icon_files):
                self.black_icon_combo.addItem(icon_file)
                self.white_icon_combo.addItem(icon_file)

            logger.debug(f"Refreshed icon combos with {len(icon_files)} icons")

        except Exception as e:
            logger.error(f"Error refreshing icon combos: {e}")
    
    def select_icon(self, color: str) -> None:
        """
        Open file dialog to select an icon.
        
        Args:
            color: 'black' or 'white' to specify which icon to select
        """
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.editor,
                f"Select {color.title()} Icon",
                str(ICONS_DIR),
                "Image Files (*.png *.jpg *.jpeg *.gif *.bmp *.svg)"
            )
            
            if file_path:
                # Get filename
                filename = os.path.basename(file_path)
                dest_path = os.path.join(ICONS_DIR, filename)
                
                # Copy file to icons directory if not already there
                if file_path != dest_path:
                    if os.path.exists(dest_path):
                        # Ask user if they want to overwrite
                        reply = QMessageBox.question(
                            self.editor,
                            "File Exists",
                            f"Icon '{filename}' already exists. Overwrite?",
                            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                        )
                        if reply != QMessageBox.StandardButton.Yes:
                            return
                    
                    shutil.copy2(file_path, dest_path)
                
                # Update combo box
                self.refresh_icon_combos()
                
                # Select the new icon
                if color == 'black':
                    self.black_icon_combo.setCurrentText(filename)
                else:
                    self.white_icon_combo.setCurrentText(filename)
                
                # Log the action
                if hasattr(self.editor, 'log_console'):
                    self.editor.log_console.append(f"Icon {filename} added for {color} pieces.")
                
                # Mark as changed
                self.editor.mark_unsaved_changes()
                
                logger.info(f"Icon selected for {color}: {filename}")
                
        except Exception as e:
            logger.error(f"Error selecting icon for {color}: {e}")
            QMessageBox.critical(self.editor, "Error", f"Failed to select icon: {str(e)}")
    
    def update_icon_previews(self) -> None:
        """Update icon preview labels."""
        try:
            if not self.black_icon_combo or not self.white_icon_combo:
                return
                
            self._set_preview(self.black_icon_combo, self.black_icon_preview)
            self._set_preview(self.white_icon_combo, self.white_icon_preview)
            
        except Exception as e:
            logger.error(f"Error updating icon previews: {e}")
    
    def _set_preview(self, combo: QComboBox, preview_label: QLabel) -> None:
        """
        Set preview for a specific icon combo and label.

        Args:
            combo: The combo box containing the icon selection
            preview_label: The label to display the preview in
        """
        try:
            icon_name = combo.currentText()

            if icon_name and icon_name != "(None)":
                icon_path = os.path.join(ICONS_DIR, icon_name)

                if os.path.exists(icon_path):
                    pixmap = QPixmap(icon_path)

                    if not pixmap.isNull():
                        # Scale pixmap to fit preview (32x32 for compact design)
                        scaled_pixmap = pixmap.scaled(
                            30, 30,
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        preview_label.setPixmap(scaled_pixmap)

                        # Set dark theme background for icon preview
                        preview_label.setStyleSheet(
                            "border: 1px solid #4a5568; background: #2d3748; padding: 1px;"
                        )
                        return

            # Set empty state with slashed circle symbol and dark theme background
            preview_label.clear()  # Clear any existing pixmap
            preview_label.setText("🚫")  # Slashed circle symbol
            preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            preview_label.setStyleSheet(
                "border: 1px solid #4a5568; background: #2d3748; color: #a0aec0; "
                "font-size: 16px; padding: 2px;"
            )

        except Exception as e:
            logger.error(f"Error setting preview: {e}")
    
    def get_icon_data(self) -> Dict[str, Optional[str]]:
        """
        Get the current icon data.
        
        Returns:
            Dictionary containing icon data
        """
        try:
            black_icon = None
            white_icon = None
            
            if self.black_icon_combo:
                black_text = self.black_icon_combo.currentText()
                if black_text and black_text != "(None)":
                    black_icon = black_text
            
            if self.white_icon_combo:
                white_text = self.white_icon_combo.currentText()
                if white_text and white_text != "(None)":
                    white_icon = white_text
            
            return {
                "black_icon": black_icon,
                "white_icon": white_icon
            }
            
        except Exception as e:
            logger.error(f"Error getting icon data: {e}")
            return {"black_icon": None, "white_icon": None}
    
    def set_icon_data(self, icon_data: Dict[str, Any]) -> None:
        """
        Set icon data from external source.
        
        Args:
            icon_data: Dictionary containing icon data
        """
        try:
            if not self.black_icon_combo or not self.white_icon_combo:
                return
            
            # Set black icon
            black_icon = icon_data.get('black_icon')
            if black_icon:
                # Find the icon in the combo box
                index = self.black_icon_combo.findText(black_icon)
                if index >= 0:
                    self.black_icon_combo.setCurrentIndex(index)
                else:
                    logger.warning(f"Black icon '{black_icon}' not found in combo box")
                    self.black_icon_combo.setCurrentIndex(0)  # Set to "(None)"
            else:
                self.black_icon_combo.setCurrentIndex(0)  # Set to "(None)"
            
            # Set white icon
            white_icon = icon_data.get('white_icon')
            if white_icon:
                # Find the icon in the combo box
                index = self.white_icon_combo.findText(white_icon)
                if index >= 0:
                    self.white_icon_combo.setCurrentIndex(index)
                else:
                    logger.warning(f"White icon '{white_icon}' not found in combo box")
                    self.white_icon_combo.setCurrentIndex(0)  # Set to "(None)"
            else:
                self.white_icon_combo.setCurrentIndex(0)  # Set to "(None)"
            
            # Update previews
            self.update_icon_previews()
            
            logger.info(f"Icon data set: black={black_icon}, white={white_icon}")
            
        except Exception as e:
            logger.error(f"Error setting icon data: {e}")
            raise
    
    def reset_icons(self) -> None:
        """Reset icons to default (none selected) state."""
        try:
            if self.black_icon_combo:
                self.black_icon_combo.setCurrentIndex(0)  # "(None)"
            
            if self.white_icon_combo:
                self.white_icon_combo.setCurrentIndex(0)  # "(None)"
            
            self.update_icon_previews()
            
            logger.info("Icons reset to default state")
            
        except Exception as e:
            logger.error(f"Error resetting icons: {e}")
    
    def validate_icon_data(self) -> List[str]:
        """
        Validate icon data for consistency.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        try:
            icon_data = self.get_icon_data()
            
            # Check if icon files exist
            for color, icon_name in icon_data.items():
                if icon_name:
                    icon_path = os.path.join(ICONS_DIR, icon_name)
                    if not os.path.exists(icon_path):
                        errors.append(f"{color.replace('_', ' ').title()} icon file '{icon_name}' does not exist")
            
        except Exception as e:
            errors.append(f"Icon validation error: {str(e)}")
        
        return errors
