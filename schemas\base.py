"""
Base Pydantic models and types for Adventure Chess
Provides common data types, enums, and validation logic
"""

from typing import List, Tuple, Union, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic.types import conint, constr


class MovementType(str, Enum):
    """Valid movement types for pieces"""
    ORTHOGONAL = "orthogonal"
    DIAGONAL = "diagonal"
    ANY = "any"
    L_SHAPE = "lShape"
    CUSTOM = "custom"


class RechargeType(str, Enum):
    """Valid recharge types for piece point systems"""
    TURN_RECHARGE = "turnRecharge"
    ADJACENCY_RECHARGE = "adjacencyRecharge"
    COMMITTED_RECHARGE = "committedRecharge"


class ActivationMode(str, Enum):
    """Valid activation modes for abilities"""
    AUTO = "auto"
    CLICK = "click"


class PieceRole(str, Enum):
    """Valid roles for pieces"""
    COMMANDER = "Commander"
    SUPPORTER = "Supporter"


class DisplacementDirection(str, Enum):
    """Valid directions for displacement abilities"""
    N = "N"
    S = "S"
    E = "E"
    W = "W"
    NE = "NE"
    NW = "NW"
    SE = "SE"
    SW = "SW"
    CUSTOM = "Custom"


class TargetType(str, Enum):
    """Valid target types for abilities"""
    ENEMY = "Enemy"
    FRIENDLY = "Friendly"
    ANY = "Any"


class AreaEffectShape(str, Enum):
    """Valid area effect shapes"""
    CIRCLE = "Circle"
    SQUARE = "Square"
    CROSS = "Cross"
    LINE = "Line"
    CUSTOM = "Custom"


class VisionType(str, Enum):
    """Valid vision types for fog of war"""
    SIGHT = "sight"
    LANTERN = "lantern"


class ReactionEventType(str, Enum):
    """Valid reaction event types"""
    ON_ENEMY_MOVE = "onEnemyMove"
    ON_ALLY_MOVE = "onAllyMove"
    ON_ENEMY_CAPTURE = "onEnemyCapture"
    ON_ALLY_CAPTURE = "onAllyCapture"
    ON_ENEMY_DEATH = "onEnemyDeath"
    ON_ALLY_DEATH = "onAllyDeath"
    ON_PIECE_ENTER_RANGE = "onPieceEnterRange"
    ON_PIECE_LEAVE_RANGE = "onPieceLeaveRange"
    ON_TURN_START = "onTurnStart"
    ON_TURN_END = "onTurnEnd"


class Coordinate(BaseModel):
    """Represents a coordinate on the 8x8 board"""
    row: conint(ge=0, le=7) = Field(..., description="Row position (0-7)")
    col: conint(ge=0, le=7) = Field(..., description="Column position (0-7)")

    def to_list(self) -> List[int]:
        """Convert to [row, col] list format for compatibility"""
        return [self.row, self.col]

    @classmethod
    def from_list(cls, coords: List[int]) -> 'Coordinate':
        """Create from [row, col] list format"""
        if len(coords) != 2:
            raise ValueError("Coordinate list must have exactly 2 elements")
        return cls(row=coords[0], col=coords[1])

    def __hash__(self) -> int:
        """Make Coordinate hashable for use as dictionary keys"""
        return hash((self.row, self.col))

    def __eq__(self, other) -> bool:
        """Define equality for Coordinate objects"""
        if not isinstance(other, Coordinate):
            return False
        return self.row == other.row and self.col == other.col


# Type aliases for common patterns
Pattern8x8 = List[List[conint(ge=0, le=5)]]  # 8x8 grid with values 0-5 for pattern editor
RangeMask8x8 = List[List[bool]]  # 8x8 grid of boolean values for range masks
Points = conint(ge=0, le=99)  # Point values (0-99)
Distance = conint(ge=0, le=8)  # Distance values (0-8)
Duration = conint(ge=1, le=10)  # Duration values (1-10)


class BaseAdventureChessModel(BaseModel):
    """Base model for all Adventure Chess data structures"""
    version: str = Field(default="1.0.0", description="Schema version")
    
    class Config:
        # Allow extra fields for backward compatibility during migration
        extra = "allow"
        # Use enum values instead of enum objects in serialization
        use_enum_values = True
        # Validate assignment to catch errors early
        validate_assignment = True


def create_default_pattern() -> Pattern8x8:
    """Create a default 8x8 pattern filled with zeros"""
    return [[0 for _ in range(8)] for _ in range(8)]


def create_default_range_mask() -> RangeMask8x8:
    """Create a default 8x8 range mask filled with False"""
    return [[False for _ in range(8)] for _ in range(8)]


def validate_pattern_8x8(pattern: Any) -> Pattern8x8:
    """Validate and normalize an 8x8 pattern"""
    if not isinstance(pattern, list):
        raise ValueError("Pattern must be a list")
    
    if len(pattern) != 8:
        raise ValueError("Pattern must have exactly 8 rows")
    
    normalized = []
    for i, row in enumerate(pattern):
        if not isinstance(row, list):
            raise ValueError(f"Row {i} must be a list")
        if len(row) != 8:
            raise ValueError(f"Row {i} must have exactly 8 columns")
        
        normalized_row = []
        for j, cell in enumerate(row):
            if not isinstance(cell, int) or cell < 0 or cell > 5:
                raise ValueError(f"Cell [{i}][{j}] must be an integer between 0 and 5")
            normalized_row.append(cell)
        normalized.append(normalized_row)
    
    return normalized


def validate_range_mask_8x8(mask: Any) -> RangeMask8x8:
    """Validate and normalize an 8x8 range mask"""
    if not isinstance(mask, list):
        raise ValueError("Range mask must be a list")
    
    if len(mask) != 8:
        raise ValueError("Range mask must have exactly 8 rows")
    
    normalized = []
    for i, row in enumerate(mask):
        if not isinstance(row, list):
            raise ValueError(f"Row {i} must be a list")
        if len(row) != 8:
            raise ValueError(f"Row {i} must have exactly 8 columns")
        
        normalized_row = []
        for j, cell in enumerate(row):
            if not isinstance(cell, bool):
                # Try to convert to bool
                try:
                    normalized_row.append(bool(cell))
                except:
                    raise ValueError(f"Cell [{i}][{j}] must be a boolean value")
            else:
                normalized_row.append(cell)
        normalized.append(normalized_row)
    
    return normalized
