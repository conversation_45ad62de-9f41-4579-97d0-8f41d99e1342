"""
PassThrough tag configuration for ability editor.
Handles pass through ability configurations with piece selection and range patterns.
"""

from PyQt6.QtWidgets import (QFormLayout, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QPushButton, QLabel, QWidget)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector
from dialogs.range_editor_dialog import edit_target_range


class PassThroughConfig(BaseTagConfig):
    """Configuration for passThrough tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "passThrough")
        # Initialize pass through pieces list
        self.pass_through_pieces = []
        # Initialize range pattern data
        self.pass_through_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.pass_through_piece_pos = [4, 4]
        self.pass_through_checkbox_states = {}
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for pass through configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting pass through UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Info label matching old editor
            info_label = QLabel("Move through another unit's square. Use adjacency settings to define which pieces allow pass-through.")
            info_label.setWordWrap(True)
            info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
            layout.addWidget(info_label)

            # Enhanced inline piece selector for pass through pieces
            pass_through_selector = InlinePieceSelector(self.editor, "Pass Through Pieces", allow_costs=True)
            self.store_widget("pass_through_selector", pass_through_selector)
            layout.addWidget(pass_through_selector)

            # Pass through range configuration
            range_group = QGroupBox("Pass Through Range")
            range_layout = QVBoxLayout()

            range_info = QLabel("Define custom range pattern for pass through targeting:")
            range_info.setStyleSheet("color: #666; font-style: italic;")
            range_layout.addWidget(range_info)

            range_btn_layout = QHBoxLayout()
            pass_through_range_btn = QPushButton("Edit Pass Through Range")
            pass_through_range_btn.setToolTip("Define custom range pattern for pass through targeting")
            pass_through_range_btn.clicked.connect(self.edit_pass_through_range)
            self.store_widget("pass_through_range_btn", pass_through_range_btn)
            range_btn_layout.addWidget(pass_through_range_btn)
            range_btn_layout.addStretch()

            range_layout.addLayout(range_btn_layout)
            range_group.setLayout(range_layout)
            layout.addWidget(range_group)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Pass through UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def edit_pass_through_range(self):
        """Open the range editor dialog for pass through range configuration."""
        try:
            self.log_debug("Opening pass through range editor")

            pattern, piece_pos, checkbox_states = edit_target_range(
                initial_pattern=self.pass_through_pattern,
                piece_position=self.pass_through_piece_pos,
                title="Edit Pass Through Range",
                parent=self.editor,
                checkbox_states=self.pass_through_checkbox_states
            )

            if pattern is not None:
                self.pass_through_pattern = pattern
                self.pass_through_piece_pos = piece_pos
                self.pass_through_checkbox_states = checkbox_states
                self.log_debug("Pass through range updated successfully")

        except Exception as e:
            self.log_error(f"Error opening pass through range editor: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating pass through data")

            # Populate pass through pieces
            pass_through_selector = self.get_widget_by_name("pass_through_selector")
            if pass_through_selector and "passThroughPieces" in data:
                pass_through_selector.set_pieces(data["passThroughPieces"])

            # Load range pattern data
            if "passThroughRangePattern" in data:
                range_data = data["passThroughRangePattern"]
                if isinstance(range_data, dict):
                    self.pass_through_pattern = range_data.get("pattern", self.pass_through_pattern)
                    self.pass_through_piece_pos = range_data.get("piece_position", self.pass_through_piece_pos)
                    self.pass_through_checkbox_states = range_data.get("checkbox_states", {})

            self.log_debug("Pass through data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting pass through data")
            data = {}

            # Collect pass through pieces
            pass_through_selector = self.get_widget_by_name("pass_through_selector")
            if pass_through_selector:
                pieces = pass_through_selector.get_pieces()
                if pieces:
                    data["passThroughPieces"] = pieces

            # Collect range pattern data
            data["passThroughRangePattern"] = {
                "pattern": self.pass_through_pattern,
                "piece_position": self.pass_through_piece_pos,
                "checkbox_states": self.pass_through_checkbox_states
            }

            self.log_debug("Pass through data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
