"""
UI Utilities for Adventure Chess Creator

This module contains utility functions for UI creation extracted from ui_shared_components.py:
- create_section_header(): Create consistent section headers
- create_info_box(): Create styled info boxes
- create_legend_item(): Create legend items for grids
- create_dialog_buttons(): Create standard dialog buttons
- create_grid_instructions(): Create grid instruction text

These utilities provide consistent styling and behavior across all
UI components in the application.
"""

from typing import Tuple
from PyQt6.QtWidgets import QLabel, QPushButton


def create_section_header(title: str, description: str = "") -> QLabel:
    """Create a consistent section header"""
    if description:
        text = f"<h3>{title}</h3><p style='color: #666; margin-top: 5px;'>{description}</p>"
    else:
        text = f"<h3>{title}</h3>"
    
    label = QLabel(text)
    label.setStyleSheet("""
        QLabel {
            padding: 10px;
            background-color: palette(base);
            color: palette(text);
            border-left: 4px solid palette(highlight);
            margin-bottom: 10px;
        }
    """)
    return label


def create_info_box(message: str, box_type: str = "info") -> QLabel:
    """Create an info box with consistent styling"""
    colors = {
        "info": {"bg": "#d1ecf1", "border": "#bee5eb", "text": "#0c5460"},
        "success": {"bg": "#d4edda", "border": "#c3e6cb", "text": "#155724"},
        "warning": {"bg": "#fff3cd", "border": "#ffeaa7", "text": "#856404"},
        "error": {"bg": "#f8d7da", "border": "#f5c6cb", "text": "#721c24"}
    }

    color = colors.get(box_type, colors["info"])

    label = QLabel(message)
    label.setWordWrap(True)
    label.setStyleSheet(f"""
        QLabel {{
            padding: 12px;
            background-color: {color['bg']};
            color: {color['text']};
            border: 1px solid {color['border']};
            border-radius: 4px;
            margin: 5px 0;
        }}
    """)

    return label


def create_legend_item(color: str, border: str, label_text: str) -> Tuple[QPushButton, QLabel]:
    """Create a legend item with consistent styling"""
    btn = QPushButton()
    btn.setFixedSize(20, 20)
    btn.setStyleSheet(f"background: {color}; border: 1px solid {border};")
    btn.setEnabled(False)

    label = QLabel(label_text)
    return btn, label


def create_dialog_buttons(save_text: str = "Save", cancel_text: str = "Cancel") -> Tuple[QPushButton, QPushButton]:
    """Create standard dialog buttons with consistent styling"""
    cancel_btn = QPushButton(cancel_text)
    save_btn = QPushButton(save_text)
    save_btn.setDefault(True)

    return save_btn, cancel_btn


def create_grid_instructions(text: str) -> QLabel:
    """Create consistent grid instruction text"""
    instructions = QLabel(text)
    instructions.setWordWrap(True)
    instructions.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
    return instructions
