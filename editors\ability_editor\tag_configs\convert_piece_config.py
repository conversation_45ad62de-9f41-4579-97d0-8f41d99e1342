"""
ConvertPiece tag configuration for ability editor.
Handles convert piece ability configurations with target selection.
"""

from PyQt6.QtWidgets import (QFormLayout, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class ConvertPieceConfig(BaseTagConfig):
    """Configuration for convertPiece tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "convertPiece")
        # Initialize convert targets list
        self.convert_targets = []
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for convert piece configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting convert piece UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Turn enemy units into friendly ones.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Enhanced inline piece selector for conversion targets
            convert_target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("convert_target_selector", convert_target_selector)
            form_layout.addRow("", convert_target_selector)

            layout.addLayout(form_layout)
            
            # Additional info
            info_label = QLabel("Use the Range configuration to define targeting area.")
            layout.addWidget(info_label)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Convert piece UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating convert piece data")

            # Populate target pieces
            convert_target_selector = self.get_widget_by_name("convert_target_selector")
            if convert_target_selector and "convertTargets" in data:
                convert_target_selector.set_pieces(data["convertTargets"])

            self.log_debug("Convert piece data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting convert piece data")
            data = {}

            # Collect target pieces
            convert_target_selector = self.get_widget_by_name("convert_target_selector")
            if convert_target_selector:
                targets = convert_target_selector.get_pieces()
                if targets:
                    data["convertTargets"] = targets

            self.log_debug("Convert piece data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
