"""
Data Handlers for Adventure Chess Creator

This module contains specialized data handlers that extend the base
data handler functionality for specific data types.

Classes:
- EnhancedPieceDataHandler: Specialized handler for piece data
- EnhancedAbilityDataHandler: Specialized handler for ability data
"""

from .specialized_data_handlers import EnhancedPieceDataHandler, EnhancedAbilityDataHandler

__all__ = [
    'EnhancedPieceDataHandler',
    'EnhancedAbilityDataHandler'
]
