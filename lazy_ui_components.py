#!/usr/bin/env python3
"""
Lazy Loading UI Components for Adventure Chess Creator
Enhanced UI components that support lazy loading with progress indicators
"""

import os
import sys
import logging
from typing import Dict, Any, Optional, List, Callable
from PyQt6.QtCore import QObject, pyqtSignal, QTimer, Qt
from PyQt6.QtWidgets import (
    QComboBox, QListWidget, QListWidgetItem, QWidget, QVBoxLayout, 
    QHBoxLayout, QLabel, QPushButton, QProgressBar, QFrame
)

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lazy_loading_system import LoadingProgressWidget
from lazy_data_integration import LazyFileListManager, get_lazy_data_manager

logger = logging.getLogger(__name__)

class LazyComboBox(QComboBox):
    """
    Enhanced combo box with lazy loading support and progress indication
    """
    
    item_selected_lazy = pyqtSignal(str, dict)  # filename, data
    loading_started = pyqtSignal(str)  # filename
    loading_finished = pyqtSignal(str)  # filename
    
    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension
        
        # Lazy loading components
        self.file_manager = LazyFileListManager(directory, file_extension, self)
        self.file_manager.register_combo_box(self)
        self.file_manager.files_loaded.connect(self._on_files_loaded)
        self.file_manager.file_data_loaded.connect(self._on_file_data_loaded)
        
        # Loading state
        self.is_loading_files = False
        self.currently_loading_file: Optional[str] = None
        
        # Connect selection change
        self.currentTextChanged.connect(self._on_selection_changed)
        
        # Start loading file list
        self._load_file_list()
    
    def _load_file_list(self):
        """Start loading file list in background"""
        if not self.is_loading_files:
            self.is_loading_files = True
            self.addItem("🔄 Loading files...")
            self.file_manager.load_file_list_lazy()
    
    def _on_files_loaded(self, metadata_list: List[Dict[str, Any]]):
        """Handle file list loaded"""
        self.is_loading_files = False
        # UI will be updated by file_manager
    
    def _on_selection_changed(self, text: str):
        """Handle selection change with lazy loading"""
        if not text or text.startswith("🔄") or text.startswith("Select"):
            return
        
        # Get filename from item data
        current_index = self.currentIndex()
        if current_index <= 0:  # Skip "Select file..." item
            return
        
        filename = self.itemData(current_index)
        if not filename:
            return
        
        # Check if already loaded
        data_manager = get_lazy_data_manager()
        if not data_manager.is_loading(filename):
            self.currently_loading_file = filename
            self.loading_started.emit(filename)
            
            # Load file data lazily
            def on_loaded(data):
                self.currently_loading_file = None
                self.loading_finished.emit(filename)
                self.item_selected_lazy.emit(filename, data)
            
            if 'piece' in self.directory.lower():
                data_manager.load_piece_lazy(filename, on_loaded, priority=5)
            else:
                data_manager.load_ability_lazy(filename, on_loaded, priority=5)
    
    def _on_file_data_loaded(self, filename: str, data: Dict[str, Any]):
        """Handle individual file data loaded"""
        if filename == self.currently_loading_file:
            self.currently_loading_file = None
            self.loading_finished.emit(filename)
            self.item_selected_lazy.emit(filename, data)
    
    def refresh_files(self):
        """Refresh file list"""
        self.file_manager.metadata_loaded = False
        self._load_file_list()

class LazyListWidget(QListWidget):
    """
    Enhanced list widget with lazy loading support
    """
    
    item_selected_lazy = pyqtSignal(str, dict)  # filename, data
    loading_started = pyqtSignal(str)  # filename
    loading_finished = pyqtSignal(str)  # filename
    
    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension
        
        # Lazy loading components
        self.file_manager = LazyFileListManager(directory, file_extension, self)
        self.file_manager.register_list_widget(self)
        self.file_manager.files_loaded.connect(self._on_files_loaded)
        self.file_manager.file_data_loaded.connect(self._on_file_data_loaded)
        
        # Loading state
        self.is_loading_files = False
        self.currently_loading_files: List[str] = []
        
        # Connect selection change
        self.itemClicked.connect(self._on_item_clicked)
        
        # Start loading file list
        self._load_file_list()
    
    def _load_file_list(self):
        """Start loading file list in background"""
        if not self.is_loading_files:
            self.is_loading_files = True
            loading_item = QListWidgetItem("🔄 Loading files...")
            self.addItem(loading_item)
            self.file_manager.load_file_list_lazy()
    
    def _on_files_loaded(self, metadata_list: List[Dict[str, Any]]):
        """Handle file list loaded"""
        self.is_loading_files = False
        # UI will be updated by file_manager
    
    def _on_item_clicked(self, item: QListWidgetItem):
        """Handle item click with lazy loading"""
        filename = item.data(0x0100)  # UserRole
        if not filename:
            return
        
        # Check if already loaded
        data_manager = get_lazy_data_manager()
        if filename not in self.currently_loading_files and not data_manager.is_loading(filename):
            self.currently_loading_files.append(filename)
            self.loading_started.emit(filename)
            
            # Update item to show loading
            item.setText(f"🔄 {item.text().replace('📄 ', '').replace('✅ ', '').replace('❌ ', '')}")
            
            # Load file data lazily
            def on_loaded(data):
                if filename in self.currently_loading_files:
                    self.currently_loading_files.remove(filename)
                self.loading_finished.emit(filename)
                self.item_selected_lazy.emit(filename, data)
            
            if 'piece' in self.directory.lower():
                data_manager.load_piece_lazy(filename, on_loaded, priority=5)
            else:
                data_manager.load_ability_lazy(filename, on_loaded, priority=5)
    
    def _on_file_data_loaded(self, filename: str, data: Dict[str, Any]):
        """Handle individual file data loaded"""
        if filename in self.currently_loading_files:
            self.currently_loading_files.remove(filename)
            self.loading_finished.emit(filename)
            self.item_selected_lazy.emit(filename, data)
    
    def refresh_files(self):
        """Refresh file list"""
        self.file_manager.metadata_loaded = False
        self._load_file_list()

class LazyLoadingPanel(QWidget):
    """
    Panel that shows loading progress and provides controls for lazy loading
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = get_lazy_data_manager()
        self.setup_ui()
        self.setup_connections()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # Update every second
    
    def setup_ui(self):
        """Setup the UI components"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Lazy Loading Status")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # Status frame
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ecf0f1;
                padding: 5px;
            }
        """)
        status_layout = QVBoxLayout()
        
        # Status labels
        self.pending_label = QLabel("Pending requests: 0")
        self.active_label = QLabel("Active callbacks: 0")
        self.cache_label = QLabel("Cache entries: 0")
        
        status_layout.addWidget(self.pending_label)
        status_layout.addWidget(self.active_label)
        status_layout.addWidget(self.cache_label)
        
        status_frame.setLayout(status_layout)
        layout.addWidget(status_frame)
        
        # Progress widget
        self.progress_widget = LoadingProgressWidget()
        self.progress_widget.hide()
        layout.addWidget(self.progress_widget)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.preload_btn = QPushButton("🚀 Preload Recent Files")
        self.preload_btn.clicked.connect(self.start_preloading)
        self.preload_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        self.clear_cache_btn = QPushButton("🗑️ Clear Cache")
        self.clear_cache_btn.clicked.connect(self.clear_cache)
        self.clear_cache_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        button_layout.addWidget(self.preload_btn)
        button_layout.addWidget(self.clear_cache_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.setLayout(layout)
    
    def setup_connections(self):
        """Setup signal connections"""
        # Add progress callback to data manager
        self.data_manager.add_progress_callback(self.on_progress_update)
    
    def update_status(self):
        """Update status display"""
        try:
            # Get loading status
            status = self.data_manager.get_loading_status()
            self.pending_label.setText(f"Pending requests: {status['pending_requests']}")
            self.active_label.setText(f"Active callbacks: {status['active_callbacks']}")
            
            # Get cache status
            cache_stats = self.data_manager.cache_manager.get_cache_stats()
            total_entries = cache_stats['entries']['total']
            self.cache_label.setText(f"Cache entries: {total_entries}")
            
        except Exception as e:
            logger.error(f"Error updating status: {e}")
    
    def on_progress_update(self, progress: int, message: str):
        """Handle progress updates"""
        if progress == 0:
            self.progress_widget.show_loading(message)
        elif progress == 100:
            self.progress_widget.hide_loading()
        else:
            self.progress_widget.update_progress(progress, message)
    
    def start_preloading(self):
        """Start preloading recent files"""
        try:
            self.preload_btn.setEnabled(False)
            self.progress_widget.show_loading("Starting preload...")
            
            # Start preloading
            self.data_manager.preload_recent_files(max_files=15)
            
            # Re-enable button after a delay
            QTimer.singleShot(2000, lambda: self.preload_btn.setEnabled(True))
            
        except Exception as e:
            logger.error(f"Error starting preload: {e}")
            self.preload_btn.setEnabled(True)
            self.progress_widget.hide_loading()
    
    def clear_cache(self):
        """Clear the cache"""
        try:
            self.data_manager.cache_manager.clear_all()
            self.update_status()
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")

class LazyFileSelector(QWidget):
    """
    Complete file selector widget with lazy loading and progress indication
    """
    
    file_selected = pyqtSignal(str, dict)  # filename, data
    
    def __init__(self, directory: str, title: str = "Select File", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.title = title
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the UI"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 2px;
            }
        """)
        layout.addWidget(title_label)
        
        # Lazy combo box
        self.combo = LazyComboBox(self.directory)
        self.combo.item_selected_lazy.connect(self.file_selected)
        layout.addWidget(self.combo)
        
        # Loading indicator (initially hidden)
        self.loading_widget = LoadingProgressWidget()
        self.loading_widget.hide()
        layout.addWidget(self.loading_widget)
        
        # Connect loading signals
        self.combo.loading_started.connect(lambda f: self.loading_widget.show_loading(f"Loading {f}..."))
        self.combo.loading_finished.connect(lambda f: self.loading_widget.hide_loading())
        
        self.setLayout(layout)
    
    def refresh(self):
        """Refresh the file list"""
        self.combo.refresh_files()

    def get_selected_filename(self) -> Optional[str]:
        """Get currently selected filename"""
        current_index = self.combo.currentIndex()
        if current_index <= 0:
            return None
        return self.combo.itemData(current_index)
