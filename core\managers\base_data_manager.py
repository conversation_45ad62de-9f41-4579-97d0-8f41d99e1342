"""
Base Data Manager for Adventure Chess Creator

This module provides a unified base class for all data managers in the application,
establishing clear data ownership patterns and single source of truth principles.

The BaseDataManager provides:
- Clear data ownership and responsibility assignment
- Standardized data access patterns
- Consistent state management across all data types
- Integration with the new base class architecture
"""

import logging
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseDataManager(ABC):
    """
    Base class for all data managers in Adventure Chess Creator.
    
    Establishes clear data ownership patterns and provides standardized
    data access methods to eliminate redundant data sources.
    """
    
    def __init__(self, data_type: str):
        """
        Initialize the base data manager.
        
        Args:
            data_type: Type of data being managed ('piece', 'ability', 'movement', etc.)
        """
        self.data_type = data_type
        self._data_store = {}
        self._change_listeners = []
        
    # ========== DATA OWNERSHIP PATTERNS ==========
    
    @abstractmethod
    def get_data_owner(self) -> str:
        """
        Return the canonical owner of this data type.
        
        Returns:
            String identifying the component that owns this data
        """
        pass
    
    @abstractmethod
    def get_data_access_pattern(self) -> str:
        """
        Return the access pattern for this data type.
        
        Returns:
            String describing how this data should be accessed
        """
        pass
    
    # ========== SINGLE SOURCE OF TRUTH METHODS ==========
    
    def set_data(self, key: str, value: Any) -> None:
        """
        Set data value with change notification.
        
        Args:
            key: Data key
            value: Data value
        """
        old_value = self._data_store.get(key)
        self._data_store[key] = value
        
        # Notify listeners of change
        self._notify_change(key, old_value, value)
        
        logger.debug(f"{self.data_type} data updated: {key}")
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """
        Get data value from single source of truth.
        
        Args:
            key: Data key
            default: Default value if key not found
            
        Returns:
            Data value or default
        """
        return self._data_store.get(key, default)
    
    def get_all_data(self) -> Dict[str, Any]:
        """
        Get all data from single source of truth.
        
        Returns:
            Complete data dictionary
        """
        return self._data_store.copy()
    
    def clear_data(self) -> None:
        """Clear all data and notify listeners."""
        old_data = self._data_store.copy()
        self._data_store.clear()
        
        # Notify listeners of clear
        for key, value in old_data.items():
            self._notify_change(key, value, None)
        
        logger.debug(f"{self.data_type} data cleared")
    
    def has_data(self, key: str) -> bool:
        """
        Check if data key exists.
        
        Args:
            key: Data key to check
            
        Returns:
            True if key exists, False otherwise
        """
        return key in self._data_store
    
    # ========== CHANGE TRACKING ==========
    
    def add_change_listener(self, listener_func) -> None:
        """
        Add a change listener function.
        
        Args:
            listener_func: Function to call on data changes (key, old_value, new_value)
        """
        if listener_func not in self._change_listeners:
            self._change_listeners.append(listener_func)
    
    def remove_change_listener(self, listener_func) -> None:
        """
        Remove a change listener function.
        
        Args:
            listener_func: Function to remove from listeners
        """
        if listener_func in self._change_listeners:
            self._change_listeners.remove(listener_func)
    
    def _notify_change(self, key: str, old_value: Any, new_value: Any) -> None:
        """
        Notify all listeners of data change.
        
        Args:
            key: Data key that changed
            old_value: Previous value
            new_value: New value
        """
        for listener in self._change_listeners:
            try:
                listener(key, old_value, new_value)
            except Exception as e:
                logger.warning(f"Error in change listener: {e}")
    
    # ========== DATA VALIDATION ==========
    
    def validate_data(self, data: Dict[str, Any]) -> List[str]:
        """
        Validate data consistency - override in subclasses.
        
        Args:
            data: Data to validate
            
        Returns:
            List of validation error messages
        """
        return []
    
    def is_data_valid(self, data: Dict[str, Any]) -> bool:
        """
        Check if data is valid.
        
        Args:
            data: Data to check
            
        Returns:
            True if valid, False otherwise
        """
        return len(self.validate_data(data)) == 0
    
    # ========== STATE MANAGEMENT ==========
    
    def get_state_summary(self) -> Dict[str, Any]:
        """
        Get summary of current state for debugging.
        
        Returns:
            Dictionary with state information
        """
        return {
            'data_type': self.data_type,
            'data_owner': self.get_data_owner(),
            'access_pattern': self.get_data_access_pattern(),
            'data_keys': list(self._data_store.keys()),
            'listener_count': len(self._change_listeners)
        }


class PieceDataManager(BaseDataManager):
    """Data manager for piece-specific data with movement consolidation."""
    
    def __init__(self):
        super().__init__("piece")
    
    def get_data_owner(self) -> str:
        return "PieceEditor.current_data"
    
    def get_data_access_pattern(self) -> str:
        return "Direct access through PieceEditor instance, movement data via current_movement_data property"


class AbilityDataManager(BaseDataManager):
    """Data manager for ability-specific data with tag integration."""
    
    def __init__(self):
        super().__init__("ability")
    
    def get_data_owner(self) -> str:
        return "AbilityEditor.current_data"
    
    def get_data_access_pattern(self) -> str:
        return "Direct access through AbilityEditor instance, tag data via tag_manager"


class MovementDataManager(BaseDataManager):
    """Data manager for movement-specific data consolidation."""
    
    def __init__(self):
        super().__init__("movement")
    
    def get_data_owner(self) -> str:
        return "PieceEditor.current_movement_data"
    
    def get_data_access_pattern(self) -> str:
        return "Consolidated access through current_movement_data, legacy properties redirect to this source"
