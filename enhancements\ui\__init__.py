"""
Comprehensive UI Enhancement Package for Adventure Chess Creator

This package provides consolidated UI enhancements including:
- Visual feedback system with real-time validation
- Comprehensive error handling with user-friendly dialogs
- Enhanced search components and validation rules
- Integration with existing UI components

Key Modules:
- visual_feedback_system: Visual feedback, loading indicators, validation widgets
- error_handling_system: Error handling, user-friendly dialogs, recovery mechanisms
- ui_components: Search components, validation rules, UI utilities

Usage:
    from enhancements.ui import (
        get_visual_feedback_manager,
        apply_visual_feedback_to_editor,
        show_user_friendly_error,
        EnhancedSearchWidget,
        ValidationRules
    )
"""

# Visual Feedback System exports
from .visual_feedback_system import (
    # Core visual feedback classes
    ColorSchemes,
    VisualFeedbackIntegrator,
    EnhancedLoadingIndicator,
    RealTimeValidationWidget,
    OperationFeedbackManager,
    StatusBarEnhancement,
    EnhancedGridVisualizer,
    
    # Validation rule creators
    create_validation_rules_for_piece_editor,
    create_validation_rules_for_ability_editor,
    
    # Visual feedback manager
    VisualFeedbackManager,
    get_visual_feedback_manager,
    reset_visual_feedback_manager,
    
    # Convenience functions
    apply_visual_feedback_to_editor,
    apply_visual_feedback_to_main_window,
)

# Error Handling System exports
from .error_handling_system import (
    # Core error handling classes
    ErrorSeverity,
    ErrorCategory,
    ErrorContext,
    EnhancedErrorHandler,
    
    # Data manager integration
    EnhancedDirectDataManager,
    
    # User-friendly error system
    UserFriendlyError,
    ErrorMessageTranslator,
    UserFriendlyErrorDialog,
    
    # Convenience functions
    safe_file_load,
    safe_file_save,
    show_user_friendly_error,
    
    # Global error handler
    error_handler,
)

# UI Components exports
from .ui_components import (
    # Search components
    SearchWorker,
    EnhancedSearchWidget,
    SearchResultsWidget,
    FileIndexBrowser,
    
    # Validation system
    ValidationRules,
    EnhancedValidationMixin,
    create_piece_validation_rules,
    create_ability_validation_rules,
)

# Package version
__version__ = "1.0.0"

# All exports for convenience
__all__ = [
    # Visual Feedback System
    "ColorSchemes",
    "VisualFeedbackIntegrator", 
    "EnhancedLoadingIndicator",
    "RealTimeValidationWidget",
    "OperationFeedbackManager",
    "StatusBarEnhancement",
    "EnhancedGridVisualizer",
    "create_validation_rules_for_piece_editor",
    "create_validation_rules_for_ability_editor",
    "VisualFeedbackManager",
    "get_visual_feedback_manager",
    "reset_visual_feedback_manager",
    "apply_visual_feedback_to_editor",
    "apply_visual_feedback_to_main_window",
    
    # Error Handling System
    "ErrorSeverity",
    "ErrorCategory", 
    "ErrorContext",
    "EnhancedErrorHandler",
    "EnhancedDirectDataManager",
    "UserFriendlyError",
    "ErrorMessageTranslator",
    "UserFriendlyErrorDialog",
    "safe_file_load",
    "safe_file_save",
    "show_user_friendly_error",
    "error_handler",
    
    # UI Components
    "SearchWorker",
    "EnhancedSearchWidget",
    "SearchResultsWidget", 
    "FileIndexBrowser",
    "ValidationRules",
    "EnhancedValidationMixin",
    "create_piece_validation_rules",
    "create_ability_validation_rules",
]
