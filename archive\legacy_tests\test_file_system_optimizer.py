"""
Test Suite for File System Optimizer

This module tests all aspects of the file system optimization including:
- File indexing functionality
- Search performance and accuracy
- Directory scanning optimization
- File compression features
- Index management and statistics
"""

import unittest
import tempfile
import shutil
import json
import os
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add parent directory to path for imports
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enhancements.performance import FileSystemOptimizer, SearchResult, reset_optimizer

class TestFileSystemOptimizer(unittest.TestCase):
    """Test the file system optimizer functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for testing
        self.test_dir = tempfile.mkdtemp()
        self.pieces_dir = os.path.join(self.test_dir, "pieces")
        self.abilities_dir = os.path.join(self.test_dir, "abilities")
        self.index_db = os.path.join(self.test_dir, "test_index.db")
        
        os.makedirs(self.pieces_dir)
        os.makedirs(self.abilities_dir)
        
        # Create test files
        self._create_test_files()
        
        # Initialize optimizer
        self.optimizer = FileSystemOptimizer(
            index_db_path=self.index_db,
            enable_compression=True,
            compression_threshold_kb=1,
            index_update_interval=3600  # Disable auto-update for tests
        )
    
    def tearDown(self):
        """Clean up test environment"""
        try:
            self.optimizer.shutdown()
            shutil.rmtree(self.test_dir)
            reset_optimizer()
        except Exception as e:
            print(f"Cleanup error: {e}")
    
    def _create_test_files(self):
        """Create test piece and ability files"""
        # Test pieces
        pieces = [
            {
                "name": "Test King",
                "description": "A powerful royal piece",
                "version": "1.0.0",
                "tags": ["royal", "powerful"],
                "abilities": {"teleport": {}, "summon": {}},
                "movement": {"type": "king"}
            },
            {
                "name": "Magic Knight",
                "description": "A knight with magical abilities",
                "version": "1.1.0",
                "tags": ["magic", "cavalry"],
                "abilities": {"magic_bolt": {}, "charge": {}},
                "movement": {"type": "knight"}
            },
            {
                "name": "Fire Dragon",
                "description": "A legendary dragon piece",
                "version": "2.0.0",
                "tags": ["legendary", "fire", "dragon"],
                "abilities": {"fire_breath": {}, "fly": {}},
                "movement": {"type": "custom"}
            }
        ]
        
        for i, piece in enumerate(pieces):
            filename = f"test_piece_{i+1}.json"
            with open(os.path.join(self.pieces_dir, filename), 'w') as f:
                json.dump(piece, f, indent=2)
        
        # Test abilities
        abilities = [
            {
                "name": "Teleport",
                "description": "Instantly move to any square",
                "version": "1.0.0",
                "tags": ["movement", "instant"],
                "range": 99,
                "cost": 3
            },
            {
                "name": "Magic Bolt",
                "description": "Ranged magical attack",
                "version": "1.0.0",
                "tags": ["attack", "magic", "ranged"],
                "range": 5,
                "damage": 2
            },
            {
                "name": "Fire Breath",
                "description": "Area of effect fire attack",
                "version": "1.5.0",
                "tags": ["attack", "fire", "aoe"],
                "range": 3,
                "damage": 4
            }
        ]
        
        for i, ability in enumerate(abilities):
            filename = f"test_ability_{i+1}.json"
            with open(os.path.join(self.abilities_dir, filename), 'w') as f:
                json.dump(ability, f, indent=2)
    
    def test_index_initialization(self):
        """Test that the index database is properly initialized"""
        self.assertTrue(os.path.exists(self.index_db))
        
        # Check that tables exist
        import sqlite3
        with sqlite3.connect(self.index_db) as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('file_index', 'file_search')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            self.assertIn('file_index', tables)
            self.assertIn('file_search', tables)
    
    def test_file_indexing(self):
        """Test file indexing functionality"""
        # Index the test directories
        indexed_count = self.optimizer.update_index([self.pieces_dir, self.abilities_dir])
        
        # Should have indexed 6 files (3 pieces + 3 abilities)
        self.assertEqual(indexed_count, 6)
        
        # Check index statistics
        stats = self.optimizer.get_index_statistics()
        self.assertEqual(stats['total_files'], 6)
        self.assertIn('piece', stats['by_type'])
        self.assertIn('ability', stats['by_type'])
        self.assertEqual(stats['by_type']['piece']['count'], 3)
        self.assertEqual(stats['by_type']['ability']['count'], 3)
    
    def test_search_functionality(self):
        """Test search functionality"""
        # Index files first
        self.optimizer.update_index([self.pieces_dir, self.abilities_dir])
        
        # Test name search
        results = self.optimizer.search_files("Test King")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].filename, "test_piece_1")
        self.assertGreater(results[0].relevance_score, 0)
        
        # Test description search
        results = self.optimizer.search_files("magical")
        self.assertGreater(len(results), 0)
        
        # Test tag search
        results = self.optimizer.search_files("fire")
        fire_results = [r for r in results if "fire" in r.metadata.get('tags', [])]
        self.assertGreater(len(fire_results), 0)
        
        # Test file type filtering
        piece_results = self.optimizer.search_files("", file_type="piece")
        self.assertEqual(len(piece_results), 3)
        for result in piece_results:
            self.assertEqual(result.file_type, "piece")
        
        ability_results = self.optimizer.search_files("", file_type="ability")
        self.assertEqual(len(ability_results), 3)
        for result in ability_results:
            self.assertEqual(result.file_type, "ability")
    
    def test_search_suggestions(self):
        """Test search suggestion functionality"""
        # Index files first
        self.optimizer.update_index([self.pieces_dir, self.abilities_dir])

        # Test suggestions - check that we get some results
        suggestions = self.optimizer.get_file_suggestions("test", limit=10)
        self.assertGreater(len(suggestions), 0)

        # Check that piece files are included
        piece_suggestions = self.optimizer.get_file_suggestions("test", file_type="piece", limit=5)
        self.assertGreater(len(piece_suggestions), 0)

        # Check that ability files are included
        ability_suggestions = self.optimizer.get_file_suggestions("test", file_type="ability", limit=5)
        self.assertGreater(len(ability_suggestions), 0)

        # Test name-based suggestions
        suggestions = self.optimizer.get_file_suggestions("Magic", limit=5)
        magic_suggestions = [s for s in suggestions if "magic" in s.lower()]
        self.assertGreater(len(magic_suggestions), 0)
    
    def test_directory_scanning_optimization(self):
        """Test directory scanning optimization"""
        # First scan (should be uncached)
        result1 = self.optimizer.optimize_directory_scanning(self.pieces_dir)
        self.assertFalse(result1['cache_hit'])
        self.assertEqual(result1['file_count'], 3)
        self.assertGreater(result1['scan_time_ms'], 0)
        
        # Second scan (should be cached)
        result2 = self.optimizer.optimize_directory_scanning(self.pieces_dir)
        self.assertTrue(result2['cache_hit'])
        self.assertEqual(result2['file_count'], 3)
        
        # Cache should make it faster
        self.assertLessEqual(result2['scan_time_ms'], result1['scan_time_ms'])
    
    def test_file_compression(self):
        """Test file compression functionality"""
        # Create a larger test file
        large_data = {
            "name": "Large Test Piece",
            "description": "A" * 1000,  # Large description
            "version": "1.0.0",
            "large_data": ["item"] * 100
        }
        
        large_file = os.path.join(self.pieces_dir, "large_test.json")
        with open(large_file, 'w') as f:
            json.dump(large_data, f, indent=2)
        
        # Index the file
        self.optimizer.update_index([self.pieces_dir])
        
        # Test compression
        result = self.optimizer.compress_large_files(threshold_kb=1)
        
        # Should have compressed at least one file
        self.assertGreaterEqual(result['compressed_files'], 0)
        if result['compressed_files'] > 0:
            self.assertGreater(result['space_saved_bytes'], 0)
    
    def test_performance_tracking(self):
        """Test search performance tracking"""
        # Index files first
        self.optimizer.update_index([self.pieces_dir, self.abilities_dir])
        
        # Perform several searches
        for query in ["Test", "Magic", "Fire", "Dragon"]:
            self.optimizer.search_files(query)
        
        # Check statistics
        stats = self.optimizer.get_index_statistics()
        search_stats = stats['search_stats']
        
        self.assertGreaterEqual(search_stats['total_searches'], 4)
        self.assertGreater(search_stats['avg_search_time_ms'], 0)
    
    def test_index_updates(self):
        """Test index update detection"""
        # Initial index
        self.optimizer.update_index([self.pieces_dir, self.abilities_dir])
        
        # Modify a file
        test_file = os.path.join(self.pieces_dir, "test_piece_1.json")
        with open(test_file, 'r') as f:
            data = json.load(f)
        
        data['description'] = "Modified description"
        
        # Wait a bit to ensure different timestamp
        time.sleep(0.1)
        
        with open(test_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        # Update index again
        indexed_count = self.optimizer.update_index([self.pieces_dir, self.abilities_dir])
        
        # Should have re-indexed the modified file
        self.assertGreaterEqual(indexed_count, 1)
        
        # Search should find the modified content
        results = self.optimizer.search_files("Modified description")
        self.assertGreater(len(results), 0)
    
    def test_error_handling(self):
        """Test error handling in various scenarios"""
        # Test with non-existent directory
        result = self.optimizer.optimize_directory_scanning("/non/existent/path")
        self.assertEqual(result['file_count'], 0)
        self.assertFalse(result['cache_hit'])
        
        # Test search with empty query
        results = self.optimizer.search_files("")
        self.assertIsInstance(results, list)
        
        # Test suggestions with empty query
        suggestions = self.optimizer.get_file_suggestions("")
        self.assertIsInstance(suggestions, list)

class TestSearchPerformance(unittest.TestCase):
    """Test search performance with larger datasets"""
    
    def setUp(self):
        """Set up performance test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.pieces_dir = os.path.join(self.test_dir, "pieces")
        self.index_db = os.path.join(self.test_dir, "perf_index.db")
        
        os.makedirs(self.pieces_dir)
        
        # Create many test files for performance testing
        self._create_large_dataset()
        
        self.optimizer = FileSystemOptimizer(
            index_db_path=self.index_db,
            index_update_interval=3600
        )
    
    def tearDown(self):
        """Clean up performance test environment"""
        try:
            self.optimizer.shutdown()
            shutil.rmtree(self.test_dir)
            reset_optimizer()
        except Exception as e:
            print(f"Performance test cleanup error: {e}")
    
    def _create_large_dataset(self):
        """Create a larger dataset for performance testing"""
        # Create 50 test files
        for i in range(50):
            piece_data = {
                "name": f"Performance Test Piece {i}",
                "description": f"This is test piece number {i} for performance testing",
                "version": "1.0.0",
                "tags": [f"tag{i%5}", f"category{i%3}", "performance"],
                "abilities": {f"ability_{i}": {}, f"special_{i%10}": {}},
                "test_data": f"data_{i}" * 10
            }
            
            filename = f"perf_piece_{i:03d}.json"
            with open(os.path.join(self.pieces_dir, filename), 'w') as f:
                json.dump(piece_data, f, indent=2)
    
    def test_indexing_performance(self):
        """Test indexing performance with larger dataset"""
        start_time = time.time()
        indexed_count = self.optimizer.update_index([self.pieces_dir])
        index_time = time.time() - start_time
        
        self.assertEqual(indexed_count, 50)
        self.assertLess(index_time, 5.0)  # Should index 50 files in under 5 seconds
        
        print(f"Indexed {indexed_count} files in {index_time:.3f} seconds")
    
    def test_search_performance(self):
        """Test search performance with larger dataset"""
        # Index files first
        self.optimizer.update_index([self.pieces_dir])
        
        # Test search performance
        search_queries = ["Performance", "Test", "Piece", "tag1", "ability_25"]
        
        total_search_time = 0
        for query in search_queries:
            start_time = time.time()
            results = self.optimizer.search_files(query, max_results=20)
            search_time = time.time() - start_time
            total_search_time += search_time
            
            self.assertLessEqual(search_time, 0.1)  # Each search should be under 100ms
            self.assertGreater(len(results), 0)
        
        avg_search_time = total_search_time / len(search_queries)
        print(f"Average search time: {avg_search_time*1000:.2f}ms")
        
        # Check statistics
        stats = self.optimizer.get_index_statistics()
        self.assertGreaterEqual(stats['search_stats']['total_searches'], len(search_queries))

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
