"""
Main Piece Editor Window for Adventure Chess Creator

This module contains the main PieceEditorWindow class that coordinates
all the refactored piece editor components:
- Data handling through PieceDataHandler
- UI components through PieceUIComponents  
- Movement management through PieceMovementManager
- Promotion management through PiecePromotionManager
- Icon management through PieceIconManager

This is the main coordinator that ties all modules together while
maintaining the same interface as the original monolithic editor.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import QWidget, QMessageBox, QFileDialog, QListWidgetItem, QHBoxLayout, QLabel
from PyQt6.QtCore import Qt
from utils.simple_bridge import simple_bridge

# Local imports
from config import PIECES_DIR, PIECE_EDITOR_DEFAULT, PIECE_EDITOR_MIN, PIECE_EDITOR_SMALL_SCREEN
from core.base_classes.base_editor import BaseEditor
from ui.ui_utils import setup_responsive_window_with_fallback, ResponsiveScrollArea
from utils.simple_bridge import simple_bridge

# Import refactored components
from .piece_data_handlers import <PERSON>Data<PERSON>andler
from .piece_ui_components import <PERSON><PERSON><PERSON>om<PERSON>
from .piece_movement_manager import PieceMovementManager
from .piece_promotion_manager import PiecePromotionManager
from .piece_icon_manager import PieceIconManager

# Import improved error handling
from error_message_improvements import show_improved_error, show_improved_warning, show_improved_info

logger = logging.getLogger(__name__)


class PieceEditorWindow(BaseEditor):
    """
    Main Piece Editor Window - Coordinates all refactored components.
    
    This class serves as the main coordinator for the piece editor,
    integrating all the specialized managers and handlers while
    maintaining the same interface as the original editor.
    """

    def __init__(self):
        """Initialize the piece editor with refactored components."""
        # Initialize base editor with piece data type
        super().__init__("piece")

        # Initialize movement-related state
        self.current_custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.custom_pattern_piece_pos = [3, 3]  # Default piece position in center
        self.selected_movement_type = "orthogonal"  # Default movement type
        self.current_movement_data = {
            "type": "orthogonal", 
            "pattern": None, 
            "piecePosition": [3, 3]
        }
        
        # Initialize promotion lists (will be managed by promotion manager)
        self.primary_promotions = []
        self.secondary_promotions = []
        
        # Initialize abilities list
        self.abilities = []  # List of ability file references

        # Initialize change tracking
        self.unsaved_changes = False

        self.setWindowTitle("Piece Editor - Adventure Chess")
        
        # Setup responsive window with small screen fallback
        setup_responsive_window_with_fallback(
            self,
            PIECE_EDITOR_DEFAULT,
            PIECE_EDITOR_MIN,
            PIECE_EDITOR_SMALL_SCREEN
        )
        
        # Initialize refactored components
        self.data_handler = PieceDataHandler(self)
        self.ui_components = PieceUIComponents(self)
        self.movement_manager = PieceMovementManager(self)
        self.promotion_manager = PiecePromotionManager(self)
        self.icon_manager = PieceIconManager(self)
        
        # Initialize UI and reset form
        self.init_ui()
        self.reset_form()
        
        logger.info("Piece editor initialized successfully")
    
    def init_ui(self) -> None:
        """Initialize the user interface using the UI components handler."""
        try:
            central_widget = self.ui_components.init_main_ui()
            self.setCentralWidget(central_widget)
            
            # Setup change tracking
            self.setup_change_tracking()
            
            # Refresh file lists
            self.refresh_file_lists()

            # Initialize promotion data
            self.primary_promotions = []
            self.secondary_promotions = []

            # Initialize promotion piece combo box
            self.refresh_promotion_piece_combo()

        except Exception as e:
            logger.error(f"Error initializing UI: {e}")
            raise
    
    # ========== BASEEDITOR ABSTRACT METHOD IMPLEMENTATIONS ==========
    
    def reset_form(self) -> None:
        """Reset the piece editor form to default state (BaseEditor abstract method)."""
        try:
            logger.info("Resetting piece editor form...")

            # Create default piece structure
            default_piece = self.data_handler.create_default_piece_data()

            # Reset all managers to default state
            self.movement_manager.reset_movement_to_default()
            self.promotion_manager.reset_promotions()
            self.icon_manager.reset_icons()

            # Use BaseEditor's standardized widget setting
            self.set_widget_values_from_data(default_piece)

            # Update UI components
            self.update_all_ui_components()

            # Clear filename and mark as saved
            self.current_filename = None
            self.mark_saved()

            # Populate quick load dropdown
            self.refresh_quick_load_dropdown()

            logger.info("Piece editor form reset completed")

        except Exception as e:
            logger.error(f"Error resetting form: {e}")
            raise

    def collect_form_data(self) -> Dict[str, Any]:
        """Collect all form data into a dictionary (BaseEditor abstract method)."""
        try:
            # Get base form data using BaseEditor's method
            form_data = self.collect_widget_data()
            
            # Add movement data
            movement_data = self.movement_manager.get_movement_data()
            form_data["movement"] = movement_data
            
            # Add promotion data
            promotion_data = self.promotion_manager.get_promotion_data()
            form_data.update(promotion_data)
            
            # Add icon data
            icon_data = self.icon_manager.get_icon_data()
            form_data.update(icon_data)
            
            # Add abilities list
            form_data["abilities"] = self.abilities.copy()
            
            logger.debug(f"Collected form data with {len(form_data)} fields")
            return form_data
            
        except Exception as e:
            logger.error(f"Error collecting form data: {e}")
            raise

    def populate_form_data(self, data: Dict[str, Any]) -> None:
        """Populate form with data (BaseEditor abstract method)."""
        try:
            logger.info(f"Populating form with {len(data)} fields...")
            
            # Use data handler to load the data
            self.data_handler.load_piece_data(data)
            
            # Load promotion data
            self.promotion_manager.set_promotion_data(data)
            
            # Load icon data
            self.icon_manager.set_icon_data(data)
            
            # Load abilities
            self.abilities = data.get('abilities', []).copy()
            
            # Update all UI components
            self.update_all_ui_components()
            
            logger.info("Form populated successfully")
            
        except Exception as e:
            logger.error(f"Error populating form: {e}")
            raise

    def validate_form_data(self) -> List[str]:
        """Validate current form data (BaseEditor abstract method)."""
        errors = []
        
        try:
            # Validate using data handler
            data_errors = self.data_handler.validate_piece_data(self.collect_form_data())
            errors.extend(data_errors)
            
            # Validate promotions
            promotion_errors = self.promotion_manager.validate_promotion_data()
            errors.extend(promotion_errors)
            
            # Validate icons
            icon_errors = self.icon_manager.validate_icon_data()
            errors.extend(icon_errors)
            
            # Validate movement data
            movement_errors = self.movement_manager.validate_movement_data()
            errors.extend(movement_errors)
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors

    # ========== UI UPDATE METHODS ==========
    
    def update_all_ui_components(self) -> None:
        """Update all UI components after data changes."""
        try:
            # Update movement controls
            if hasattr(self, 'movement_manager'):
                self.movement_manager.update_movement_controls()
                self.movement_manager.update_movement_pattern_preview()
            
            # Update promotion displays
            if hasattr(self, 'promotion_manager'):
                self.promotion_manager.update_promotion_displays()
            
            # Update icon previews
            if hasattr(self, 'icon_manager'):
                self.icon_manager.update_icon_previews()
            
            # Update other UI components
            self.update_recharge_options()
            self.refresh_abilities_list()
            self.on_role_changed()
            
        except Exception as e:
            logger.error(f"Error updating UI components: {e}")
    
    def update_points_recharge_options(self) -> None:
        """Update points and recharge options based on master checkbox."""
        try:
            logger.debug("Updating points and recharge options")

            # Check if points and recharge system is enabled
            if hasattr(self, 'enable_points_recharge_check'):
                enabled = self.enable_points_recharge_check.isChecked()

                # Enable/disable ALL points and recharge widgets based on master checkbox
                if hasattr(self, 'points_recharge_widgets'):
                    for widget in self.points_recharge_widgets:
                        widget.setEnabled(enabled)

                # Gray out/restore labels
                if hasattr(self, 'points_recharge_labels'):
                    for label in self.points_recharge_labels:
                        if enabled:
                            label.setStyleSheet("font-weight: bold; margin-top: 10px; margin-bottom: 5px;")  # Restore normal styling
                        else:
                            label.setStyleSheet("font-weight: bold; margin-top: 10px; margin-bottom: 5px; color: #888888;")  # Gray out

                # Update recharge type-specific visibility if enabled
                if enabled:
                    self.update_recharge_options()
                else:
                    # Hide all type-specific controls when system is disabled
                    if hasattr(self, 'turn_recharge_spin'):
                        self.turn_recharge_spin.setVisible(False)
                    if hasattr(self, 'adjacency_config_btn'):
                        self.adjacency_config_btn.setVisible(False)
                    if hasattr(self, 'committed_turns_spin'):
                        self.committed_turns_spin.setVisible(False)

        except Exception as e:
            logger.error(f"Error updating points and recharge options: {e}")

    def update_recharge_options(self) -> None:
        """Update recharge type-specific options based on current settings."""
        try:
            logger.debug("Updating recharge type options")

            # Only update if points/recharge system is enabled
            if hasattr(self, 'enable_points_recharge_check') and self.enable_points_recharge_check.isChecked():
                if hasattr(self, 'recharge_type_combo'):
                    recharge_type = self.recharge_type_combo.currentText()

                    # Hide all recharge type-specific controls first
                    if hasattr(self, 'turn_recharge_spin'):
                        self.turn_recharge_spin.setVisible(False)
                    if hasattr(self, 'adjacency_config_btn'):
                        self.adjacency_config_btn.setVisible(False)
                    if hasattr(self, 'committed_turns_spin'):
                        self.committed_turns_spin.setVisible(False)

                    # Show only the currently selected recharge type input
                    if recharge_type == "turnRecharge" and hasattr(self, 'turn_recharge_spin'):
                        self.turn_recharge_spin.setVisible(True)
                    elif recharge_type == "adjacencyRecharge" and hasattr(self, 'adjacency_config_btn'):
                        self.adjacency_config_btn.setVisible(True)
                    elif recharge_type == "committedRecharge" and hasattr(self, 'committed_turns_spin'):
                        self.committed_turns_spin.setVisible(True)
            else:
                # Hide all type-specific controls when system is disabled
                if hasattr(self, 'turn_recharge_spin'):
                    self.turn_recharge_spin.setVisible(False)
                if hasattr(self, 'adjacency_config_btn'):
                    self.adjacency_config_btn.setVisible(False)
                if hasattr(self, 'committed_turns_spin'):
                    self.committed_turns_spin.setVisible(False)

        except Exception as e:
            logger.error(f"Error updating recharge type options: {e}")

    def update_promotion_visibility(self) -> None:
        """Update promotion section visibility based on enable checkbox."""
        try:
            logger.debug("Updating promotion section visibility")

            # Check if promotions are enabled
            if hasattr(self, 'enable_promotions_check') and hasattr(self, 'promotion_section'):
                enabled = self.enable_promotions_check.isChecked()
                self.promotion_section.setVisible(enabled)

                logger.debug(f"Promotion section visibility set to: {enabled}")

        except Exception as e:
            logger.error(f"Error updating promotion visibility: {e}")

    def on_quick_load_selection(self, piece_filename: str) -> None:
        """Handle quick load dropdown selection with auto-refresh."""
        try:
            if not piece_filename or piece_filename == "Select piece to load...":
                return

            # Extract the actual filename from the display format: "Piece Name (filename)"
            if '(' in piece_filename and piece_filename.endswith(')'):
                # Extract filename from "Piece Name (filename)" format
                start_idx = piece_filename.rfind('(') + 1
                end_idx = piece_filename.rfind(')')
                base_filename = piece_filename[start_idx:end_idx]
            else:
                # Fallback: treat the whole string as filename and remove .json if present
                base_filename = piece_filename.replace('.json', '')

            piece_data, error = simple_bridge.load_piece_for_ui(base_filename)
            if error:
                QMessageBox.critical(self, "Error", f"Failed to load piece: {error}")
                return

            self.data_handler.load_piece_data(piece_data)

            # Set the current filename so saves go to the original file
            self.current_filename = base_filename

            if hasattr(self, 'status_widget'):
                self.status_widget.show_success(f"Loaded piece: {piece_data.get('name', 'Unknown')}")

            self.clear_unsaved_changes()

            # Reset dropdown to placeholder and refresh the list
            if hasattr(self, 'quick_load_combo'):
                self.quick_load_combo.setCurrentIndex(0)
                self.refresh_quick_load_dropdown()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load piece: {str(e)}")
            logger.error(f"Error in quick load selection: {e}")

    def refresh_quick_load_dropdown(self) -> None:
        """Refresh the quick load dropdown with current piece files."""
        try:
            if hasattr(self, 'quick_load_combo'):
                current_text = self.quick_load_combo.currentText()
                self.quick_load_combo.clear()
                self.quick_load_combo.addItem("Select piece to load...")

                # Get list of piece files
                piece_files = []
                if os.path.exists(PIECES_DIR):
                    piece_files = [f for f in os.listdir(PIECES_DIR) if f.endswith('.json')]

                # Add piece files to dropdown
                for piece_file in sorted(piece_files):
                    try:
                        base_filename = piece_file.replace('.json', '')
                        piece_data, error = simple_bridge.load_piece_for_ui(base_filename)

                        if piece_data and not error:
                            piece_name = piece_data.get('name', base_filename)
                            display_text = f"{piece_name} ({base_filename})"
                            self.quick_load_combo.addItem(display_text)

                    except Exception as e:
                        logger.error(f"Error loading piece info for dropdown: {piece_file}: {e}")

                # Restore selection if it still exists
                if current_text != "Select piece to load...":
                    index = self.quick_load_combo.findText(current_text)
                    if index >= 0:
                        self.quick_load_combo.setCurrentIndex(index)

        except Exception as e:
            logger.error(f"Error refreshing quick load dropdown: {e}")

    def open_adjacency_required_dialog(self) -> None:
        """Open the adjacency required configuration dialog."""
        try:
            from dialogs.unified_adjacency_dialog import edit_adjacency_config

            # Get current adjacency config if it exists
            current_config = getattr(self, 'adjacency_recharge_config', None)

            # Open dialog for adjacency recharge configuration
            config = edit_adjacency_config(
                parent=self,
                initial_config=current_config,
                dialog_type="adjacency_recharge"
            )

            if config is not None:
                self.adjacency_recharge_config = config
                logger.debug(f"Updated adjacency recharge config: {config}")

        except Exception as e:
            logger.error(f"Error opening adjacency required dialog: {e}")

    def add_promotion_piece(self) -> None:
        """Add a piece to the selected promotion type."""
        try:
            # Get selected promotion type
            promotion_type = self.promotion_type_combo.currentText()

            # Get selected piece
            piece_text = self.promotion_piece_combo.currentText()
            if not piece_text or piece_text == "Choose piece to add...":
                show_improved_warning("Please select a piece to add.", "No Piece Selected", self)
                return

            # Extract piece filename from display text
            piece_filename = piece_text.split(' (')[-1].replace(')', '') if ' (' in piece_text else piece_text

            # Determine target list
            if promotion_type == "Primary Promotions":
                target_list = self.primary_promo_list
                if not hasattr(self, 'primary_promotions'):
                    self.primary_promotions = []
                promotion_list = self.primary_promotions
            else:  # Secondary Promotions
                target_list = self.secondary_promo_list
                if not hasattr(self, 'secondary_promotions'):
                    self.secondary_promotions = []
                promotion_list = self.secondary_promotions

            # Check if piece is already added
            if piece_filename in promotion_list:
                show_improved_warning(f"Piece '{piece_filename}' is already in {promotion_type.lower()}.", "Duplicate Piece", self)
                return

            # Add to data list
            promotion_list.append(piece_filename)

            # Add to display list with remove button
            self.refresh_promotion_display()

            # Reset combo box
            self.promotion_piece_combo.setCurrentIndex(0)

            self.mark_unsaved_changes()
            logger.debug(f"Added {piece_filename} to {promotion_type}")

        except Exception as e:
            logger.error(f"Error adding promotion piece: {e}")

    def remove_promotion_piece(self, piece_filename: str, promotion_type: str) -> None:
        """Remove a piece from the specified promotion type."""
        try:
            if promotion_type == "Primary":
                if hasattr(self, 'primary_promotions') and piece_filename in self.primary_promotions:
                    self.primary_promotions.remove(piece_filename)
            else:  # Secondary
                if hasattr(self, 'secondary_promotions') and piece_filename in self.secondary_promotions:
                    self.secondary_promotions.remove(piece_filename)

            self.refresh_promotion_display()
            self.mark_unsaved_changes()
            logger.debug(f"Removed {piece_filename} from {promotion_type} promotions")

        except Exception as e:
            logger.error(f"Error removing promotion piece: {e}")

    def refresh_promotion_display(self) -> None:
        """Refresh the promotion display lists."""
        try:
            # Clear both lists
            self.primary_promo_list.clear()
            self.secondary_promo_list.clear()

            # Populate primary promotions
            if hasattr(self, 'primary_promotions'):
                for piece_filename in self.primary_promotions:
                    item_widget = self.create_promotion_item(piece_filename, "Primary")
                    item = QListWidgetItem()
                    item.setSizeHint(item_widget.sizeHint())
                    self.primary_promo_list.addItem(item)
                    self.primary_promo_list.setItemWidget(item, item_widget)

            # Populate secondary promotions
            if hasattr(self, 'secondary_promotions'):
                for piece_filename in self.secondary_promotions:
                    item_widget = self.create_promotion_item(piece_filename, "Secondary")
                    item = QListWidgetItem()
                    item.setSizeHint(item_widget.sizeHint())
                    self.secondary_promo_list.addItem(item)
                    self.secondary_promo_list.setItemWidget(item, item_widget)

        except Exception as e:
            logger.error(f"Error refreshing promotion display: {e}")

    def create_promotion_item(self, piece_filename: str, promotion_type: str) -> QWidget:
        """Create a widget for a promotion item with remove button."""
        try:
            widget = QWidget()
            layout = QHBoxLayout()
            layout.setContentsMargins(2, 2, 2, 2)

            # Load piece data to get display name
            piece_data, error = simple_bridge.load_piece_for_ui(piece_filename)
            if piece_data and not error:
                piece_name = piece_data.get('name', piece_filename)
                display_text = f"{piece_name} ({piece_filename})"
            else:
                display_text = f"{piece_filename} (Error loading)"

            # Piece label
            piece_label = QLabel(display_text)
            layout.addWidget(piece_label)
            layout.addStretch()

            # Remove button
            remove_btn = QPushButton("✕")
            remove_btn.setFixedSize(20, 20)
            remove_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff4444;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-weight: bold;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #cc3333;
                }
            """)
            remove_btn.clicked.connect(lambda: self.remove_promotion_piece(piece_filename, promotion_type))
            layout.addWidget(remove_btn)

            widget.setLayout(layout)
            return widget

        except Exception as e:
            logger.error(f"Error creating promotion item: {e}")
            # Return simple widget on error
            widget = QWidget()
            layout = QHBoxLayout()
            layout.addWidget(QLabel(f"{piece_filename} (Error)"))
            widget.setLayout(layout)
            return widget

    def refresh_promotion_piece_combo(self) -> None:
        """Refresh the promotion piece combo box with available pieces."""
        try:
            if not hasattr(self, 'promotion_piece_combo'):
                return

            # Clear existing items except the first one
            self.promotion_piece_combo.clear()
            self.promotion_piece_combo.addItem("Choose piece to add...")

            # Load pieces from directory
            piece_files = simple_bridge.list_pieces()

            for piece_filename in sorted(piece_files):
                try:
                    piece_data, error = simple_bridge.load_piece_for_ui(piece_filename)
                    if piece_data and not error:
                        piece_name = piece_data.get('name', piece_filename)
                        display_text = f"{piece_name} ({piece_filename})"
                        self.promotion_piece_combo.addItem(display_text)
                except Exception as e:
                    logger.warning(f"Error loading piece {piece_filename} for combo: {e}")

        except Exception as e:
            logger.error(f"Error refreshing promotion piece combo: {e}")

    def refresh_abilities_list(self) -> None:
        """Refresh the abilities list display."""
        try:
            logger.debug("Refreshing abilities list")

            # Update the inline ability selector with current abilities
            if hasattr(self, 'ability_selector') and hasattr(self, 'abilities'):
                # Convert abilities list to the format expected by the selector
                abilities_data = []
                for ability_name in self.abilities:
                    if ability_name:  # Skip empty strings
                        abilities_data.append({'ability': ability_name, 'cost': 0})

                self.ability_selector.set_abilities(abilities_data)
                logger.debug(f"Updated ability selector with {len(abilities_data)} abilities")

        except Exception as e:
            logger.error(f"Error refreshing abilities list: {e}")

    def on_abilities_changed(self):
        """Handle changes to the abilities list from the inline selector"""
        try:
            # Mark unsaved changes when abilities are modified
            self.mark_unsaved_changes()

            # Update the abilities list from the selector
            if hasattr(self, 'ability_selector'):
                abilities_data = self.ability_selector.get_abilities()
                # Convert to simple list of ability names for compatibility
                self.abilities = [ability_data.get('ability', '') for ability_data in abilities_data]
                logger.debug(f"Updated abilities list: {self.abilities}")
        except Exception as e:
            logger.error(f"Error handling abilities change: {e}")

    def mark_unsaved_changes(self):
        """Mark that there are unsaved changes"""
        try:
            self.unsaved_changes = True
            if not self.windowTitle().endswith("*"):
                self.setWindowTitle(self.windowTitle() + "*")
        except Exception as e:
            logger.error(f"Error marking unsaved changes: {e}")

    def mark_saved(self):
        """Mark that changes have been saved (alias for clear_unsaved_changes)"""
        self.clear_unsaved_changes()

    def clear_unsaved_changes(self):
        """Clear the unsaved changes flag"""
        try:
            self.unsaved_changes = False
            title = self.windowTitle()
            if title.endswith("*"):
                self.setWindowTitle(title[:-1])
        except Exception as e:
            logger.error(f"Error clearing unsaved changes: {e}")

    def save_piece(self):
        """Save the current piece using standardized BaseEditor method"""
        try:
            if not self.name_edit.text().strip():
                show_improved_warning("Please enter a piece name before saving.", "Missing Piece Name", self)
                return False

            result = self.data_handler.save_data()  # Use data handler's save method

            # Auto-refresh the quick load dropdown after saving
            if result:
                self.refresh_quick_load_dropdown()

            return result
        except Exception as e:
            logger.error(f"Error saving piece: {e}")
            return False

    def save_as_piece(self):
        """Save the current piece with a new name"""
        try:
            if not self.name_edit.text().strip():
                show_improved_warning("Please enter a piece name before saving.", "Missing Piece Name", self)
                return False

            # Get new filename from user
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Piece As", PIECES_DIR, "JSON Files (*.json)"
            )

            if file_path:
                return self.data_handler.save_data_as(file_path)
            return False

        except Exception as e:
            logger.error(f"Error saving piece as: {e}")
            return False

    def delete_current_piece(self):
        """Delete the currently loaded piece file"""
        try:
            if not hasattr(self, 'current_filename') or not self.current_filename:
                show_improved_warning("No piece file is currently loaded to delete.", "No File Loaded", self)
                return False

            # Confirm deletion
            reply = QMessageBox.question(
                self, "Confirm Deletion",
                f"Are you sure you want to delete '{self.current_filename}'?\n\nThis action cannot be undone.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                result = self.data_handler.delete_current_file()

                # Auto-refresh the quick load dropdown after deletion
                if result:
                    self.refresh_quick_load_dropdown()

                return result
            return False

        except Exception as e:
            logger.error(f"Error deleting piece: {e}")
            return False

    def new_piece(self):
        """Create a new piece using standardized BaseEditor method"""
        try:
            self.data_handler.new_data()  # Use data handler's new method
        except Exception as e:
            logger.error(f"Error creating new piece: {e}")

    def generate_standard_pattern(self, movement_type, piece_pos):
        """Generate the standard pattern for a movement type"""
        try:
            pattern = [[0 for _ in range(8)] for _ in range(8)]
            piece_r, piece_c = piece_pos

            if movement_type == "orthogonal":
                # Rook pattern: orthogonal lines
                for c in range(8):
                    if c != piece_c:
                        pattern[piece_r][c] = 3
                for r in range(8):
                    if r != piece_r:
                        pattern[r][piece_c] = 3

            elif movement_type == "diagonal":
                # Bishop pattern: diagonal lines
                for r in range(8):
                    for c in range(8):
                        if r != piece_r and c != piece_c:
                            if abs(r - piece_r) == abs(c - piece_c):
                                pattern[r][c] = 3

            elif movement_type == "any":
                # Queen pattern: orthogonal + diagonal
                for c in range(8):
                    if c != piece_c:
                        pattern[piece_r][c] = 3
                for r in range(8):
                    if r != piece_r:
                        pattern[r][piece_c] = 3
                for r in range(8):
                    for c in range(8):
                        if r != piece_r and c != piece_c:
                            if abs(r - piece_r) == abs(c - piece_c):
                                pattern[r][c] = 3

            elif movement_type == "lShape":
                # Knight pattern: L-shaped moves
                knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2),
                               (1, -2), (1, 2), (2, -1), (2, 1)]
                for dr, dc in knight_moves:
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        pattern[r][c] = 3

            elif movement_type == "king":
                # King pattern: 1 square in any direction
                king_moves = [(-1, -1), (-1, 0), (-1, 1),
                             (0, -1),           (0, 1),
                             (1, -1),  (1, 0),  (1, 1)]
                for dr, dc in king_moves:
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        pattern[r][c] = 3

            elif movement_type == "global":
                # Global pattern: anywhere on the board
                for r in range(8):
                    for c in range(8):
                        if r != piece_r or c != piece_c:  # Exclude piece position
                            pattern[r][c] = 3

            return pattern
        except Exception as e:
            logger.error(f"Error generating standard pattern: {e}")
            return [[0 for _ in range(8)] for _ in range(8)]

    def on_movement_pattern_selected(self, movement_type: str) -> None:
        """Delegate movement pattern selection to movement manager"""
        try:
            self.movement_manager.on_movement_pattern_selected(movement_type)
        except Exception as e:
            logger.error(f"Error handling movement pattern selection: {e}")

    def on_role_changed(self) -> None:
        """Handle role change events."""
        try:
            # This method will be implemented based on the original logic
            # For now, just log that it was called
            logger.debug("Role changed")
            
        except Exception as e:
            logger.error(f"Error handling role change: {e}")

    # ========== CONVENIENCE METHODS FOR BACKWARD COMPATIBILITY ==========
    
    def load_piece_data(self, piece_data: Dict[str, Any]) -> None:
        """Load piece data (backward compatibility method)."""
        self.populate_form_data(piece_data)
    
    def update_movement_controls(self) -> None:
        """Update movement controls (backward compatibility method)."""
        if hasattr(self, 'movement_manager'):
            self.movement_manager.update_movement_controls()
    
    def update_movement_pattern_preview(self) -> None:
        """Update movement pattern preview (backward compatibility method)."""
        if hasattr(self, 'movement_manager'):
            self.movement_manager.update_movement_pattern_preview()
    
    def update_icon_previews(self) -> None:
        """Update icon previews (backward compatibility method)."""
        if hasattr(self, 'icon_manager'):
            self.icon_manager.update_icon_previews()
    
    def update_promotion_displays(self) -> None:
        """Update promotion displays (backward compatibility method)."""
        if hasattr(self, 'promotion_manager'):
            self.promotion_manager.update_promotion_displays()

    # ========== FILE OPERATIONS ==========
    
    def load_piece(self) -> None:
        """Load a piece from file using standardized BaseEditor method."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Piece", PIECES_DIR, "JSON Files (*.json)"
            )

            if file_path:
                filename = os.path.basename(file_path).replace('.json', '')
                self.load_data(filename)  # Use BaseEditor's standardized load_data method
                
        except Exception as e:
            logger.error(f"Error loading piece: {e}")
            show_improved_error(e, "loading piece data", filename, self)
    
    def open_piece_dialog(self) -> None:
        """Alias for load_piece to maintain compatibility with main.py."""
        self.load_piece()

    # ========== REFRESH METHODS ==========
    
    def refresh_all_data(self) -> None:
        """Refresh all data and UI components - for menu refresh function."""
        try:
            logger.info("Refreshing all piece editor data...")

            # Refresh file lists
            self.refresh_quick_load_dropdown()

            # Refresh abilities list
            self.refresh_abilities_list()

            # Update all UI components
            self.update_all_ui_components()

            # Clear any stale pattern data if no file is loaded
            if not hasattr(self, 'current_filename') or not self.current_filename:
                self.movement_manager.reset_movement_to_default()
                self.movement_manager.update_movement_pattern_preview()

            self.status_widget.show_success("All data refreshed")
            logger.info("Piece editor data refresh completed")

        except Exception as e:
            logger.error(f"Error refreshing data: {e}")
            show_improved_error(e, "refreshing piece data", "", self)

    # ========== ABILITY MANAGEMENT ==========
    
    def open_ability_editor(self, ability_name: Optional[str] = None) -> None:
        """Open the Ability Editor window."""
        try:
            from editors.ability_editor import AbilityEditorWindow
            
            # Create ability editor window
            self.ability_editor = AbilityEditorWindow()
            self.ability_editor.setWindowModality(Qt.WindowModality.ApplicationModal)
            
            # If editing existing ability, load it
            if ability_name:
                ability_data, error = simple_bridge.load_ability_for_ui(ability_name)
                if ability_data and not error:
                    self.ability_editor.populate_form_data(ability_data)
                    self.ability_editor.current_filename = ability_name
                else:
                    show_improved_error(Exception(error), "loading ability data", ability_name, self)
                    return
            
            # Show the editor
            self.ability_editor.show()
            
        except Exception as e:
            logger.error(f"Error opening ability editor: {e}")
            show_improved_error(e, "opening ability editor", "", self)
