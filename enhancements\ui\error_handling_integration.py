#!/usr/bin/env python3
"""
Integration patches for Enhanced Error Handling System
Provides drop-in replacements and patches for existing data managers
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

from enhanced_error_handling import error_handler, ErrorSeverity, ErrorCategory, safe_file_load, safe_file_save

logger = logging.getLogger(__name__)

class EnhancedDirectDataManager:
    """
    Enhanced version of DirectDataManager with improved error handling
    Drop-in replacement for utils.direct_data_manager.DirectDataManager
    """
    
    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece data with enhanced error handling
        """
        try:
            from config import PIECES_DIR
            
            # Determine filename
            if filename is None:
                filename = piece_data.get('name', 'unnamed_piece')
            
            # Clean filename
            filename = EnhancedDirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = str(Path(PIECES_DIR) / filename)
            
            # Add version if not present
            if 'version' not in piece_data:
                piece_data['version'] = '1.0.0'
            
            # Use enhanced error handling for save operation
            success, error_context = error_handler.safe_json_save(piece_data, file_path)
            
            if success:
                logger.info(f"Piece saved successfully: {file_path}")
                return True, None
            else:
                return False, error_context.user_message if error_context else "Unknown error"
                
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="save_piece",
                file_path=filename
            )
            return False, error_context.user_message
    
    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load piece data with enhanced error handling
        """
        try:
            from config import PIECES_DIR
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = str(Path(PIECES_DIR) / filename)
            
            # Use enhanced error handling for load operation
            piece_data, error_context = error_handler.safe_json_load(file_path)
            
            if piece_data is not None:
                logger.info(f"Piece loaded successfully: {file_path}")
                return piece_data, None
            else:
                return None, error_context.user_message if error_context else "Unknown error"
                
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="load_piece",
                file_path=filename
            )
            return None, error_context.user_message
    
    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data with enhanced error handling
        """
        try:
            from config import ABILITIES_DIR
            
            # Determine filename
            if filename is None:
                filename = ability_data.get('name', 'unnamed_ability')
            
            # Clean filename
            filename = EnhancedDirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = str(Path(ABILITIES_DIR) / filename)
            
            # Add version if not present
            if 'version' not in ability_data:
                ability_data['version'] = '1.0.0'
            
            # Use enhanced error handling for save operation
            success, error_context = error_handler.safe_json_save(ability_data, file_path)
            
            if success:
                logger.info(f"Ability saved successfully: {file_path}")
                return True, None
            else:
                return False, error_context.user_message if error_context else "Unknown error"
                
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="save_ability",
                file_path=filename
            )
            return False, error_context.user_message
    
    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load ability data with enhanced error handling
        """
        try:
            from config import ABILITIES_DIR
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = str(Path(ABILITIES_DIR) / filename)
            
            # Use enhanced error handling for load operation
            ability_data, error_context = error_handler.safe_json_load(file_path)
            
            if ability_data is not None:
                logger.info(f"Ability loaded successfully: {file_path}")
                return ability_data, None
            else:
                return None, error_context.user_message if error_context else "Unknown error"
                
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="load_ability",
                file_path=filename
            )
            return None, error_context.user_message
    
    @staticmethod
    def list_pieces() -> List[str]:
        """List available piece files with error handling"""
        try:
            from config import PIECES_DIR
            pieces_path = Path(PIECES_DIR)
            
            if not pieces_path.exists():
                logger.warning(f"Pieces directory does not exist: {PIECES_DIR}")
                return []
            
            pieces = []
            for file_path in pieces_path.glob("*.json"):
                pieces.append(file_path.stem)  # filename without extension
            
            return sorted(pieces)
            
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.FILE_OPERATION,
                operation="list_pieces"
            )
            return []
    
    @staticmethod
    def list_abilities() -> List[str]:
        """List available ability files with error handling"""
        try:
            from config import ABILITIES_DIR
            abilities_path = Path(ABILITIES_DIR)
            
            if not abilities_path.exists():
                logger.warning(f"Abilities directory does not exist: {ABILITIES_DIR}")
                return []
            
            abilities = []
            for file_path in abilities_path.glob("*.json"):
                abilities.append(file_path.stem)  # filename without extension
            
            return sorted(abilities)
            
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.FILE_OPERATION,
                operation="list_abilities"
            )
            return []
    
    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete piece file with enhanced error handling"""
        try:
            from config import PIECES_DIR
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(PIECES_DIR) / filename
            
            if not file_path.exists():
                return False, f"Piece file not found: {filename}"
            
            # Create backup before deletion
            backup_path = file_path.with_suffix('.json.deleted_backup')
            import shutil
            shutil.copy2(file_path, backup_path)
            
            # Delete the file
            file_path.unlink()
            
            logger.info(f"Piece deleted successfully: {filename} (backup created)")
            return True, None
            
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="delete_piece",
                file_path=filename
            )
            return False, error_context.user_message
    
    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete ability file with enhanced error handling"""
        try:
            from config import ABILITIES_DIR
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = Path(ABILITIES_DIR) / filename
            
            if not file_path.exists():
                return False, f"Ability file not found: {filename}"
            
            # Create backup before deletion
            backup_path = file_path.with_suffix('.json.deleted_backup')
            import shutil
            shutil.copy2(file_path, backup_path)
            
            # Delete the file
            file_path.unlink()
            
            logger.info(f"Ability deleted successfully: {filename} (backup created)")
            return True, None
            
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.FILE_OPERATION,
                operation="delete_ability",
                file_path=filename
            )
            return False, error_context.user_message
    
    @staticmethod
    def _clean_filename(filename: str) -> str:
        """Clean filename for safe file operations"""
        import re
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')
        # Ensure it's not empty
        if not filename:
            filename = 'unnamed_file'
        return filename

class EnhancedSimpleBridge:
    """
    Enhanced version of SimpleBridge with improved error handling
    Drop-in replacement for utils.simple_bridge.SimpleBridge
    """
    
    @staticmethod
    def get_piece_data_from_ui(editor) -> Dict[str, Any]:
        """Get piece data from UI with enhanced error handling"""
        try:
            from core.interfaces.editor_data_interface import EditorDataInterface
            return EditorDataInterface.collect_data_from_ui(editor, "piece")
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.UI_OPERATION,
                operation="get_piece_data_from_ui"
            )
            # Return safe default data
            return {
                'version': '1.0.0',
                'name': '',
                'description': '',
                'role': 'Commander',
                'movement': {'type': 'orthogonal', 'pattern': None, 'piecePosition': [3, 3]},
                'canCapture': True,
                'abilities': [],
                'maxPoints': 0,
                'startingPoints': 0,
                'rechargeType': 'turnRecharge'
            }
    
    @staticmethod
    def set_piece_data_to_ui(editor, data: Dict[str, Any]):
        """Set piece data to UI with enhanced error handling"""
        try:
            from core.interfaces.editor_data_interface import EditorDataInterface
            EditorDataInterface.populate_ui_from_data(editor, data, "piece")
            logger.info("Piece data set to UI successfully")
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.UI_OPERATION,
                operation="set_piece_data_to_ui"
            )
    
    @staticmethod
    def get_ability_data_from_ui(editor) -> Dict[str, Any]:
        """Get ability data from UI with enhanced error handling"""
        try:
            from core.interfaces.editor_data_interface import EditorDataInterface
            return EditorDataInterface.collect_data_from_ui(editor, "ability")
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.UI_OPERATION,
                operation="get_ability_data_from_ui"
            )
            # Return safe default data
            return {
                'version': '1.0.0',
                'name': '',
                'description': '',
                'cost': 0,
                'tags': []
            }
    
    @staticmethod
    def set_ability_data_to_ui(editor, data: Dict[str, Any]):
        """Set ability data to UI with enhanced error handling"""
        try:
            from core.interfaces.editor_data_interface import EditorDataInterface
            EditorDataInterface.populate_ui_from_data(editor, data, "ability")
            logger.info("Ability data set to UI successfully")
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.UI_OPERATION,
                operation="set_ability_data_to_ui"
            )
    
    # File operations delegate to enhanced data manager
    @staticmethod
    def load_piece_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load piece for UI with enhanced error handling"""
        return EnhancedDirectDataManager.load_piece(filename)
    
    @staticmethod
    def save_piece_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save piece from UI with enhanced error handling"""
        try:
            piece_data = EnhancedSimpleBridge.get_piece_data_from_ui(editor)
            return EnhancedDirectDataManager.save_piece(piece_data, filename)
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.UI_OPERATION,
                operation="save_piece_from_ui"
            )
            return False, error_context.user_message
    
    @staticmethod
    def load_ability_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Load ability for UI with enhanced error handling"""
        return EnhancedDirectDataManager.load_ability(filename)
    
    @staticmethod
    def save_ability_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save ability from UI with enhanced error handling"""
        try:
            ability_data = EnhancedSimpleBridge.get_ability_data_from_ui(editor)
            return EnhancedDirectDataManager.save_ability(ability_data, filename)
        except Exception as e:
            error_context = error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.ERROR,
                category=ErrorCategory.UI_OPERATION,
                operation="save_ability_from_ui"
            )
            return False, error_context.user_message
    
    # List and delete operations
    @staticmethod
    def list_pieces() -> List[str]:
        return EnhancedDirectDataManager.list_pieces()
    
    @staticmethod
    def list_abilities() -> List[str]:
        return EnhancedDirectDataManager.list_abilities()
    
    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        return EnhancedDirectDataManager.delete_piece(filename)
    
    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        return EnhancedDirectDataManager.delete_ability(filename)
