"""
Data Ownership Registry for Adventure Chess Creator

This module defines the authoritative data ownership patterns and access rules
for all data types in the application, establishing a single source of truth
for each category of data.

The registry eliminates confusion about where data should be stored and accessed,
providing clear guidelines for developers and AI assistants.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DataAccessPattern(Enum):
    """Enumeration of data access patterns used in the application."""
    DIRECT_PROPERTY = "direct_property"  # Direct access via object property
    MANAGER_MEDIATED = "manager_mediated"  # Access through manager class
    INTERFACE_MEDIATED = "interface_mediated"  # Access through interface
    COMPUTED_PROPERTY = "computed_property"  # Computed from other data


@dataclass
class DataOwnershipRule:
    """
    Defines ownership and access rules for a specific data type.
    """
    data_type: str
    owner_component: str
    owner_property: str
    access_pattern: DataAccessPattern
    description: str
    legacy_aliases: List[str]
    validation_rules: List[str]
    
    def __post_init__(self):
        """Validate the ownership rule after initialization."""
        if not self.data_type:
            raise ValueError("data_type cannot be empty")
        if not self.owner_component:
            raise ValueError("owner_component cannot be empty")
        if not self.owner_property:
            raise ValueError("owner_property cannot be empty")


class DataOwnershipRegistry:
    """
    Central registry for all data ownership patterns in Adventure Chess Creator.
    
    This registry serves as the authoritative source for determining where
    each type of data should be stored and how it should be accessed.
    """
    
    def __init__(self):
        """Initialize the data ownership registry with core rules."""
        self._ownership_rules: Dict[str, DataOwnershipRule] = {}
        self._legacy_mapping: Dict[str, str] = {}
        self._initialize_core_rules()
    
    def _initialize_core_rules(self):
        """Initialize the core data ownership rules."""
        
        # Piece Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="piece_data",
            owner_component="PieceEditor",
            owner_property="current_data",
            access_pattern=DataAccessPattern.DIRECT_PROPERTY,
            description="All piece configuration data including name, role, abilities, points, etc.",
            legacy_aliases=["current_piece"],
            validation_rules=["Must have name", "Must have valid role", "Must have movement data"]
        ))
        
        # Movement Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="movement_data",
            owner_component="PieceEditor",
            owner_property="current_movement_data",
            access_pattern=DataAccessPattern.COMPUTED_PROPERTY,
            description="Movement pattern, type, and piece position data",
            legacy_aliases=["current_custom_pattern", "selected_movement_type", "custom_pattern_piece_pos"],
            validation_rules=["Must have type", "Must have valid pattern", "Must have piece position"]
        ))
        
        # Ability Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="ability_data",
            owner_component="AbilityEditor",
            owner_property="current_data",
            access_pattern=DataAccessPattern.DIRECT_PROPERTY,
            description="All ability configuration data including name, cost, tags, etc.",
            legacy_aliases=["current_ability"],
            validation_rules=["Must have name", "Must have valid cost", "Must have valid tags"]
        ))
        
        # Tag Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="tag_data",
            owner_component="AbilityTagManager",
            owner_property="tag_configurations",
            access_pattern=DataAccessPattern.MANAGER_MEDIATED,
            description="Tag-specific configuration data for abilities",
            legacy_aliases=[],
            validation_rules=["Must match tag schema", "Must be valid for tag type"]
        ))
        
        # UI State Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="ui_state",
            owner_component="BaseEditor",
            owner_property="ui_state",
            access_pattern=DataAccessPattern.INTERFACE_MEDIATED,
            description="UI component states, dialog positions, widget values",
            legacy_aliases=[],
            validation_rules=["Must be serializable", "Must not contain sensitive data"]
        ))
        
        # File State Data Ownership
        self.register_ownership_rule(DataOwnershipRule(
            data_type="file_state",
            owner_component="BaseEditor",
            owner_property="current_filename, last_saved_data",
            access_pattern=DataAccessPattern.DIRECT_PROPERTY,
            description="Current file information and saved state tracking",
            legacy_aliases=[],
            validation_rules=["Filename must be valid", "Saved data must match current schema"]
        ))
    
    def register_ownership_rule(self, rule: DataOwnershipRule) -> None:
        """
        Register a new data ownership rule.
        
        Args:
            rule: DataOwnershipRule to register
        """
        self._ownership_rules[rule.data_type] = rule
        
        # Register legacy aliases
        for alias in rule.legacy_aliases:
            self._legacy_mapping[alias] = rule.data_type
        
        logger.debug(f"Registered ownership rule for {rule.data_type}")
    
    def get_ownership_rule(self, data_type: str) -> Optional[DataOwnershipRule]:
        """
        Get ownership rule for a data type.
        
        Args:
            data_type: Type of data to look up
            
        Returns:
            DataOwnershipRule if found, None otherwise
        """
        # Check direct mapping first
        if data_type in self._ownership_rules:
            return self._ownership_rules[data_type]
        
        # Check legacy aliases
        if data_type in self._legacy_mapping:
            canonical_type = self._legacy_mapping[data_type]
            return self._ownership_rules.get(canonical_type)
        
        return None
    
    def get_canonical_data_type(self, data_type_or_alias: str) -> Optional[str]:
        """
        Get the canonical data type for a given type or alias.
        
        Args:
            data_type_or_alias: Data type or legacy alias
            
        Returns:
            Canonical data type if found, None otherwise
        """
        if data_type_or_alias in self._ownership_rules:
            return data_type_or_alias
        
        return self._legacy_mapping.get(data_type_or_alias)
    
    def get_data_owner(self, data_type: str) -> Optional[Tuple[str, str]]:
        """
        Get the owner component and property for a data type.
        
        Args:
            data_type: Type of data to look up
            
        Returns:
            Tuple of (component, property) if found, None otherwise
        """
        rule = self.get_ownership_rule(data_type)
        if rule:
            return (rule.owner_component, rule.owner_property)
        return None
    
    def validate_data_access(self, data_type: str, access_method: str) -> bool:
        """
        Validate that a data access method is appropriate for the data type.
        
        Args:
            data_type: Type of data being accessed
            access_method: Method being used to access the data
            
        Returns:
            True if access method is valid, False otherwise
        """
        rule = self.get_ownership_rule(data_type)
        if not rule:
            logger.warning(f"No ownership rule found for data type: {data_type}")
            return False
        
        # This could be expanded with more sophisticated validation
        return True
    
    def get_legacy_aliases(self, data_type: str) -> List[str]:
        """
        Get legacy aliases for a data type.
        
        Args:
            data_type: Canonical data type
            
        Returns:
            List of legacy aliases
        """
        rule = self.get_ownership_rule(data_type)
        return rule.legacy_aliases if rule else []
    
    def get_all_data_types(self) -> List[str]:
        """
        Get all registered data types.
        
        Returns:
            List of all canonical data types
        """
        return list(self._ownership_rules.keys())
    
    def get_ownership_summary(self) -> Dict[str, Dict[str, Any]]:
        """
        Get a summary of all ownership rules for debugging.
        
        Returns:
            Dictionary with ownership information for all data types
        """
        summary = {}
        for data_type, rule in self._ownership_rules.items():
            summary[data_type] = {
                'owner': f"{rule.owner_component}.{rule.owner_property}",
                'access_pattern': rule.access_pattern.value,
                'description': rule.description,
                'legacy_aliases': rule.legacy_aliases,
                'validation_rules': rule.validation_rules
            }
        return summary


# Global registry instance
data_ownership_registry = DataOwnershipRegistry()
