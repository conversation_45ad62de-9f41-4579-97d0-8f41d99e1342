"""
Comprehensive File System Optimization for Adventure Chess Creator

This consolidated module provides comprehensive file system optimization including:
- File indexing for faster searches
- Content-based search capabilities
- Directory scanning optimization
- File compression support
- Performance monitoring and analytics
- Integration with existing data managers

Key Features:
- Full-text search across JSON content
- Metadata indexing (name, description, tags, etc.)
- Cached directory structures
- Compression for large datasets
- Search performance analytics
- Optimized file integration wrappers

Consolidates functionality from:
- file_system_optimizer.py (core optimization)
- optimized_file_integration.py (integration wrappers)
"""

import os
import json
import gzip
import sqlite3
import hashlib
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Set, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
import time

logger = logging.getLogger(__name__)

@dataclass
class FileIndexEntry:
    """Represents a file in the search index"""
    file_path: str
    filename: str
    file_type: str  # 'piece' or 'ability'
    size_bytes: int
    modified_time: datetime
    content_hash: str
    
    # Searchable content
    name: str
    description: str
    tags: List[str]
    abilities: List[str]  # For pieces
    full_text: str  # Complete searchable text
    
    # Metadata
    version: str
    author: Optional[str] = None
    category: Optional[str] = None

@dataclass
class SearchResult:
    """Represents a search result"""
    file_path: str
    filename: str
    file_type: str
    relevance_score: float
    matched_fields: List[str]
    preview_text: str
    metadata: Dict[str, Any]

class FileSystemOptimizer:
    """
    Comprehensive file system optimization and indexing system
    """
    
    def __init__(self, 
                 index_db_path: str = "data/file_index.db",
                 enable_compression: bool = False,
                 compression_threshold_kb: int = 50,
                 index_update_interval: int = 300):  # 5 minutes
        
        self.index_db_path = index_db_path
        self.enable_compression = enable_compression
        self.compression_threshold_kb = compression_threshold_kb
        self.index_update_interval = index_update_interval
        
        # Threading
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="FileOptimizer")
        self.index_lock = threading.RLock()
        
        # Performance tracking
        self.search_stats = {
            'total_searches': 0,
            'avg_search_time_ms': 0,
            'cache_hits': 0,
            'index_updates': 0
        }
        
        # Initialize database
        self._init_database()
        
        # Start background indexing
        self._start_background_indexing()
    
    def _init_database(self):
        """Initialize SQLite database for file indexing"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.index_db_path), exist_ok=True)
            
            with sqlite3.connect(self.index_db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS file_index (
                        file_path TEXT PRIMARY KEY,
                        filename TEXT NOT NULL,
                        file_type TEXT NOT NULL,
                        size_bytes INTEGER,
                        modified_time TIMESTAMP,
                        content_hash TEXT,
                        name TEXT,
                        description TEXT,
                        tags TEXT,  -- JSON array
                        abilities TEXT,  -- JSON array
                        full_text TEXT,
                        version TEXT,
                        author TEXT,
                        category TEXT,
                        indexed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes for fast searching
                conn.execute("CREATE INDEX IF NOT EXISTS idx_filename ON file_index(filename)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_file_type ON file_index(file_type)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_name ON file_index(name)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_modified_time ON file_index(modified_time)")
                
                # Full-text search index
                conn.execute("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS file_search USING fts5(
                        file_path,
                        filename,
                        name,
                        description,
                        tags,
                        abilities,
                        full_text,
                        content='file_index',
                        content_rowid='rowid'
                    )
                """)
                
                conn.commit()
                logger.info("File index database initialized successfully")
                
        except Exception as e:
            logger.error(f"Error initializing file index database: {e}")
            raise
    
    def _start_background_indexing(self):
        """Start background thread for periodic index updates"""
        def background_indexer():
            while True:
                try:
                    time.sleep(self.index_update_interval)
                    self.update_index()
                except Exception as e:
                    logger.error(f"Error in background indexing: {e}")
        
        indexer_thread = threading.Thread(target=background_indexer, daemon=True)
        indexer_thread.start()
        logger.info("Background indexing started")
    
    def update_index(self, directories: Optional[List[str]] = None) -> int:
        """
        Update the file index for specified directories
        Returns number of files indexed
        """
        if directories is None:
            from config import PIECES_DIR, ABILITIES_DIR
            directories = [PIECES_DIR, ABILITIES_DIR]
        
        indexed_count = 0
        
        with self.index_lock:
            try:
                with sqlite3.connect(self.index_db_path) as conn:
                    for directory in directories:
                        if not os.path.exists(directory):
                            continue
                        
                        file_type = 'piece' if 'piece' in str(directory).lower() else 'ability'
                        
                        for file_path in Path(directory).glob("*.json"):
                            try:
                                # Check if file needs indexing
                                if self._needs_indexing(str(file_path), conn):
                                    entry = self._create_index_entry(str(file_path), file_type)
                                    if entry:
                                        self._store_index_entry(entry, conn)
                                        indexed_count += 1
                                        
                            except Exception as e:
                                logger.warning(f"Error indexing {file_path}: {e}")
                    
                    # Update FTS index
                    conn.execute("INSERT INTO file_search(file_search) VALUES('rebuild')")
                    conn.commit()
                    
                self.search_stats['index_updates'] += 1
                logger.info(f"Index updated: {indexed_count} files processed")
                
            except Exception as e:
                logger.error(f"Error updating index: {e}")
        
        return indexed_count
    
    def _needs_indexing(self, file_path: str, conn: sqlite3.Connection) -> bool:
        """Check if file needs to be indexed or re-indexed"""
        try:
            file_stat = os.stat(file_path)
            file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
            
            cursor = conn.execute(
                "SELECT modified_time FROM file_index WHERE file_path = ?",
                (file_path,)
            )
            result = cursor.fetchone()
            
            if result is None:
                return True  # File not in index
            
            indexed_mtime = datetime.fromisoformat(result[0])
            return file_mtime > indexed_mtime  # File modified since last index
            
        except Exception:
            return True  # Error checking, re-index to be safe
    
    def _create_index_entry(self, file_path: str, file_type: str) -> Optional[FileIndexEntry]:
        """Create an index entry from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            file_stat = os.stat(file_path)
            content_str = json.dumps(data, sort_keys=True)
            content_hash = hashlib.md5(content_str.encode()).hexdigest()
            
            # Extract searchable content
            name = data.get('name', Path(file_path).stem)
            description = data.get('description', '')
            tags = data.get('tags', [])
            abilities = []
            
            if file_type == 'piece':
                abilities_data = data.get('abilities', {})
                if isinstance(abilities_data, dict):
                    abilities = list(abilities_data.keys())
                elif isinstance(abilities_data, list):
                    # Handle case where abilities is a list of ability names
                    abilities = abilities_data
                else:
                    abilities = []
            
            # Create full-text search content
            full_text_parts = [name, description]
            full_text_parts.extend(tags)
            full_text_parts.extend(abilities)
            
            # Add other searchable fields
            for key, value in data.items():
                if isinstance(value, str) and len(value) < 200:
                    full_text_parts.append(value)
            
            full_text = ' '.join(filter(None, full_text_parts))
            
            return FileIndexEntry(
                file_path=file_path,
                filename=Path(file_path).stem,
                file_type=file_type,
                size_bytes=file_stat.st_size,
                modified_time=datetime.fromtimestamp(file_stat.st_mtime),
                content_hash=content_hash,
                name=name,
                description=description,
                tags=tags,
                abilities=abilities,
                full_text=full_text,
                version=data.get('version', ''),
                author=data.get('author'),
                category=data.get('category')
            )
            
        except Exception as e:
            logger.error(f"Error creating index entry for {file_path}: {e}")
            return None
    
    def _store_index_entry(self, entry: FileIndexEntry, conn: sqlite3.Connection):
        """Store an index entry in the database"""
        conn.execute("""
            INSERT OR REPLACE INTO file_index (
                file_path, filename, file_type, size_bytes, modified_time,
                content_hash, name, description, tags, abilities, full_text,
                version, author, category
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            entry.file_path, entry.filename, entry.file_type, entry.size_bytes,
            entry.modified_time.isoformat(), entry.content_hash, entry.name,
            entry.description, json.dumps(entry.tags), json.dumps(entry.abilities),
            entry.full_text, entry.version, entry.author, entry.category
        ))

    def search_files(self,
                    query: str,
                    file_type: Optional[str] = None,
                    max_results: int = 50,
                    include_content: bool = True) -> List[SearchResult]:
        """
        Search files using the index

        Args:
            query: Search query (supports full-text search)
            file_type: Filter by 'piece' or 'ability' (None for all)
            max_results: Maximum number of results
            include_content: Whether to include full content in results

        Returns:
            List of SearchResult objects sorted by relevance
        """
        start_time = time.time()
        results = []

        try:
            with sqlite3.connect(self.index_db_path) as conn:
                # Build search query
                if query.strip():
                    # Use FTS for full-text search
                    fts_query = f'"{query}"'  # Exact phrase search
                    sql = """
                        SELECT fi.*, rank
                        FROM file_search fs
                        JOIN file_index fi ON fs.rowid = fi.rowid
                        WHERE fs.file_search MATCH ?
                    """
                    params = [fts_query]

                    if file_type:
                        sql += " AND fi.file_type = ?"
                        params.append(file_type)

                    sql += " ORDER BY rank LIMIT ?"
                    params.append(max_results)

                else:
                    # No query, return recent files
                    sql = """
                        SELECT *, 1.0 as rank
                        FROM file_index
                        WHERE 1=1
                    """
                    params = []

                    if file_type:
                        sql += " AND file_type = ?"
                        params.append(file_type)

                    sql += " ORDER BY modified_time DESC LIMIT ?"
                    params.append(max_results)

                cursor = conn.execute(sql, params)
                rows = cursor.fetchall()

                # Convert to SearchResult objects
                for row in rows:
                    try:
                        # Calculate relevance score
                        relevance = self._calculate_relevance(query, row, include_content)

                        # Determine matched fields
                        matched_fields = self._find_matched_fields(query, row)

                        # Create preview text
                        preview = self._create_preview(query, row)

                        # Create metadata
                        metadata = {
                            'size_bytes': row[4],
                            'modified_time': row[5],
                            'version': row[12],
                            'author': row[13],
                            'category': row[14],
                            'tags': json.loads(row[8]) if row[8] else [],
                            'abilities': json.loads(row[9]) if row[9] else []
                        }

                        result = SearchResult(
                            file_path=row[0],
                            filename=row[1],
                            file_type=row[2],
                            relevance_score=relevance,
                            matched_fields=matched_fields,
                            preview_text=preview,
                            metadata=metadata
                        )

                        results.append(result)

                    except Exception as e:
                        logger.warning(f"Error processing search result: {e}")

                # Sort by relevance score
                results.sort(key=lambda x: x.relevance_score, reverse=True)

        except Exception as e:
            logger.error(f"Error searching files: {e}")

        # Update search statistics
        search_time = (time.time() - start_time) * 1000
        self.search_stats['total_searches'] += 1
        self.search_stats['avg_search_time_ms'] = (
            (self.search_stats['avg_search_time_ms'] * (self.search_stats['total_searches'] - 1) + search_time) /
            self.search_stats['total_searches']
        )

        logger.debug(f"Search completed in {search_time:.2f}ms, {len(results)} results")
        return results

    def _calculate_relevance(self, query: str, row: tuple, include_content: bool) -> float:
        """Calculate relevance score for a search result"""
        if not query.strip():
            return 1.0

        query_lower = query.lower()
        score = 0.0

        # Name match (highest weight)
        name = row[7] or ""
        if query_lower in name.lower():
            score += 10.0
            if name.lower() == query_lower:
                score += 20.0  # Exact match bonus

        # Filename match
        filename = row[1] or ""
        if query_lower in filename.lower():
            score += 5.0

        # Description match
        description = row[8] or ""
        if query_lower in description.lower():
            score += 3.0

        # Tags match
        tags_json = row[8] or "[]"
        try:
            tags = json.loads(tags_json)
            for tag in tags:
                if query_lower in tag.lower():
                    score += 2.0
        except:
            pass

        # Full text match (lower weight)
        full_text = row[10] or ""
        if query_lower in full_text.lower():
            score += 1.0

        return score

    def _find_matched_fields(self, query: str, row: tuple) -> List[str]:
        """Find which fields matched the search query"""
        if not query.strip():
            return []

        query_lower = query.lower()
        matched = []

        if query_lower in (row[7] or "").lower():
            matched.append("name")
        if query_lower in (row[1] or "").lower():
            matched.append("filename")
        if query_lower in (row[8] or "").lower():
            matched.append("description")

        return matched

    def _create_preview(self, query: str, row: tuple, max_length: int = 200) -> str:
        """Create a preview text snippet for the search result"""
        name = row[7] or ""
        description = row[8] or ""

        if description:
            preview = f"{name}: {description}"
        else:
            preview = name

        if len(preview) > max_length:
            preview = preview[:max_length-3] + "..."

        return preview

    def get_file_suggestions(self, partial_name: str, file_type: Optional[str] = None, limit: int = 10) -> List[str]:
        """
        Get file name suggestions for autocomplete
        """
        try:
            with sqlite3.connect(self.index_db_path) as conn:
                # Search both filename and name fields, prioritize filename matches
                sql = """
                    SELECT filename, name,
                           CASE
                               WHEN filename LIKE ? THEN 1
                               WHEN name LIKE ? THEN 2
                               ELSE 3
                           END as priority
                    FROM file_index
                    WHERE (filename LIKE ? OR name LIKE ?)
                """
                search_pattern = f"%{partial_name}%"
                params = [search_pattern, search_pattern, search_pattern, search_pattern]

                if file_type:
                    sql += " AND file_type = ?"
                    params.append(file_type)

                sql += " ORDER BY priority, filename LIMIT ?"
                params.append(limit * 2)  # Get more results to filter duplicates

                cursor = conn.execute(sql, params)
                results = cursor.fetchall()

                suggestions = []
                seen = set()

                for row in results:
                    filename, name, priority = row

                    # Add filename if not seen
                    if filename not in seen:
                        suggestions.append(filename)
                        seen.add(filename)

                    # Add name if different from filename and not seen
                    if name and name != filename and name not in seen:
                        suggestions.append(name)
                        seen.add(name)

                    if len(suggestions) >= limit:
                        break

                return suggestions[:limit]

        except Exception as e:
            logger.error(f"Error getting file suggestions: {e}")
            return []

    def get_index_statistics(self) -> Dict[str, Any]:
        """Get statistics about the file index"""
        try:
            with sqlite3.connect(self.index_db_path) as conn:
                # File counts by type
                cursor = conn.execute("""
                    SELECT file_type, COUNT(*), AVG(size_bytes), MAX(modified_time)
                    FROM file_index
                    GROUP BY file_type
                """)
                type_stats = {}
                for row in cursor.fetchall():
                    type_stats[row[0]] = {
                        'count': row[1],
                        'avg_size_bytes': row[2],
                        'last_modified': row[3]
                    }

                # Total statistics
                cursor = conn.execute("""
                    SELECT COUNT(*), SUM(size_bytes), MAX(indexed_time)
                    FROM file_index
                """)
                total_row = cursor.fetchone()

                return {
                    'total_files': total_row[0],
                    'total_size_bytes': total_row[1] or 0,
                    'last_index_update': total_row[2],
                    'by_type': type_stats,
                    'search_stats': self.search_stats.copy()
                }

        except Exception as e:
            logger.error(f"Error getting index statistics: {e}")
            return {}

    def optimize_directory_scanning(self, directory: str) -> Dict[str, Any]:
        """
        Optimize directory scanning by caching directory structure
        """
        try:
            start_time = time.time()

            # Get cached directory info
            cache_key = f"dir_scan_{directory}"
            cached_info = self._get_directory_cache(cache_key)

            if cached_info and self._is_directory_cache_valid(cached_info, directory):
                scan_time = (time.time() - start_time) * 1000
                return {
                    'files': cached_info['files'],
                    'scan_time_ms': scan_time,
                    'cache_hit': True,
                    'file_count': len(cached_info['files'])
                }

            # Scan directory
            files = []
            if os.path.exists(directory):
                for file_path in Path(directory).glob("*.json"):
                    stat = file_path.stat()
                    files.append({
                        'path': str(file_path),
                        'name': file_path.stem,
                        'size': stat.st_size,
                        'modified': stat.st_mtime
                    })

            # Cache results
            cache_data = {
                'files': files,
                'scan_time': time.time(),
                'directory_mtime': os.path.getmtime(directory) if os.path.exists(directory) else 0
            }
            self._set_directory_cache(cache_key, cache_data)

            scan_time = (time.time() - start_time) * 1000
            return {
                'files': files,
                'scan_time_ms': scan_time,
                'cache_hit': False,
                'file_count': len(files)
            }

        except Exception as e:
            logger.error(f"Error optimizing directory scan for {directory}: {e}")
            return {'files': [], 'scan_time_ms': 0, 'cache_hit': False, 'file_count': 0}

    def _get_directory_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached directory information"""
        # Simple in-memory cache for now
        if not hasattr(self, '_dir_cache'):
            self._dir_cache = {}
        return self._dir_cache.get(cache_key)

    def _set_directory_cache(self, cache_key: str, data: Dict[str, Any]):
        """Set cached directory information"""
        if not hasattr(self, '_dir_cache'):
            self._dir_cache = {}
        self._dir_cache[cache_key] = data

    def _is_directory_cache_valid(self, cached_info: Dict[str, Any], directory: str) -> bool:
        """Check if cached directory information is still valid"""
        try:
            # Check if cache is too old (5 minutes)
            if time.time() - cached_info['scan_time'] > 300:
                return False

            # Check if directory was modified
            if os.path.exists(directory):
                current_mtime = os.path.getmtime(directory)
                if current_mtime > cached_info['directory_mtime']:
                    return False

            return True

        except Exception:
            return False

    def compress_large_files(self, threshold_kb: Optional[int] = None) -> Dict[str, Any]:
        """
        Compress files larger than threshold (if compression is enabled)
        """
        if not self.enable_compression:
            return {'compressed_files': 0, 'space_saved_bytes': 0, 'message': 'Compression disabled'}

        threshold = (threshold_kb or self.compression_threshold_kb) * 1024
        compressed_files = 0
        space_saved = 0

        try:
            with sqlite3.connect(self.index_db_path) as conn:
                cursor = conn.execute("""
                    SELECT file_path, size_bytes
                    FROM file_index
                    WHERE size_bytes > ?
                """, (threshold,))

                for file_path, size_bytes in cursor.fetchall():
                    if self._compress_file(file_path):
                        compressed_files += 1
                        # Estimate space saved (typical JSON compression ratio ~70%)
                        space_saved += int(size_bytes * 0.7)

            return {
                'compressed_files': compressed_files,
                'space_saved_bytes': space_saved,
                'threshold_kb': threshold // 1024
            }

        except Exception as e:
            logger.error(f"Error compressing files: {e}")
            return {'compressed_files': 0, 'space_saved_bytes': 0, 'error': str(e)}

    def _compress_file(self, file_path: str) -> bool:
        """Compress a single file"""
        try:
            compressed_path = f"{file_path}.gz"

            # Skip if already compressed
            if os.path.exists(compressed_path):
                return False

            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.write(f_in.read())

            # Verify compression worked and saved space
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(compressed_path)

            if compressed_size < original_size * 0.9:  # At least 10% savings
                # Keep both files for now (could remove original in production)
                logger.info(f"Compressed {file_path}: {original_size} -> {compressed_size} bytes")
                return True
            else:
                # Remove compressed file if no significant savings
                os.remove(compressed_path)
                return False

        except Exception as e:
            logger.error(f"Error compressing {file_path}: {e}")
            return False

    def shutdown(self):
        """Shutdown the optimizer and cleanup resources"""
        try:
            # Use shutdown without timeout for compatibility
            self.executor.shutdown(wait=True)
            logger.info("File system optimizer shutdown complete")
        except Exception as e:
            logger.error(f"Error during optimizer shutdown: {e}")

# Global optimizer instance
_optimizer_instance: Optional[FileSystemOptimizer] = None

def get_file_system_optimizer() -> FileSystemOptimizer:
    """Get or create the global file system optimizer instance"""
    global _optimizer_instance
    if _optimizer_instance is None:
        _optimizer_instance = FileSystemOptimizer()
    return _optimizer_instance

def reset_optimizer():
    """Reset the global optimizer (for testing)"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.shutdown()
    _optimizer_instance = None


# ========== OPTIMIZED FILE INTEGRATION ==========

class OptimizedDataManager:
    """
    Enhanced data manager that combines lazy loading with file system optimization
    """

    def __init__(self):
        # Import here to avoid circular imports
        try:
            from .lazy_loading_system import get_lazy_data_manager
            self.lazy_manager = get_lazy_data_manager()
        except ImportError:
            self.lazy_manager = None
            logger.warning("Lazy loading system not available")

        self.optimizer = get_file_system_optimizer()

        # Initialize index on startup
        self._initialize_index()

    def _initialize_index(self):
        """Initialize the file index in background"""
        def index_worker():
            try:
                # Import config constants with fallbacks
                try:
                    from config import PIECES_DIR, ABILITIES_DIR
                    directories = [PIECES_DIR, ABILITIES_DIR]
                except ImportError:
                    directories = ['data/pieces', 'data/abilities']

                indexed_count = self.optimizer.update_index(directories)
                logger.info(f"File index initialized: {indexed_count} files indexed")
            except Exception as e:
                logger.error(f"Error initializing file index: {e}")

        # Run indexing in background
        import threading
        index_thread = threading.Thread(target=index_worker, daemon=True)
        index_thread.start()

    def search_pieces(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """Search pieces using the optimized index"""
        return self.optimizer.search_files(
            query=query,
            file_type="piece",
            max_results=max_results
        )

    def search_abilities(self, query: str, max_results: int = 20) -> List[SearchResult]:
        """Search abilities using the optimized index"""
        return self.optimizer.search_files(
            query=query,
            file_type="ability",
            max_results=max_results
        )

    def get_piece_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """Get piece name suggestions for autocomplete"""
        return self.optimizer.get_file_suggestions(
            partial_name=partial_name,
            file_type="piece",
            limit=limit
        )

    def get_ability_suggestions(self, partial_name: str, limit: int = 10) -> List[str]:
        """Get ability name suggestions for autocomplete"""
        return self.optimizer.get_file_suggestions(
            partial_name=partial_name,
            file_type="ability",
            limit=limit
        )

    def get_optimized_file_list(self, directory: str) -> Dict[str, Any]:
        """Get optimized file list using cached directory scanning"""
        return self.optimizer.optimize_directory_scanning(directory)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get combined performance statistics"""
        optimizer_stats = self.optimizer.get_index_statistics()

        # Combine with lazy loading stats if available
        combined_stats = {
            'file_system_optimization': optimizer_stats,
            'lazy_loading': {
                'cache_manager_active': self.lazy_manager is not None,
                'lazy_manager_active': self.lazy_manager is not None
            }
        }

        # Add cache stats if available
        if self.lazy_manager and hasattr(self.lazy_manager, 'cache_manager'):
            try:
                cache_stats = self.lazy_manager.cache_manager.get_cache_stats()
                combined_stats['lazy_loading']['cache_stats'] = cache_stats
            except Exception as e:
                logger.warning(f"Could not get cache stats: {e}")

        return combined_stats

    def load_piece_optimized(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                           priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load piece with optimization hints"""
        # Use lazy loading if available
        if self.lazy_manager:
            return self.lazy_manager.load_piece_lazy(filename, callback, priority)

        # Fallback to direct loading
        try:
            from config import PIECES_DIR
            file_path = Path(PIECES_DIR) / f"{filename}.json"
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if callback:
                    callback(data)
                return data
        except Exception as e:
            logger.error(f"Error loading piece {filename}: {e}")
            return None

    def load_ability_optimized(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                             priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load ability with optimization hints"""
        # Use lazy loading if available
        if self.lazy_manager:
            return self.lazy_manager.load_ability_lazy(filename, callback, priority)

        # Fallback to direct loading
        try:
            from config import ABILITIES_DIR
            file_path = Path(ABILITIES_DIR) / f"{filename}.json"
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if callback:
                    callback(data)
                return data
        except Exception as e:
            logger.error(f"Error loading ability {filename}: {e}")
            return None

    def refresh_index(self, directories: Optional[List[str]] = None):
        """Refresh the file index"""
        if directories is None:
            try:
                from config import PIECES_DIR, ABILITIES_DIR
                directories = [PIECES_DIR, ABILITIES_DIR]
            except ImportError:
                directories = ['data/pieces', 'data/abilities']

        return self.optimizer.update_index(directories)

    def shutdown(self):
        """Shutdown the optimized data manager"""
        if self.optimizer:
            self.optimizer.shutdown()
        if self.lazy_manager and hasattr(self.lazy_manager, 'shutdown'):
            self.lazy_manager.shutdown()


# Global optimized data manager instance
_optimized_data_manager: Optional[OptimizedDataManager] = None

def get_optimized_data_manager() -> OptimizedDataManager:
    """Get or create the global optimized data manager"""
    global _optimized_data_manager
    if _optimized_data_manager is None:
        _optimized_data_manager = OptimizedDataManager()
    return _optimized_data_manager

def reset_optimized_data_manager():
    """Reset the global optimized data manager (for testing)"""
    global _optimized_data_manager
    if _optimized_data_manager:
        _optimized_data_manager.shutdown()
    _optimized_data_manager = None
