"""
Piece Data Handlers for Adventure Chess Creator

This module handles all data operations for the piece editor:
- Loading and saving piece data
- Data validation and error handling
- Movement pattern data processing
- Promotion data management
- Integration with BaseEditor data methods

Extracted from the original piece_editor.py to improve maintainability
and make data operations easier to debug and test.
"""

import logging
import os
from typing import Dict, Any, List, Tuple, Optional
from PyQt6.QtWidgets import QMessageBox

from config import PIECES_DIR
from utils.simple_bridge import simple_bridge

logger = logging.getLogger(__name__)


class PieceDataHandler:
    """
    Handles all data operations for the piece editor.
    
    This class manages:
    - Loading and saving piece data
    - Data validation and error handling
    - Movement pattern data processing
    - Promotion data management
    - File operations and error handling
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the data handler.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
        
    def load_piece_data(self, piece_data: Dict[str, Any]) -> None:
        """
        Load piece data into the form using standardized BaseEditor approach.
        
        Args:
            piece_data: Dictionary containing piece data
        """
        try:
            logger.info(f"Loading piece data with {len(piece_data)} fields...")

            # Reset pattern data to defaults before loading
            self.reset_pattern_data()

            # Use BaseEditor's standardized widget setting
            self.editor.set_widget_values_from_data(piece_data)

            # Load movement pattern data specifically
            self.load_movement_pattern_data(piece_data)

            # Update UI components after loading
            self.editor.update_movement_controls()
            self.editor.update_recharge_options()
            self.editor.update_icon_previews()
            self.editor.update_movement_pattern_preview()
            self.editor.refresh_abilities_list()
            self.editor.on_role_changed()

            logger.info("Piece data loaded and validated successfully")

        except Exception as e:
            logger.error(f"Error loading piece data: {e}")
            raise

    def reset_pattern_data(self) -> None:
        """Reset pattern data to defaults before loading new piece."""
        self.editor.current_custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.editor.custom_pattern_piece_pos = [3, 3]
        self.editor.selected_movement_type = "orthogonal"
        self.editor.current_movement_data = {
            "type": "orthogonal", 
            "pattern": None, 
            "piecePosition": [3, 3]
        }

    def load_movement_pattern_data(self, piece_data: Dict[str, Any]) -> None:
        """
        Load movement pattern data from piece data.
        
        Args:
            piece_data: Dictionary containing piece data
        """
        if 'movement' not in piece_data:
            return
            
        movement = piece_data['movement']

        # Set movement type
        movement_type = movement.get('type', 'orthogonal')
        self.editor.selected_movement_type = movement_type

        # Load pattern if it exists
        if 'pattern' in movement:
            self.editor.current_custom_pattern = movement['pattern']

        # Load piece position if it exists
        if 'piecePosition' in movement:
            self.editor.custom_pattern_piece_pos = movement['piecePosition']

        # Update movement data
        self.editor.current_movement_data = {
            "type": self.editor.selected_movement_type,
            "pattern": self.editor.current_custom_pattern,
            "piecePosition": self.editor.custom_pattern_piece_pos
        }

        # If it's a standard movement type, generate the pattern
        if movement_type != "custom" and 'pattern' not in movement:
            standard_pattern = self.editor.generate_standard_pattern(
                movement_type, 
                self.editor.custom_pattern_piece_pos
            )
            self.editor.current_custom_pattern = standard_pattern
            self.editor.current_movement_data["pattern"] = standard_pattern

        logger.info(f"Loaded movement pattern: {movement_type}")

        # Update movement button selection
        if hasattr(self.editor, 'movement_pattern_buttons'):
            for btn, btn_movement_type in self.editor.movement_pattern_buttons:
                btn.setChecked(btn_movement_type == self.editor.selected_movement_type)

    def validate_piece_data(self, data: Dict[str, Any]) -> List[str]:
        """
        Validate piece data for consistency and completeness.
        
        Args:
            data: Dictionary containing piece data
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        try:
            # Check required fields
            if not data.get('name', '').strip():
                errors.append("Piece name is required")
            
            # Check role is valid
            valid_roles = ["Commander", "Warrior", "Support", "Specialist"]
            role = data.get('role', 'Commander')
            if role not in valid_roles:
                errors.append(f"Invalid role: {role}")
            
            # Check points are valid
            max_points = data.get('maxPoints', 0)
            starting_points = data.get('startingPoints', 0)
            if not isinstance(max_points, (int, float)) or max_points < 0:
                errors.append("Max points must be a non-negative number")
            if not isinstance(starting_points, (int, float)) or starting_points < 0:
                errors.append("Starting points must be a non-negative number")
            if starting_points > max_points:
                errors.append("Starting points cannot exceed max points")
            
            # Check movement data
            movement = data.get('movement', {})
            if not isinstance(movement, dict):
                errors.append("Movement data must be a dictionary")
            else:
                movement_type = movement.get('type', 'orthogonal')
                valid_types = ['orthogonal', 'diagonal', 'any', 'lShape', 'king', 'global', 'custom']
                if movement_type not in valid_types:
                    errors.append(f"Invalid movement type: {movement_type}")
            
            # Check abilities are valid
            abilities = data.get('abilities', [])
            if not isinstance(abilities, list):
                errors.append("Abilities must be a list")
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors

    def verify_pattern_matches_type(self, movement_data: Dict[str, Any]) -> Tuple[str, bool]:
        """
        Verify if a custom pattern actually matches a standard movement type.
        
        Args:
            movement_data: Dictionary containing movement data
            
        Returns:
            Tuple of (pattern_type, is_quick_pattern)
        """
        pattern = movement_data.get('pattern')
        piece_pos = movement_data.get('piecePosition', [3, 3])
        movement_type = movement_data.get('type', 'orthogonal')

        if not pattern or not isinstance(pattern, list):
            return movement_type, True

        # Generate standard patterns for comparison
        standard_patterns = {
            'orthogonal': self.editor.generate_standard_pattern('orthogonal', piece_pos),
            'diagonal': self.editor.generate_standard_pattern('diagonal', piece_pos),
            'any': self.editor.generate_standard_pattern('any', piece_pos),
            'king': self.editor.generate_standard_pattern('king', piece_pos),
            'lShape': self.editor.generate_standard_pattern('lShape', piece_pos),
        }

        # Check if pattern matches any standard type
        for pattern_type, standard_pattern in standard_patterns.items():
            if pattern == standard_pattern:
                return pattern_type, True

        # If no match found, it's a custom pattern
        return 'custom', False

    def refresh_file_lists(self) -> None:
        """Refresh piece file lists in UI components."""
        try:
            # Get list of piece files
            piece_files = []
            if os.path.exists(PIECES_DIR):
                piece_files = [f for f in os.listdir(PIECES_DIR) if f.endswith('.json')]

            # Update load piece combo if it exists
            if hasattr(self.editor, 'load_piece_combo'):
                self.editor.load_piece_combo.clear()
                self.editor.load_piece_combo.addItem("Select piece to load...")
                
                for piece_file in sorted(piece_files):
                    try:
                        base_filename = piece_file.replace('.json', '')
                        piece_data, error = simple_bridge.load_piece_for_ui(base_filename)
                        if piece_data and not error:
                            piece_name = piece_data.get('name', piece_file[:-5])
                            display_text = f"{piece_name}"
                            tooltip_text = f"File: {piece_file}\nDescription: {piece_data.get('description', 'No description')}"
                        else:
                            display_text = f"❌ {piece_file[:-5]} (Error)"
                            tooltip_text = f"File: {piece_file}\nError: {error or 'Unknown error'}"
                    except Exception as e:
                        display_text = f"❌ {piece_file[:-5]} (Error)"
                        tooltip_text = f"File: {piece_file}\nError: {str(e)}"
                    
                    self.editor.load_piece_combo.addItem(display_text)
                    self.editor.load_piece_combo.setItemData(
                        self.editor.load_piece_combo.count() - 1, 
                        tooltip_text, 
                        3  # ToolTipRole
                    )

        except Exception as e:
            logger.error(f"Error refreshing file lists: {e}")

    def load_piece_from_dropdown(self, piece_filename: str) -> None:
        """
        Load a piece from the dropdown selection.

        Args:
            piece_filename: Display name of the piece (piece name, not filename)
        """
        if not piece_filename or piece_filename == "Select piece to load...":
            return

        try:
            # The dropdown shows piece names, not filenames
            # We need to find the actual filename by searching through piece files
            actual_filename = None

            if os.path.exists(PIECES_DIR):
                piece_files = [f for f in os.listdir(PIECES_DIR) if f.endswith('.json')]

                for piece_file in piece_files:
                    try:
                        base_filename = piece_file.replace('.json', '')
                        piece_data, error = simple_bridge.load_piece_for_ui(base_filename)

                        if piece_data and not error:
                            piece_name = piece_data.get('name', base_filename)
                            if piece_name == piece_filename:
                                actual_filename = base_filename
                                break
                    except Exception:
                        continue

            if not actual_filename:
                QMessageBox.critical(self.editor, "Error", f"Could not find piece file for: {piece_filename}")
                return

            piece_data, error = simple_bridge.load_piece_for_ui(actual_filename)
            if error:
                QMessageBox.critical(self.editor, "Error", f"Failed to load piece: {error}")
                return

            self.load_piece_data(piece_data)

            # Set the current filename so saves go to the original file
            self.editor.current_filename = actual_filename

            if hasattr(self.editor, 'status_widget'):
                self.editor.status_widget.show_success(f"Loaded piece: {piece_data.get('name', 'Unknown')}")
            if hasattr(self.editor, 'log_console'):
                self.editor.log_console.append(f"Loaded piece '{piece_filename}' from {actual_filename}.json - will save to same file")
            
            self.editor.clear_unsaved_changes()

            # Reset dropdown to placeholder
            if hasattr(self.editor, 'load_piece_combo'):
                self.editor.load_piece_combo.setCurrentIndex(0)

        except Exception as e:
            QMessageBox.critical(self.editor, "Error", f"Failed to load piece: {str(e)}")
            logger.error(f"Error loading piece from dropdown: {e}")

    def create_default_piece_data(self) -> Dict[str, Any]:
        """
        Create default piece data structure.

        Returns:
            Dictionary containing default piece data
        """
        return {
            "version": "1.0.0",
            "name": "",
            "description": "",
            "role": "Commander",
            "canCastle": False,
            "movement": {"type": "orthogonal", "pattern": None, "piecePosition": [3, 3]},
            "colorDirectional": False,
            "abilities": [],
            "maxPoints": 1,  # Updated default to 1
            "startingPoints": 1,  # Updated default to 1
            "rechargeType": "turnRecharge"
        }

    def save_data(self, filename: Optional[str] = None) -> bool:
        """
        Save piece data using the BaseEditor's standardized save method.

        Args:
            filename: Optional filename override

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.editor.save_data(filename)
        except Exception as e:
            logger.error(f"Error saving piece data: {e}")
            return False

    def save_data_as(self, filename: str) -> bool:
        """
        Save piece data with a new filename.

        Args:
            filename: New filename to save to

        Returns:
            True if successful, False otherwise
        """
        try:
            return self.editor.save_data(filename)
        except Exception as e:
            logger.error(f"Error saving piece data as: {e}")
            return False
