#!/usr/bin/env python3
"""
Reusable Target Range Dialog for Adventure Chess
Used for selecting target squares/ranges in abilities and adjacency configurations
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QCheckBox)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from ui.ui_shared_components import create_legend_item, create_dialog_buttons, create_grid_instructions

class TargetRangeDialog(QDialog):
    """
    Target range dialog for selecting target squares/ranges
    Used for abilities and adjacency configurations
    """

    # Class-level state storage for checkbox persistence
    _last_checkbox_state = {
        'starting_square_checked': False,
        'continue_off_board_checked': False
    }

    def __init__(self, initial_pattern=None, piece_position=None, title="Target Range Editor", parent=None, checkbox_states=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumSize(350, 450)

        # Initialize pattern - convert to boolean for range editor
        if initial_pattern is None:
            self.pattern = [[False for _ in range(8)] for _ in range(8)]
        else:
            # Convert different pattern formats to boolean
            self.pattern = []
            for row in initial_pattern:
                new_row = []
                for cell in row:
                    # Handle different data types (bool, int, etc.)
                    if isinstance(cell, bool):
                        new_row.append(cell)
                    elif isinstance(cell, int):
                        new_row.append(cell > 0)  # Convert int to bool
                    else:
                        new_row.append(bool(cell))
                self.pattern.append(new_row)

        # Piece position (use provided position or default to center)
        if piece_position is not None and len(piece_position) == 2:
            self.piece_pos = piece_position[:]  # Copy the position
        else:
            self.piece_pos = [3, 3]  # Default to center

        # Initialize checkbox states from parameter or defaults
        if checkbox_states is not None:
            self.include_starting_square = checkbox_states.get('starting_square_checked', False)
            self.continue_off_board = checkbox_states.get('continue_off_board_checked', False)
        else:
            self.include_starting_square = False
            self.continue_off_board = False

        self.setup_ui()
        self.update_visual()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout()
        
        # Title
        title_label = QLabel("Click tiles to set range. Right-click to move piece position.")
        title_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(title_label)

        # Starting square checkbox
        self.starting_square_check = QCheckBox("Include Starting Square")
        self.starting_square_check.setToolTip("Allow targeting the piece's starting position")
        self.starting_square_check.stateChanged.connect(self.on_starting_square_changed)
        layout.addWidget(self.starting_square_check)

        # Continue off board checkbox
        self.continue_off_board_check = QCheckBox("Continue Off Board")
        self.continue_off_board_check.setToolTip("continues board pattern off edges of map")
        self.continue_off_board_check.stateChanged.connect(self.on_continue_off_board_changed)
        layout.addWidget(self.continue_off_board_check)

        # Set checkbox states from initialization
        self.starting_square_check.setChecked(self.include_starting_square)
        self.continue_off_board_check.setChecked(self.continue_off_board)

        # Quick Patterns
        pattern_layout = QHBoxLayout()
        pattern_layout.addWidget(QLabel("Quick Patterns:"))

        # Chess piece pattern buttons with styling
        pattern_buttons = [
            ("♜", "rook", "Orthogonal lines (like Rook)"),
            ("♝", "bishop", "Diagonal lines (like Bishop)"),
            ("♛", "queen", "All directions (like Queen)"),
            ("♞", "knight", "L-shaped moves (like Knight)"),
            ("♚", "king", "Adjacent squares (like King)"),
            ("🌐", "global", "Entire board range")
        ]

        # Track pattern buttons for highlighting
        self.range_preset_buttons = []
        self.current_range_preset_type = None

        for symbol, preset_type, tooltip in pattern_buttons:
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip}")

            # Store button reference with preset type
            self.range_preset_buttons.append((btn, preset_type))

            # Set initial styling (normal style)
            self.update_range_preset_button_style(btn, preset_type)

            btn.clicked.connect(lambda checked, pt=preset_type: self.select_range_preset(pt))
            pattern_layout.addWidget(btn)

        pattern_layout.addStretch()

        clear_preset_btn = QPushButton("Clear")
        clear_preset_btn.clicked.connect(self.clear_pattern)
        pattern_layout.addWidget(clear_preset_btn)

        layout.addLayout(pattern_layout)
        
        # Grid
        self.grid = []
        grid_layout = QGridLayout()
        
        for row in range(8):
            row_btns = []
            for col in range(8):
                btn = QPushButton()
                btn.setFixedSize(35, 35)
                btn.setCheckable(True)
                btn.setStyleSheet("background: #eee; border: 1px solid #ccc;")
                btn.clicked.connect(lambda checked, r=row, c=col: self.toggle_tile(r, c))
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(lambda pos, r=row, c=col: self.move_piece(r, c))
                grid_layout.addWidget(btn, row, col)
                row_btns.append(btn)
            self.grid.append(row_btns)
        
        layout.addLayout(grid_layout)
        
        # Legend using shared utilities
        legend_layout = QHBoxLayout()
        legend_layout.addWidget(QLabel("Legend:"))

        # Blue - Piece position
        blue_btn, blue_label = create_legend_item("#3399ff", "#003366", "Piece Position")
        blue_btn.setStyleSheet("background: #3399ff; border: 2px solid #003366;")
        legend_layout.addWidget(blue_btn)
        legend_layout.addWidget(blue_label)

        # Green - In range
        green_btn, green_label = create_legend_item("#44aa44", "#006600", "In Range")
        legend_layout.addWidget(green_btn)
        legend_layout.addWidget(green_label)

        # Gray - Out of range
        gray_btn, gray_label = create_legend_item("#eee", "#ccc", "Out of Range")
        legend_layout.addWidget(gray_btn)
        legend_layout.addWidget(gray_label)

        legend_layout.addStretch()
        layout.addLayout(legend_layout)
        
        # Instructions using shared utility
        instructions = create_grid_instructions("Right-click on any tile to move the piece position.\nClick tiles to toggle their range status.")
        layout.addWidget(instructions)
        
        # Preset buttons
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("Presets:"))
        
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_pattern)
        preset_layout.addWidget(clear_btn)
        
        radius1_btn = QPushButton("Radius 1")
        radius1_btn.clicked.connect(lambda: self.set_radius_pattern(1))
        preset_layout.addWidget(radius1_btn)
        
        radius2_btn = QPushButton("Radius 2")
        radius2_btn.clicked.connect(lambda: self.set_radius_pattern(2))
        preset_layout.addWidget(radius2_btn)
        
        radius3_btn = QPushButton("Radius 3")
        radius3_btn.clicked.connect(lambda: self.set_radius_pattern(3))
        preset_layout.addWidget(radius3_btn)
        
        preset_layout.addStretch()
        layout.addLayout(preset_layout)
        
        # Buttons using shared utility
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        save_btn, cancel_btn = create_dialog_buttons("Save Range", "Cancel")
        cancel_btn.clicked.connect(self.reject)
        save_btn.clicked.connect(self.accept)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def move_piece(self, row, col):
        """Move the piece to a new position"""
        self.piece_pos = [row, col]
        self.update_visual()

    def on_starting_square_changed(self, state):
        """Handle starting square checkbox change"""
        self.include_starting_square = state == Qt.CheckState.Checked.value

    def on_continue_off_board_changed(self, state):
        """Handle continue off board checkbox change"""
        self.continue_off_board = state == Qt.CheckState.Checked.value

    def save_checkbox_states(self):
        """Save current checkbox states for persistence (both class-level and JSON)"""
        # Update class-level state for immediate use
        RangeEditorDialog._last_checkbox_state = {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }

    def restore_checkbox_states(self):
        """Restore checkbox states from saved state"""
        state = RangeEditorDialog._last_checkbox_state
        self.starting_square_check.setChecked(state.get('starting_square_checked', False))
        self.continue_off_board_check.setChecked(state.get('continue_off_board_checked', False))

    def reset_to_defaults(self):
        """Reset checkbox states to default values"""
        self.starting_square_check.setChecked(False)
        self.continue_off_board_check.setChecked(False)
        # Update class-level state to reflect defaults
        RangeEditorDialog._last_checkbox_state = {
            'starting_square_checked': False,
            'continue_off_board_checked': False
        }

    def accept(self):
        """Override accept to save checkbox states before closing"""
        self.save_checkbox_states()
        super().accept()
    
    def toggle_tile(self, row, col):
        """Toggle tile range state"""
        if [row, col] == self.piece_pos:
            return  # Can't toggle piece position
        
        self.pattern[row][col] = not self.pattern[row][col]
        self.update_visual()
    
    def update_visual(self):
        """Update the visual display of the grid"""
        for r in range(8):
            for c in range(8):
                btn = self.grid[r][c]
                
                if [r, c] == self.piece_pos:
                    # Piece position
                    btn.setStyleSheet("background: #3399ff; border: 2px solid #003366; font-weight: bold;")
                    btn.setText("♔")
                else:
                    btn.setText("")
                    if self.pattern[r][c]:
                        # In range
                        btn.setStyleSheet("background: #44aa44; border: 1px solid #006600;")
                    else:
                        # Out of range
                        btn.setStyleSheet("background: #eee; border: 1px solid #ccc;")
    
    def clear_pattern(self):
        """Clear the range pattern"""
        self.pattern = [[False for _ in range(8)] for _ in range(8)]
        # Clear preset selection when manually clearing
        self.current_range_preset_type = None
        self.update_all_range_preset_button_styles()
        self.update_visual()

    def update_range_preset_button_style(self, btn, preset_type):
        """Update the style of a single range preset button based on selection state"""
        if self.current_range_preset_type == preset_type:
            # Highlighted style for the current preset
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 3px solid #66aaff;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #5599dd, stop:1 #4477bb);
                    border-color: #77bbff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #2255aa, stop:1 #1144aa);
                    border-color: #4488cc;
                }
            """)
        else:
            # Normal style
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)

    def update_all_range_preset_button_styles(self):
        """Update all range preset button styles based on current selection"""
        for btn, preset_type in self.range_preset_buttons:
            self.update_range_preset_button_style(btn, preset_type)

    def select_range_preset(self, preset_type):
        """Select a range preset and update button highlighting"""
        # Update current selection
        self.current_range_preset_type = preset_type

        # Update all button styles
        self.update_all_range_preset_button_styles()

        # Apply the preset pattern
        self.apply_range_preset(preset_type)
    
    def set_radius_pattern(self, radius):
        """Set a circular radius pattern"""
        self.pattern = [[False for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        for r in range(8):
            for c in range(8):
                # Calculate distance from piece
                dr = abs(r - piece_r)
                dc = abs(c - piece_c)
                distance = max(dr, dc)  # Chebyshev distance (king's move distance)

                if distance <= radius and [r, c] != self.piece_pos:
                    self.pattern[r][c] = True

        self.update_visual()

    def apply_range_preset(self, preset_type):
        """Apply a preset range pattern based on chess piece movement with auto continue off board"""
        # Clear current pattern
        self.pattern = [[False for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        # Auto-set continue off board based on preset type
        # King and Knight should NOT continue off board, all others should
        should_continue_off_board = preset_type not in ['king', 'knight']
        if hasattr(self, 'continue_off_board_check'):
            self.continue_off_board_check.setChecked(should_continue_off_board)
        if hasattr(self, 'continue_off_board'):
            self.continue_off_board = should_continue_off_board

        if preset_type == "rook":
            # Rook: All squares in orthogonal lines
            # Horizontal line
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = True
            # Vertical line
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = True

        elif preset_type == "bishop":
            # Bishop: All squares in diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        # Check if on diagonal
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = True

        elif preset_type == "queen":
            # Queen: Combination of rook and bishop
            # Orthogonal lines (rook)
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = True
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = True
            # Diagonal lines (bishop)
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = True

        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2),
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = True

        elif preset_type == "king":
            # King: 8 adjacent squares
            directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1),
                         (0, 1), (1, -1), (1, 0), (1, 1)]
            for dr, dc in directions:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = True

        elif preset_type == "global":
            # Global: Entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.piece_pos:
                        self.pattern[r][c] = True

        self.update_visual()
    
    def get_range_pattern(self):
        """Get the range pattern"""
        return [row[:] for row in self.pattern]  # Return deep copy

    def get_piece_position(self):
        """Get the piece position"""
        return self.piece_pos[:]  # Return copy

    def get_checkbox_states(self):
        """Get the current checkbox states"""
        return {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }
    
    def set_range_pattern(self, pattern):
        """Set the range pattern"""
        if pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            # Convert to boolean pattern
            self.pattern = []
            for row in pattern:
                new_row = []
                for cell in row:
                    if isinstance(cell, bool):
                        new_row.append(cell)
                    elif isinstance(cell, int):
                        new_row.append(cell > 0)
                    else:
                        new_row.append(bool(cell))
                self.pattern.append(new_row)
            self.update_visual()


# Convenience function
def edit_target_range(initial_pattern=None, piece_position=None, title="Edit Target Range", parent=None, checkbox_states=None):
    """
    Edit a target range pattern with checkbox state persistence
    Returns: (pattern, piece_position, checkbox_states) or (None, None, None) if cancelled
    """
    dialog = TargetRangeDialog(initial_pattern, piece_position, title, parent, checkbox_states)

    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_range_pattern(), dialog.get_piece_position(), dialog.get_checkbox_states()
    return None, None, None

# Legacy compatibility function
def edit_range_pattern(initial_pattern=None, piece_position=None, title="Edit Range Pattern", parent=None, checkbox_states=None):
    """Legacy compatibility function - redirects to edit_target_range"""
    return edit_target_range(initial_pattern, piece_position, title, parent, checkbox_states)