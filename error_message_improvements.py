"""
Error Message Improvements Integration for Adventure Chess Creator

This module integrates the user-friendly error system with existing components,
replacing technical error messages with clear, actionable guidance throughout
the application.

Key improvements:
- Replaces QMessageBox.critical() calls with user-friendly dialogs
- Adds contextual help for common error scenarios
- Provides quick fix actions for common problems
- Maintains backward compatibility with existing error handling

Usage:
    from error_message_improvements import improved_error_handler
    
    # Replace this:
    QMessageBox.critical(self, "Error", f"Failed to load: {str(e)}")
    
    # With this:
    improved_error_handler.show_error(e, "loading file", file_path, self)
"""

import logging
import os
import sys
from typing import Optional, Dict, Any, Callable
from pathlib import Path
from PyQt6.QtWidgets import QMessageBox, QFileDialog, QWidget
from PyQt6.QtCore import QObject, pyqtSignal

from user_friendly_error_system import (
    show_user_friendly_error, 
    show_contextual_help,
    error_dialog_manager,
    ErrorCategory,
    ErrorSeverity
)

logger = logging.getLogger(__name__)

class ImprovedErrorHandler(QObject):
    """Enhanced error handler that integrates with the application"""
    
    # Signals for communicating with the main application
    file_operation_requested = pyqtSignal(str, str)  # operation, file_path
    help_requested = pyqtSignal(str)  # help_topic
    
    def __init__(self):
        super().__init__()
        self.setup_quick_fix_handlers()
        self.last_operation = None
        self.last_file_path = None
        self.current_parent = None
    
    def setup_quick_fix_handlers(self):
        """Setup quick fix handlers for common actions"""
        error_dialog_manager.register_quick_fix_handler("save_as_dialog", self.show_save_as_dialog)
        error_dialog_manager.register_quick_fix_handler("browse_for_file", self.show_browse_dialog)
        error_dialog_manager.register_quick_fix_handler("create_new_file", self.create_new_file)
        error_dialog_manager.register_quick_fix_handler("retry_operation", self.retry_operation)
        error_dialog_manager.register_quick_fix_handler("reset_to_defaults", self.reset_to_defaults)
        error_dialog_manager.register_quick_fix_handler("open_in_text_editor", self.open_in_text_editor)
        error_dialog_manager.register_quick_fix_handler("show_field_help", self.show_field_help)
        error_dialog_manager.register_quick_fix_handler("open_file_properties", self.open_file_properties)
        error_dialog_manager.register_quick_fix_handler("restore_backup", self.restore_backup)
    
    def show_error(self, error: Exception, operation: str = "", file_path: str = "", parent: Optional[QWidget] = None) -> bool:
        """Show improved error dialog with user-friendly messages"""
        try:
            # Store context for quick fixes
            self.last_operation = operation
            self.last_file_path = file_path
            self.current_parent = parent
            
            # Show user-friendly error dialog
            return show_user_friendly_error(error, operation, file_path, parent)
            
        except Exception as e:
            logger.error(f"Error showing improved error dialog: {e}")
            # Fallback to standard error dialog
            QMessageBox.critical(parent, "Error", f"An error occurred: {str(error)}")
            return False
    
    def show_warning(self, message: str, title: str = "Warning", parent: Optional[QWidget] = None) -> bool:
        """Show improved warning dialog"""
        try:
            # Create a mock exception for the warning
            warning_exception = Exception(message)
            return self.show_error(warning_exception, "warning", "", parent)
        except Exception as e:
            logger.error(f"Error showing warning dialog: {e}")
            QMessageBox.warning(parent, title, message)
            return False
    
    def show_info(self, message: str, title: str = "Information", parent: Optional[QWidget] = None):
        """Show improved information dialog"""
        try:
            # For info messages, we can use the standard dialog as they're usually positive
            QMessageBox.information(parent, title, message)
        except Exception as e:
            logger.error(f"Error showing info dialog: {e}")
    
    # Quick fix handlers
    def show_save_as_dialog(self):
        """Show save as dialog for file operations"""
        try:
            if self.current_parent and hasattr(self.current_parent, 'save_as'):
                self.current_parent.save_as()
            else:
                # Generic save as dialog
                file_path, _ = QFileDialog.getSaveFileName(
                    self.current_parent,
                    "Save As",
                    "",
                    "JSON Files (*.json);;All Files (*)"
                )
                if file_path:
                    self.file_operation_requested.emit("save_as", file_path)
        except Exception as e:
            logger.error(f"Error in save as dialog: {e}")
    
    def show_browse_dialog(self):
        """Show file browse dialog"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self.current_parent,
                "Select File",
                self.last_file_path or "",
                "JSON Files (*.json);;All Files (*)"
            )
            if file_path:
                self.file_operation_requested.emit("load_file", file_path)
        except Exception as e:
            logger.error(f"Error in browse dialog: {e}")
    
    def create_new_file(self):
        """Create new file"""
        try:
            if self.current_parent and hasattr(self.current_parent, 'new_data'):
                self.current_parent.new_data()
            elif self.current_parent and hasattr(self.current_parent, 'reset_form'):
                self.current_parent.reset_form()
            else:
                self.file_operation_requested.emit("new_file", "")
        except Exception as e:
            logger.error(f"Error creating new file: {e}")
    
    def retry_operation(self):
        """Retry the last operation"""
        try:
            if self.last_operation and self.current_parent:
                # Try to find and call the appropriate method
                if hasattr(self.current_parent, 'retry_last_operation'):
                    self.current_parent.retry_last_operation()
                elif "load" in self.last_operation.lower() and hasattr(self.current_parent, 'load_data'):
                    if self.last_file_path:
                        filename = os.path.basename(self.last_file_path).replace('.json', '')
                        self.current_parent.load_data(filename)
                elif "sav" in self.last_operation.lower() and hasattr(self.current_parent, 'save_data'):
                    self.current_parent.save_data()
        except Exception as e:
            logger.error(f"Error retrying operation: {e}")
    
    def reset_to_defaults(self):
        """Reset current form to defaults"""
        try:
            if self.current_parent and hasattr(self.current_parent, 'reset_form'):
                self.current_parent.reset_form()
            elif self.current_parent and hasattr(self.current_parent, 'new_data'):
                self.current_parent.new_data()
        except Exception as e:
            logger.error(f"Error resetting to defaults: {e}")
    
    def open_in_text_editor(self):
        """Open file in system text editor"""
        try:
            if self.last_file_path and os.path.exists(self.last_file_path):
                if sys.platform.startswith('win'):
                    os.startfile(self.last_file_path)
                elif sys.platform.startswith('darwin'):
                    os.system(f'open "{self.last_file_path}"')
                else:
                    os.system(f'xdg-open "{self.last_file_path}"')
        except Exception as e:
            logger.error(f"Error opening file in text editor: {e}")
    
    def show_field_help(self):
        """Show help for field requirements"""
        try:
            show_contextual_help("validation_errors", self.current_parent)
        except Exception as e:
            logger.error(f"Error showing field help: {e}")
    
    def open_file_properties(self):
        """Open file properties dialog"""
        try:
            if self.last_file_path and os.path.exists(self.last_file_path):
                if sys.platform.startswith('win'):
                    os.system(f'explorer /select,"{self.last_file_path}"')
                else:
                    # For non-Windows systems, just open the containing folder
                    folder_path = os.path.dirname(self.last_file_path)
                    if sys.platform.startswith('darwin'):
                        os.system(f'open "{folder_path}"')
                    else:
                        os.system(f'xdg-open "{folder_path}"')
        except Exception as e:
            logger.error(f"Error opening file properties: {e}")
    
    def restore_backup(self):
        """Restore from backup file"""
        try:
            if self.last_file_path:
                backup_path = self.last_file_path + ".backup"
                if os.path.exists(backup_path):
                    # Show dialog to confirm restore
                    reply = QMessageBox.question(
                        self.current_parent,
                        "Restore Backup",
                        f"Restore from backup file?\n\nThis will replace the current file with the backup version.",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    
                    if reply == QMessageBox.StandardButton.Yes:
                        import shutil
                        shutil.copy2(backup_path, self.last_file_path)
                        QMessageBox.information(
                            self.current_parent,
                            "Backup Restored",
                            "The backup file has been restored successfully."
                        )
                        # Try to reload the file
                        self.retry_operation()
                else:
                    QMessageBox.information(
                        self.current_parent,
                        "No Backup Found",
                        "No backup file was found for this file."
                    )
        except Exception as e:
            logger.error(f"Error restoring backup: {e}")

# Global instance
improved_error_handler = ImprovedErrorHandler()

# Convenience functions for easy migration
def show_improved_error(error: Exception, operation: str = "", file_path: str = "", parent: Optional[QWidget] = None) -> bool:
    """Show improved error dialog - convenience function"""
    return improved_error_handler.show_error(error, operation, file_path, parent)

def show_improved_warning(message: str, title: str = "Warning", parent: Optional[QWidget] = None) -> bool:
    """Show improved warning dialog - convenience function"""
    return improved_error_handler.show_warning(message, title, parent)

def show_improved_info(message: str, title: str = "Information", parent: Optional[QWidget] = None):
    """Show improved info dialog - convenience function"""
    improved_error_handler.show_info(message, title, parent)

# Monkey patch for easy migration (optional)
def patch_qmessagebox():
    """
    Monkey patch QMessageBox to use improved dialogs
    WARNING: This is experimental and should be used carefully
    """
    original_critical = QMessageBox.critical
    original_warning = QMessageBox.warning
    
    def improved_critical(parent, title, text, buttons=QMessageBox.StandardButton.Ok, defaultButton=QMessageBox.StandardButton.NoButton):
        try:
            # Try to extract meaningful error info from the text
            error = Exception(text)
            return show_improved_error(error, "operation", "", parent)
        except:
            return original_critical(parent, title, text, buttons, defaultButton)
    
    def improved_warning(parent, title, text, buttons=QMessageBox.StandardButton.Ok, defaultButton=QMessageBox.StandardButton.NoButton):
        try:
            return show_improved_warning(text, title, parent)
        except:
            return original_warning(parent, title, text, buttons, defaultButton)
    
    QMessageBox.critical = improved_critical
    QMessageBox.warning = improved_warning

# Error message templates for common scenarios
ERROR_TEMPLATES = {
    "file_not_found": "The file '{file_path}' could not be found. It may have been moved, renamed, or deleted.",
    "permission_denied": "Cannot access '{file_path}'. Please check file permissions or try a different location.",
    "invalid_json": "The file '{file_path}' appears to be corrupted or in an invalid format.",
    "validation_failed": "The data entered doesn't meet the required format. Please check the highlighted fields.",
    "save_failed": "Could not save the file. Please check permissions and try again.",
    "load_failed": "Could not load the file. The file may be corrupted or in an unsupported format."
}

def get_error_template(error_type: str, **kwargs) -> str:
    """Get formatted error message template"""
    template = ERROR_TEMPLATES.get(error_type, "An error occurred: {error}")
    try:
        return template.format(**kwargs)
    except KeyError:
        # Handle missing template arguments gracefully
        return template.replace("{file_path}", kwargs.get("file_path", "[file]")).replace("{error}", str(kwargs.get("error", "unknown error")))
