#!/usr/bin/env python3
"""
Comprehensive tests for security enhancements in Adventure Chess Creator

Tests all security features including:
- File path validation and directory traversal prevention
- Input sanitization and validation
- Crash recovery mechanisms
- Enhanced Pydantic validation rules
- Secure file operations
"""

import pytest
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import patch, Mock

from security_enhancements import SecurityValidator, CrashRecoveryManager, SecureDataManager
from enhanced_validation_rules import ValidationRules
from security_integration import SecureDirectDataManager, SecurePydanticDataManager


class TestSecurityValidator:
    """Test security validation functionality"""
    
    def test_validate_safe_file_path(self):
        """Test validation of safe file paths"""
        with tempfile.TemporaryDirectory() as temp_dir:
            safe_path = Path(temp_dir) / "test_file.json"
            is_valid, error = SecurityValidator.validate_file_path(safe_path, temp_dir)
            assert is_valid, f"Safe path should be valid: {error}"
    
    def test_prevent_directory_traversal(self):
        """Test prevention of directory traversal attacks"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test various directory traversal attempts
            dangerous_paths = [
                "../../../etc/passwd",
                "..\\..\\windows\\system32",
                temp_dir + "/../../../sensitive_file.txt",
                Path(temp_dir).parent / "outside_file.json"
            ]
            
            for dangerous_path in dangerous_paths:
                is_valid, error = SecurityValidator.validate_file_path(dangerous_path, temp_dir)
                assert not is_valid, f"Dangerous path should be invalid: {dangerous_path}"
                assert "outside allowed directory" in error or "Dangerous pattern" in error
    
    def test_filename_sanitization(self):
        """Test filename sanitization"""
        test_cases = [
            ("normal_file.json", "normal_file.json"),
            ("file with spaces.json", "file with spaces.json"),
            ("file<>:\"/\\|?*.json", "file_________.json"),
            ("", "unnamed_file"),
            ("   .   ", "unnamed_file"),
            ("very_long_filename_" + "x" * 100 + ".json", "very_long_filename_" + "x" * 77 + ".json"),
        ]
        
        for input_name, expected in test_cases:
            result = SecurityValidator.sanitize_filename(input_name)
            assert len(result) <= 100, f"Sanitized filename too long: {result}"
            assert SecurityValidator.FILENAME_PATTERN.match(result), f"Sanitized filename invalid: {result}"
    
    def test_input_validation_security(self):
        """Test input validation for security threats"""
        dangerous_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "eval(malicious_code)",
            "__import__('os').system('rm -rf /')",
            "subprocess.call(['rm', '-rf', '/'])",
        ]
        
        for dangerous_input in dangerous_inputs:
            is_valid, error = SecurityValidator.validate_user_input(dangerous_input, "test_field")
            assert not is_valid, f"Dangerous input should be invalid: {dangerous_input}"
            assert "dangerous content" in error.lower()
    
    def test_file_size_validation(self):
        """Test file size validation"""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file_name = temp_file.name
            # Write data exceeding the limit
            large_data = "x" * (SecurityValidator.MAX_FILE_SIZE + 1)
            temp_file.write(large_data.encode())
            temp_file.flush()
            temp_file.close()  # Close file before validation

            is_valid, error = SecurityValidator.validate_file_size(temp_file_name)
            assert not is_valid, "Large file should be invalid"
            assert "exceeds maximum allowed" in error

            os.unlink(temp_file_name)


class TestCrashRecoveryManager:
    """Test crash recovery functionality"""
    
    def test_backup_creation(self):
        """Test backup file creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            recovery_manager = CrashRecoveryManager()
            recovery_manager.backup_dir = Path(temp_dir) / "backups"
            recovery_manager.backup_dir.mkdir(parents=True, exist_ok=True)  # Ensure backup dir exists

            # Create test file
            test_file = Path(temp_dir) / "test_file.json"
            test_data = {"name": "test", "value": 123}
            with open(test_file, 'w') as f:
                json.dump(test_data, f)

            # Create backup
            success, error = recovery_manager.create_backup(test_file, "test")
            assert success, f"Backup creation failed: {error}"

            # Verify backup exists
            backup_files = list(recovery_manager.backup_dir.glob("test_file_test_*.json"))
            assert len(backup_files) > 0, "Backup file not created"
    
    def test_recovery_point_creation(self):
        """Test recovery point creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            recovery_manager = CrashRecoveryManager()
            recovery_manager.recovery_dir = Path(temp_dir) / "recovery"
            recovery_manager.recovery_dir.mkdir(parents=True, exist_ok=True)  # Ensure recovery dir exists

            test_data = {"name": "test_piece", "role": "Commander"}

            success, error = recovery_manager.create_recovery_point(test_data, "piece")
            assert success, f"Recovery point creation failed: {error}"

            # Verify recovery file exists
            recovery_files = list(recovery_manager.recovery_dir.glob("piece_recovery_*.json"))
            assert len(recovery_files) > 0, "Recovery file not created"
    
    def test_recovery_restoration(self):
        """Test data restoration from recovery file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            recovery_manager = CrashRecoveryManager()
            recovery_manager.recovery_dir = Path(temp_dir) / "recovery"
            recovery_manager.recovery_dir.mkdir(exist_ok=True)
            
            # Create recovery file
            test_data = {"name": "test_piece", "role": "Commander"}
            recovery_file = recovery_manager.recovery_dir / "piece_recovery_20240101_120000.json"
            
            recovery_content = {
                "timestamp": "20240101_120000",
                "data_type": "piece",
                "data": test_data,
                "version": "1.0.0"
            }
            
            with open(recovery_file, 'w') as f:
                json.dump(recovery_content, f)
            
            # Test restoration
            restored_data, error = recovery_manager.restore_from_recovery(recovery_file)
            assert restored_data is not None, f"Recovery restoration failed: {error}"
            assert restored_data == test_data, "Restored data doesn't match original"


class TestValidationRules:
    """Test enhanced validation rules"""
    
    def test_string_validation(self):
        """Test string field validation"""
        # Valid strings
        valid_strings = ["normal_string", "String with spaces", "123"]
        for valid_str in valid_strings:
            result = ValidationRules.validate_string_field(valid_str, "test_field")
            assert isinstance(result, str)
        
        # Invalid strings
        with pytest.raises(ValueError):
            ValidationRules.validate_string_field("", "test_field", allow_empty=False)
        
        with pytest.raises(ValueError):
            ValidationRules.validate_string_field("<script>alert('xss')</script>", "test_field")
        
        with pytest.raises(ValueError):
            ValidationRules.validate_string_field("x" * 1001, "test_field")  # Too long
    
    def test_numeric_validation(self):
        """Test numeric field validation"""
        # Valid numbers
        assert ValidationRules.validate_numeric_field(42, "test_field") == 42
        assert ValidationRules.validate_numeric_field(3.14, "test_field") == 3.14
        assert ValidationRules.validate_numeric_field("123", "test_field") == 123
        
        # Invalid numbers
        with pytest.raises(ValueError):
            ValidationRules.validate_numeric_field(float('nan'), "test_field")
        
        with pytest.raises(ValueError):
            ValidationRules.validate_numeric_field(float('inf'), "test_field")
        
        with pytest.raises(ValueError):
            ValidationRules.validate_numeric_field("not_a_number", "test_field")
    
    def test_coordinate_validation(self):
        """Test coordinate validation"""
        # Valid coordinates
        valid_coords = [[0, 0], [3, 4], [7, 7]]
        for coord in valid_coords:
            result = ValidationRules.validate_coordinate(coord)
            assert result == coord
        
        # Invalid coordinates
        invalid_coords = [
            [8, 0],      # Out of range
            [-1, 5],     # Negative
            [3],         # Wrong length
            [3, 4, 5],   # Wrong length
            ["a", "b"],  # Non-numeric
        ]
        
        for coord in invalid_coords:
            with pytest.raises(ValueError):
                ValidationRules.validate_coordinate(coord)
    
    def test_pattern_validation(self):
        """Test 8x8 pattern validation"""
        # Valid pattern
        valid_pattern = [[0 if i != j else 1 for j in range(8)] for i in range(8)]
        result = ValidationRules.validate_pattern_8x8(valid_pattern)
        assert result == valid_pattern
        
        # Invalid patterns
        with pytest.raises(ValueError):
            ValidationRules.validate_pattern_8x8([[1, 0]])  # Wrong size
        
        with pytest.raises(ValueError):
            ValidationRules.validate_pattern_8x8([[2] * 8] * 8)  # Invalid values


class TestSecureDataManager:
    """Test secure data manager functionality"""
    
    def test_secure_save_and_load(self):
        """Test secure save and load operations"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a secure manager with the temp directory as allowed base
            secure_manager = SecureDataManager()

            test_data = {"name": "test_piece", "role": "Commander", "version": "1.0.0"}
            test_file = Path(temp_dir) / "test_piece.json"

            # Test secure save with explicit base directory
            # Override the security validator to use temp_dir as base
            original_validate = SecurityValidator.validate_file_path
            def mock_validate(file_path, base_dir=None):
                return original_validate(file_path, temp_dir)

            with patch.object(SecurityValidator, 'validate_file_path', side_effect=mock_validate):
                success, error = secure_manager.secure_save_file(test_data, test_file)
                assert success, f"Secure save failed: {error}"
                assert test_file.exists(), "File was not created"

                # Test secure load
                loaded_data, error = secure_manager.secure_load_file(test_file)
                assert loaded_data is not None, f"Secure load failed: {error}"
                assert loaded_data == test_data, "Loaded data doesn't match saved data"
    
    def test_secure_save_with_dangerous_data(self):
        """Test secure save with potentially dangerous data"""
        with tempfile.TemporaryDirectory() as temp_dir:
            secure_manager = SecureDataManager()

            dangerous_data = {
                "name": "<script>alert('xss')</script>",
                "description": "eval(malicious_code)"
            }
            test_file = Path(temp_dir) / "dangerous.json"

            # Should fail validation
            # Override the security validator to use temp_dir as base but still catch dangerous data
            original_validate = SecurityValidator.validate_file_path
            def mock_validate(file_path, base_dir=None):
                return original_validate(file_path, temp_dir)

            with patch.object(SecurityValidator, 'validate_file_path', side_effect=mock_validate):
                success, error = secure_manager.secure_save_file(dangerous_data, test_file)
                assert not success, "Dangerous data should not be saved"
                assert ("validation failed" in error.lower() or "dangerous content" in error.lower())


class TestSecureIntegration:
    """Test security integration with existing managers"""
    
    def test_secure_direct_data_manager(self):
        """Test secure wrapper for DirectDataManager"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Override the security validator to use temp_dir as base
            original_validate = SecurityValidator.validate_file_path
            def mock_validate(file_path, base_dir=None):
                return original_validate(file_path, temp_dir)

            with patch('config.PIECES_DIR', temp_dir), \
                 patch.object(SecurityValidator, 'validate_file_path', side_effect=mock_validate):
                test_data = {"name": "test_piece", "role": "Commander", "version": "1.0.0"}

                # Test secure save
                success, error = SecureDirectDataManager.save_piece(test_data, "test_piece")
                assert success, f"Secure piece save failed: {error}"

                # Test secure load
                loaded_data, error = SecureDirectDataManager.load_piece("test_piece")
                assert loaded_data is not None, f"Secure piece load failed: {error}"
                assert loaded_data["name"] == test_data["name"]
    
    def test_secure_direct_data_manager_with_invalid_data(self):
        """Test secure DirectDataManager with invalid data"""
        dangerous_data = {
            "name": "<script>alert('xss')</script>",
            "role": "Commander"
        }
        
        success, error = SecureDirectDataManager.save_piece(dangerous_data, "dangerous_piece")
        assert not success, "Dangerous piece data should not be saved"
        assert "validation failed" in error.lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
