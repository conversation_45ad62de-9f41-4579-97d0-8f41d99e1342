#!/usr/bin/env python3
"""
Enhanced Error Handling System for Adventure Chess Creator
Provides comprehensive error handling, recovery mechanisms, and user feedback
"""

import logging
import traceback
import json
import os
from typing import Dict, Any, List, Optional, Tuple, Callable, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

# Setup enhanced logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for better classification"""
    FILE_OPERATION = "file_operation"
    DATA_VALIDATION = "data_validation"
    UI_OPERATION = "ui_operation"
    NETWORK = "network"
    SYSTEM = "system"
    USER_INPUT = "user_input"

@dataclass
class ErrorContext:
    """Enhanced error context with recovery information"""
    severity: ErrorSeverity
    category: ErrorCategory
    message: str
    details: Optional[str] = None
    file_path: Optional[str] = None
    operation: Optional[str] = None
    timestamp: Optional[datetime] = None
    stack_trace: Optional[str] = None
    recovery_suggestions: Optional[List[str]] = None
    user_message: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class EnhancedErrorHandler:
    """
    Enhanced error handling system with recovery mechanisms and user feedback
    """
    
    def __init__(self):
        self.error_log: List[ErrorContext] = []
        self.recovery_strategies: Dict[str, Callable] = {}
        self.user_feedback_callback: Optional[Callable] = None
        
        # Register default recovery strategies
        self._register_default_recovery_strategies()
    
    def set_user_feedback_callback(self, callback: Callable[[ErrorContext], None]):
        """Set callback for user feedback (e.g., showing error dialogs)"""
        self.user_feedback_callback = callback
    
    def handle_error(self, 
                    error: Exception, 
                    severity: ErrorSeverity = ErrorSeverity.ERROR,
                    category: ErrorCategory = ErrorCategory.SYSTEM,
                    operation: Optional[str] = None,
                    file_path: Optional[str] = None,
                    recovery_suggestions: Optional[List[str]] = None,
                    user_message: Optional[str] = None) -> ErrorContext:
        """
        Handle an error with enhanced context and recovery options
        
        Args:
            error: The exception that occurred
            severity: Error severity level
            category: Error category
            operation: Operation being performed when error occurred
            file_path: File path if relevant
            recovery_suggestions: List of recovery suggestions
            user_message: User-friendly error message
            
        Returns:
            ErrorContext object with full error information
        """
        
        # Create error context
        error_context = ErrorContext(
            severity=severity,
            category=category,
            message=str(error),
            details=f"{type(error).__name__}: {str(error)}",
            file_path=file_path,
            operation=operation,
            stack_trace=traceback.format_exc(),
            recovery_suggestions=recovery_suggestions or self._get_default_recovery_suggestions(category),
            user_message=user_message or self._generate_user_friendly_message(error, category)
        )
        
        # Log the error
        self._log_error(error_context)
        
        # Store in error log
        self.error_log.append(error_context)
        
        # Attempt automatic recovery if available
        self._attempt_recovery(error_context)
        
        # Notify user if callback is set
        if self.user_feedback_callback:
            self.user_feedback_callback(error_context)
        
        return error_context
    
    def handle_file_operation_error(self, 
                                  error: Exception, 
                                  operation: str, 
                                  file_path: str,
                                  severity: ErrorSeverity = ErrorSeverity.ERROR) -> ErrorContext:
        """Handle file operation specific errors with enhanced context"""
        
        recovery_suggestions = []
        
        if "Permission denied" in str(error):
            recovery_suggestions = [
                "Check file permissions",
                "Close any applications that might be using the file",
                "Run the application as administrator",
                "Try saving to a different location"
            ]
        elif "No such file or directory" in str(error):
            recovery_suggestions = [
                "Check if the file path exists",
                "Verify the file hasn't been moved or deleted",
                "Try browsing for the file manually",
                "Create the directory if it doesn't exist"
            ]
        elif "Invalid JSON" in str(error) or "JSONDecodeError" in str(error):
            recovery_suggestions = [
                "Check file format - ensure it's valid JSON",
                "Try opening the file in a text editor to check for corruption",
                "Restore from backup if available",
                "Create a new file with default values"
            ]
        
        return self.handle_error(
            error=error,
            severity=severity,
            category=ErrorCategory.FILE_OPERATION,
            operation=operation,
            file_path=file_path,
            recovery_suggestions=recovery_suggestions
        )
    
    def handle_validation_error(self, 
                              error: Exception, 
                              data: Dict[str, Any],
                              operation: str = "data_validation") -> ErrorContext:
        """Handle data validation errors with specific recovery suggestions"""
        
        recovery_suggestions = [
            "Check required fields are filled",
            "Verify data types match expected formats",
            "Review field constraints and limits",
            "Reset to default values if needed"
        ]
        
        # Add specific suggestions based on error type
        if "validation error" in str(error).lower():
            recovery_suggestions.append("Check the validation error details for specific field issues")
        
        return self.handle_error(
            error=error,
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.DATA_VALIDATION,
            operation=operation,
            recovery_suggestions=recovery_suggestions
        )
    
    def safe_file_operation(self, 
                          operation: Callable, 
                          operation_name: str,
                          file_path: Optional[str] = None,
                          fallback_result: Any = None) -> Tuple[Any, Optional[ErrorContext]]:
        """
        Safely execute a file operation with error handling
        
        Args:
            operation: Function to execute
            operation_name: Name of the operation for logging
            file_path: File path if relevant
            fallback_result: Result to return if operation fails
            
        Returns:
            Tuple of (result, error_context)
        """
        try:
            result = operation()
            return result, None
        except Exception as e:
            error_context = self.handle_file_operation_error(e, operation_name, file_path or "unknown")
            return fallback_result, error_context
    
    def safe_json_load(self, file_path: str) -> Tuple[Optional[Dict[str, Any]], Optional[ErrorContext]]:
        """Safely load JSON file with enhanced error handling"""
        
        def load_operation():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return self.safe_file_operation(load_operation, "json_load", file_path, None)
    
    def safe_json_save(self, data: Dict[str, Any], file_path: str) -> Tuple[bool, Optional[ErrorContext]]:
        """Safely save JSON file with enhanced error handling"""
        
        def save_operation():
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Create backup if file exists
            if os.path.exists(file_path):
                backup_path = f"{file_path}.backup"
                import shutil
                shutil.copy2(file_path, backup_path)
            
            # Save file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
        
        return self.safe_file_operation(save_operation, "json_save", file_path, False)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors encountered"""
        summary = {
            "total_errors": len(self.error_log),
            "by_severity": {},
            "by_category": {},
            "recent_errors": []
        }
        
        # Count by severity
        for severity in ErrorSeverity:
            count = sum(1 for error in self.error_log if error.severity == severity)
            summary["by_severity"][severity.value] = count
        
        # Count by category
        for category in ErrorCategory:
            count = sum(1 for error in self.error_log if error.category == category)
            summary["by_category"][category.value] = count
        
        # Recent errors (last 10)
        recent = sorted(self.error_log, key=lambda x: x.timestamp, reverse=True)[:10]
        summary["recent_errors"] = [
            {
                "timestamp": error.timestamp.isoformat(),
                "severity": error.severity.value,
                "category": error.category.value,
                "message": error.message,
                "operation": error.operation
            }
            for error in recent
        ]
        
        return summary
    
    def _register_default_recovery_strategies(self):
        """Register default recovery strategies"""
        
        def create_backup_recovery(file_path: str):
            """Create backup file recovery strategy"""
            backup_path = f"{file_path}.backup"
            if os.path.exists(backup_path):
                import shutil
                shutil.copy2(backup_path, file_path)
                return True
            return False
        
        def create_default_file_recovery(file_path: str, default_data: Dict[str, Any]):
            """Create default file recovery strategy"""
            try:
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_data, f, indent=2)
                return True
            except Exception:
                return False
        
        self.recovery_strategies["backup_recovery"] = create_backup_recovery
        self.recovery_strategies["default_file_recovery"] = create_default_file_recovery
    
    def _get_default_recovery_suggestions(self, category: ErrorCategory) -> List[str]:
        """Get default recovery suggestions based on error category"""
        suggestions = {
            ErrorCategory.FILE_OPERATION: [
                "Check file permissions and access rights",
                "Verify file path exists and is accessible",
                "Try closing other applications that might be using the file"
            ],
            ErrorCategory.DATA_VALIDATION: [
                "Check all required fields are filled correctly",
                "Verify data types and formats",
                "Reset to default values if needed"
            ],
            ErrorCategory.UI_OPERATION: [
                "Try refreshing the interface",
                "Check if all required components are loaded",
                "Restart the application if issues persist"
            ],
            ErrorCategory.USER_INPUT: [
                "Check input format and requirements",
                "Verify all required fields are completed",
                "Review input constraints and limits"
            ]
        }
        
        return suggestions.get(category, ["Contact support if the issue persists"])
    
    def _generate_user_friendly_message(self, error: Exception, category: ErrorCategory) -> str:
        """Generate user-friendly error message"""
        error_str = str(error).lower()
        
        if category == ErrorCategory.FILE_OPERATION:
            if "permission" in error_str:
                return "Unable to access the file. Please check file permissions."
            elif "not found" in error_str:
                return "The requested file could not be found."
            elif "json" in error_str:
                return "The file appears to be corrupted or in an invalid format."
            else:
                return "An error occurred while working with the file."
        
        elif category == ErrorCategory.DATA_VALIDATION:
            return "The data entered doesn't meet the required format. Please check your input."
        
        elif category == ErrorCategory.UI_OPERATION:
            return "An interface error occurred. Please try the operation again."
        
        else:
            return f"An unexpected error occurred: {str(error)}"
    
    def _log_error(self, error_context: ErrorContext):
        """Log error with appropriate level"""
        log_message = f"[{error_context.category.value}] {error_context.operation or 'Unknown'}: {error_context.message}"
        
        if error_context.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_context.severity == ErrorSeverity.ERROR:
            logger.error(log_message)
        elif error_context.severity == ErrorSeverity.WARNING:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # Log stack trace for errors and critical issues
        if error_context.severity in [ErrorSeverity.ERROR, ErrorSeverity.CRITICAL] and error_context.stack_trace:
            logger.debug(f"Stack trace: {error_context.stack_trace}")
    
    def _attempt_recovery(self, error_context: ErrorContext):
        """Attempt automatic recovery based on error context"""
        try:
            # Attempt file backup recovery if available
            if error_context.error_type == "file_corruption" and hasattr(self, 'backup_manager'):
                return self.backup_manager.restore_latest_backup(error_context.file_path)

            # Attempt data validation recovery
            if error_context.error_type == "validation_error" and hasattr(self, 'data_validator'):
                return self.data_validator.attempt_repair(error_context.data)

            return False
        except Exception as e:
            logger.error(f"Recovery attempt failed: {e}")
            return False

# Global error handler instance
error_handler = EnhancedErrorHandler()

# Convenience functions for common error handling patterns
def safe_file_load(file_path: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
    """Safely load a file and return (data, error_message)"""
    data, error_context = error_handler.safe_json_load(file_path)
    error_message = error_context.user_message if error_context else None
    return data, error_message

def safe_file_save(data: Dict[str, Any], file_path: str) -> Tuple[bool, Optional[str]]:
    """Safely save a file and return (success, error_message)"""
    success, error_context = error_handler.safe_json_save(data, file_path)
    error_message = error_context.user_message if error_context else None
    return success, error_message
