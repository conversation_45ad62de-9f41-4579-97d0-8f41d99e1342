"""
Trap Tile tag configuration for the Adventure Chess Creator ability editor.
Creates traps on target tiles with various effects.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox, QLabel, QSpinBox,
    QCheckBox, QPushButton
)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig
from dialogs.range_editor_dialog import edit_target_range
from ui.inline_selection_widgets import InlineAbilitySelector


class TrapTileConfig(BaseTagConfig):
    """Configuration for trapTile tag - tile-based traps with various effects."""

    def __init__(self, editor_instance):
        super().__init__(editor_instance, "trapTile")
        # Initialize teleport range data
        self.teleport_pattern = [[False for _ in range(8)] for _ in range(8)]
        self.teleport_piece_pos = [4, 4]
        self.teleport_checkbox_states = {}
    
    def get_title(self) -> str:
        return "🪤 Trap Tile Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the trap tile configuration UI."""
        try:
            # Main group box
            group = QGroupBox(self.get_title())
            layout = QVBoxLayout()
            
            # Description
            description = QLabel("Create traps on target tiles with various effects.")
            description.setWordWrap(True)
            layout.addWidget(description)
            
            # Trap effects group
            effects_group = QGroupBox("Trap Effects")
            effects_layout = QVBoxLayout()
            
            # Capture effect
            capture_check = QCheckBox("Capture (Destroys target)")
            capture_check.setToolTip("Trap destroys any piece that steps on it")
            self.store_widget("trap_capture_check", capture_check)
            effects_layout.addWidget(capture_check)
            
            # Immobilize effect
            immobilize_layout = QHBoxLayout()
            immobilize_check = QCheckBox("Immobilize for")
            immobilize_check.setToolTip("Trap immobilizes pieces that step on it")
            self.store_widget("trap_immobilize_check", immobilize_check)
            immobilize_layout.addWidget(immobilize_check)
            
            immobilize_spin = QSpinBox()
            immobilize_spin.setRange(0, 10)
            immobilize_spin.setValue(2)
            immobilize_spin.setSuffix(" turns")
            immobilize_spin.setSpecialValueText("Indefinite")
            immobilize_spin.setToolTip("Duration of immobilization (0 = indefinite)")
            immobilize_spin.setEnabled(False)
            self.store_widget("trap_immobilize_spin", immobilize_spin)
            immobilize_layout.addWidget(immobilize_spin)
            
            # Connect checkbox to enable/disable spinbox
            immobilize_check.toggled.connect(immobilize_spin.setEnabled)
            
            immobilize_layout.addStretch()
            effects_layout.addLayout(immobilize_layout)
            
            # Teleport effect
            teleport_layout = QHBoxLayout()
            teleport_check = QCheckBox("Teleport")
            teleport_check.setToolTip("Trap teleports pieces to a different location")
            self.store_widget("trap_teleport_check", teleport_check)
            teleport_layout.addWidget(teleport_check)

            teleport_btn = QPushButton("Edit Teleport Range")
            teleport_btn.setToolTip("Configure where pieces are teleported to")
            teleport_btn.setEnabled(False)
            teleport_btn.clicked.connect(self.edit_teleport_range)
            self.store_widget("trap_teleport_btn", teleport_btn)
            teleport_layout.addWidget(teleport_btn)

            # Connect checkbox to enable/disable button
            teleport_check.toggled.connect(teleport_btn.setEnabled)

            teleport_layout.addStretch()
            effects_layout.addLayout(teleport_layout)
            
            # Add embedded ability effect
            ability_layout = QVBoxLayout()
            add_ability_check = QCheckBox("Add Embedded Effect")
            add_ability_check.setToolTip("Trap triggers an additional ability effect")
            self.store_widget("trap_add_ability_check", add_ability_check)
            ability_layout.addWidget(add_ability_check)

            # Inline ability selector
            ability_selector = InlineAbilitySelector(title="Embedded Ability")
            ability_selector.setVisible(False)
            self.store_widget("trap_ability_selector", ability_selector)
            ability_layout.addWidget(ability_selector)

            # Connect checkbox to show/hide selector
            add_ability_check.toggled.connect(ability_selector.setVisible)

            effects_layout.addLayout(ability_layout)
            
            effects_group.setLayout(effects_layout)
            layout.addWidget(effects_group)
            
            # Trap properties group
            properties_group = QGroupBox("Trap Properties")
            properties_layout = QFormLayout()
            
            # Trap duration
            duration_spin = QSpinBox()
            duration_spin.setRange(0, 20)
            duration_spin.setValue(0)
            duration_spin.setSuffix(" turns")
            duration_spin.setSpecialValueText("Permanent")
            duration_spin.setToolTip("How long the trap remains active (0 = permanent)")
            self.store_widget("trap_duration_spin", duration_spin)
            properties_layout.addRow("Trap Duration:", duration_spin)
            
            # Trigger limit
            trigger_limit_spin = QSpinBox()
            trigger_limit_spin.setRange(0, 10)
            trigger_limit_spin.setValue(0)
            trigger_limit_spin.setSpecialValueText("Unlimited")
            trigger_limit_spin.setToolTip("How many times the trap can be triggered (0 = unlimited)")
            self.store_widget("trap_trigger_limit_spin", trigger_limit_spin)
            properties_layout.addRow("Trigger Limit:", trigger_limit_spin)
            
            properties_group.setLayout(properties_layout)
            layout.addWidget(properties_group)
            
            # Visibility options
            visibility_group = QGroupBox("Visibility Options")
            visibility_layout = QVBoxLayout()
            
            # Visible to enemies
            visible_enemies_check = QCheckBox("Visible to Enemies")
            visible_enemies_check.setToolTip("Enemy players can see the trap")
            visible_enemies_check.setChecked(False)
            self.store_widget("trap_visible_enemies_check", visible_enemies_check)
            visibility_layout.addWidget(visible_enemies_check)
            
            # Visible to allies
            visible_allies_check = QCheckBox("Visible to Allies")
            visible_allies_check.setToolTip("Allied players can see the trap")
            visible_allies_check.setChecked(True)
            self.store_widget("trap_visible_allies_check", visible_allies_check)
            visibility_layout.addWidget(visible_allies_check)
            
            visibility_group.setLayout(visibility_layout)
            layout.addWidget(visibility_group)
            
            group.setLayout(layout)
            parent_layout.addWidget(group)
            
            self.log_debug("Trap tile configuration UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating trap tile UI: {e}")

    def edit_teleport_range(self):
        """Open the range editor dialog for teleport range configuration."""
        try:
            self.log_debug("Opening teleport range editor")

            pattern, piece_pos, checkbox_states = edit_target_range(
                initial_pattern=self.teleport_pattern,
                piece_position=self.teleport_piece_pos,
                title="Edit Teleport Range",
                parent=self.editor,
                checkbox_states=self.teleport_checkbox_states
            )

            if pattern is not None:
                self.teleport_pattern = pattern
                self.teleport_piece_pos = piece_pos
                self.teleport_checkbox_states = checkbox_states
                self.log_debug("Teleport range updated successfully")

        except Exception as e:
            self.log_error(f"Error opening teleport range editor: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating trap tile data: {data}")
            
            # Populate trap effects
            trap_effects = data.get("trapEffects", {})
            
            capture_check = self.get_widget_by_name("trap_capture_check")
            if capture_check:
                capture_check.setChecked(trap_effects.get("capture", False))
            
            # Immobilize effect
            immobilize_check = self.get_widget_by_name("trap_immobilize_check")
            immobilize_spin = self.get_widget_by_name("trap_immobilize_spin")
            if immobilize_check and immobilize_spin:
                immobilize_data = trap_effects.get("immobilize", {})
                enabled = immobilize_data.get("enabled", False)
                immobilize_check.setChecked(enabled)
                immobilize_spin.setValue(immobilize_data.get("duration", 2))
                immobilize_spin.setEnabled(enabled)
            
            # Teleport effect
            teleport_check = self.get_widget_by_name("trap_teleport_check")
            teleport_btn = self.get_widget_by_name("trap_teleport_btn")
            if teleport_check and teleport_btn:
                teleport_data = trap_effects.get("teleport", {})
                teleport_enabled = teleport_data.get("enabled", False)
                teleport_check.setChecked(teleport_enabled)
                teleport_btn.setEnabled(teleport_enabled)

                # Load teleport range data
                teleport_range = teleport_data.get("range", {})
                if teleport_range:
                    self.teleport_pattern = teleport_range.get("pattern", self.teleport_pattern)
                    self.teleport_piece_pos = teleport_range.get("piece_position", self.teleport_piece_pos)
                    self.teleport_checkbox_states = teleport_range.get("checkbox_states", {})

            # Embedded ability effect
            add_ability_check = self.get_widget_by_name("trap_add_ability_check")
            ability_selector = self.get_widget_by_name("trap_ability_selector")
            if add_ability_check and ability_selector:
                ability_data = trap_effects.get("embeddedAbility", {})
                enabled = ability_data.get("enabled", False)
                add_ability_check.setChecked(enabled)
                ability_selector.setVisible(enabled)

                # Set ability data if available
                if enabled and "abilityData" in ability_data:
                    ability_selector.set_ability(ability_data["abilityData"])
            
            # Populate trap properties
            duration_spin = self.get_widget_by_name("trap_duration_spin")
            if duration_spin:
                duration = data.get("trapDuration", 0)
                duration_spin.setValue(duration)
            
            trigger_limit_spin = self.get_widget_by_name("trap_trigger_limit_spin")
            if trigger_limit_spin:
                trigger_limit = data.get("trapTriggerLimit", 0)
                trigger_limit_spin.setValue(trigger_limit)
            
            # Populate visibility options
            visibility = data.get("trapVisibility", {})
            
            visible_enemies_check = self.get_widget_by_name("trap_visible_enemies_check")
            if visible_enemies_check:
                visible_enemies_check.setChecked(visibility.get("enemies", False))
            
            visible_allies_check = self.get_widget_by_name("trap_visible_allies_check")
            if visible_allies_check:
                visible_allies_check.setChecked(visibility.get("allies", True))
            
            self.log_debug("Trap tile data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating trap tile data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}
            
            # Collect trap effects
            trap_effects = {}
            
            capture_check = self.get_widget_by_name("trap_capture_check")
            if capture_check and capture_check.isChecked():
                trap_effects["capture"] = True
            
            # Immobilize effect
            immobilize_check = self.get_widget_by_name("trap_immobilize_check")
            immobilize_spin = self.get_widget_by_name("trap_immobilize_spin")
            if immobilize_check and immobilize_check.isChecked():
                trap_effects["immobilize"] = {
                    "enabled": True,
                    "duration": immobilize_spin.value() if immobilize_spin else 2
                }
            
            # Teleport effect
            teleport_check = self.get_widget_by_name("trap_teleport_check")
            if teleport_check and teleport_check.isChecked():
                trap_effects["teleport"] = {
                    "enabled": True,
                    "range": {
                        "pattern": self.teleport_pattern,
                        "piece_position": self.teleport_piece_pos,
                        "checkbox_states": self.teleport_checkbox_states
                    }
                }

            # Embedded ability effect
            add_ability_check = self.get_widget_by_name("trap_add_ability_check")
            ability_selector = self.get_widget_by_name("trap_ability_selector")
            if add_ability_check and add_ability_check.isChecked() and ability_selector:
                ability_data = ability_selector.get_ability()
                if ability_data:
                    trap_effects["embeddedAbility"] = {
                        "enabled": True,
                        "abilityData": ability_data
                    }
            
            if trap_effects:
                data["trapEffects"] = trap_effects
            
            # Collect trap properties
            duration_spin = self.get_widget_by_name("trap_duration_spin")
            if duration_spin:
                data["trapDuration"] = duration_spin.value()
            
            trigger_limit_spin = self.get_widget_by_name("trap_trigger_limit_spin")
            if trigger_limit_spin:
                data["trapTriggerLimit"] = trigger_limit_spin.value()
            
            # Collect visibility options
            visibility = {}
            
            visible_enemies_check = self.get_widget_by_name("trap_visible_enemies_check")
            if visible_enemies_check:
                visibility["enemies"] = visible_enemies_check.isChecked()
            
            visible_allies_check = self.get_widget_by_name("trap_visible_allies_check")
            if visible_allies_check:
                visibility["allies"] = visible_allies_check.isChecked()
            
            if visibility:
                data["trapVisibility"] = visibility
            
            self.log_debug(f"Collected trap tile data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting trap tile data: {e}")
            return {}
